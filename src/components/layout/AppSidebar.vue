<template>
  <Sidebar variant="inset">
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <router-link to="/" class="flex items-center gap-2">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Building2 class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">玻璃ERP系统</span>
                <span class="truncate text-xs">Glass Manufacturing</span>
              </div>
            </router-link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>
    
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>主要功能</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="item in filteredMenuItems" :key="item.title">
              <SidebarMenuButton as-child :tooltip="item.title">
                <router-link :to="item.url" class="flex items-center gap-2">
                  <component :is="item.icon" class="size-4" />
                  <span>{{ item.title }}</span>
                </router-link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>

      <!-- 管理功能 - 仅管理员可见 -->
      <SidebarGroup v-if="hasAdminAccess">
        <SidebarGroupLabel>系统管理</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="item in adminMenuItems" :key="item.title">
              <SidebarMenuButton as-child :tooltip="item.title">
                <router-link :to="item.url" class="flex items-center gap-2">
                  <component :is="item.icon" class="size-4" />
                  <span>{{ item.title }}</span>
                </router-link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>

    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="currentUser?.avatar || ''" :alt="currentUser?.name || ''" />
              <AvatarFallback class="rounded-lg">
                {{ currentUser?.name?.charAt(0) || 'U' }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ currentUser?.name || '未登录' }}</span>
              <span class="truncate text-xs">{{ currentUser?.department || '' }}</span>
            </div>
            <ChevronsUpDown class="ml-auto size-4" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
  </Sidebar>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Building2,
  LayoutDashboard,
  Users,
  Package,
  Factory,
  ShoppingCart,
  ClipboardCheck,
  Settings,
  UserCog,
  Database,
  ChevronsUpDown
} from 'lucide-vue-next'

interface MenuItem {
  title: string
  url: string
  icon: unknown
  roles: string[]
}

const userStore = useUserStore()

// 主要菜单项配置
const mainMenuItems: MenuItem[] = [
  {
    title: '仪表盘',
    url: '/dashboard',
    icon: LayoutDashboard,
    roles: ['admin', 'enterprise_manager', 'sales_engineer', 'production_scheduler', 'quality_engineer', 'warehouse_manager', 'procurement_manager', 'finance_manager', 'production_supervisor']
  },
  {
    title: '客户关系',
    url: '/crm',
    icon: Users,
    roles: ['admin', 'enterprise_manager', 'sales_engineer', 'finance_manager']
  },
  {
    title: '库存管理',
    url: '/inventory',
    icon: Package,
    roles: ['admin', 'enterprise_manager', 'warehouse_manager', 'procurement_manager', 'production_scheduler']
  },
  {
    title: '生产执行',
    url: '/mes',
    icon: Factory,
    roles: ['admin', 'enterprise_manager', 'production_scheduler', 'production_supervisor', 'cutting_operator', 'tempering_operator', 'laminating_operator']
  },
  {
    title: '采购管理',
    url: '/procurement',
    icon: ShoppingCart,
    roles: ['admin', 'enterprise_manager', 'procurement_manager', 'warehouse_manager']
  },
  {
    title: '质量管理',
    url: '/quality',
    icon: ClipboardCheck,
    roles: ['admin', 'enterprise_manager', 'quality_engineer', 'production_supervisor']
  }
]

// 管理员菜单项
const adminMenuItems: MenuItem[] = [
  {
    title: '用户管理',
    url: '/admin/users',
    icon: UserCog,
    roles: ['admin']
  },
  {
    title: '元数据管理',
    url: '/admin/metadata',
    icon: Database,
    roles: ['admin']
  },
  {
    title: '系统设置',
    url: '/admin/settings',
    icon: Settings,
    roles: ['admin']
  }
]

// 根据用户角色过滤菜单项
const filteredMenuItems = computed(() => {
  if (!userStore.currentUser) return []

  return mainMenuItems.filter(item =>
    item.roles.some(role => userStore.currentUser?.roles.includes(role))
  )
})

// 检查是否有管理员权限
const hasAdminAccess = computed(() => {
  return userStore.isAdmin
})

// 组件挂载时不需要额外操作，用户状态由AppHeader统一管理
</script>