/**
 * 玻璃行业物料变体数据模型
 * 基于 ODOO 数据结构，结合玻璃深加工行业特性
 */

// 基础类型定义
export interface BaseAttribute {
  id: string;
  name: string; // "厚度"、"颜色"、"等级"
  type: 'number' | 'text' | 'select';
  unit?: string; // "mm"
  options?: string[]; // ["透明", "茶色", "蓝色"]
  isRequired: boolean;
}

export interface VariantAttribute {
  id: string;
  name: string; // "宽度"、"高度"、"长度"
  type: 'number';
  unit: string; // "mm"
  minValue?: number;
  maxValue?: number;
  isRequired: boolean;
}

export interface AttributeValue {
  attributeId: string;
  attributeName: string;
  value: string | number;
  unit?: string;
}

// 物料分类
export interface MaterialCategory {
  id: string;
  name: string;
  code: string;
  parentId?: string;
  description?: string;
  isActive: boolean;
}

// 供应商信息
export interface Supplier {
  id: string;
  name: string;
  code: string;
  contactInfo: {
    phone?: string;
    email?: string;
    address?: string;
  };
  isActive: boolean;
}

// 物料模板接口
export interface MaterialTemplate {
  id: string;
  name: string; // "浮法玻璃"
  code: string; // "GLASS_FLOAT"
  category: MaterialCategory;
  materialType: 'raw_glass' | 'profile' | 'hardware' | 'sealant' | 'chemical';
  baseAttributes: BaseAttribute[]; // 基础规格：厚度、颜色、等级
  variantAttributes: VariantAttribute[]; // 变体规格：宽度、高度、长度
  variants: MaterialVariant[]; // 具体的变体列表
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 物料变体基础接口
export interface MaterialVariant {
  id: string;
  templateId: string;
  sku: string; // "GLASS_FLOAT_6MM_CLEAR_3300x2140"
  displayName: string; // "6mm透明浮法玻璃 3300x2140mm"
  baseAttributeValues: AttributeValue[]; // 厚度:6mm, 颜色:透明, 等级:优等品
  variantAttributeValues: AttributeValue[]; // 宽度:3300mm, 高度:2140mm
  cost: number;
  weight: number;
  area?: number; // 对于玻璃，面积是重要属性
  supplier: Supplier;
  leadTime: number; // 交货周期（天）
  isActive: boolean;
  stockQuantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  createdAt: string;
  updatedAt: string;
}

// 玻璃原片物料变体接口
export interface GlassSheetVariant extends MaterialVariant {
  thickness: number; // mm
  color: string; // "透明"、"茶色"、"蓝色"
  grade: string; // "优等品"、"一等品"
  width: number; // mm
  height: number; // mm
  glassType: 'float' | 'ultra_clear' | 'tinted' | 'reflective';
  surfaceQuality: 'standard' | 'premium';
}

// 型材物料变体接口
export interface ProfileVariant extends MaterialVariant {
  crossSection: string; // "50x30mm"
  material: string; // "铝合金"、"塑钢"
  color: string; // "银白色"、"香槟色"
  length: number; // mm
  wallThickness: number; // mm
  surfaceTreatment: 'anodized' | 'powder_coated' | 'electrophoresis';
}

// 库存位置
export interface StockLocation {
  id: string;
  name: string;
  code: string;
  type: 'warehouse' | 'production' | 'quality' | 'shipping';
  parentId?: string;
  isActive: boolean;
}

// 物料变体库存管理
export interface MaterialVariantStock {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  locationId: string;
  location: StockLocation;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  unitCost: number;
  totalValue: number;
  lastMovementDate: string;
  reorderPoint: number; // 物料变体级别的安全库存
  maxStock: number; // 物料变体级别的最大库存
  lotNumbers: string[]; // 批次号
  expiryDate?: string; // 过期日期（如适用）
}

// 物料变体库存移动记录
export interface MaterialVariantStockMove {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  quantity: number;
  sourceLocationId: string;
  destinationLocationId: string;
  moveType: 'receipt' | 'delivery' | 'internal' | 'adjustment' | 'scrap';
  reference: string;
  state: 'draft' | 'confirmed' | 'done' | 'cancelled';
  unitCost: number;
  totalCost: number;
  scheduledDate: string;
  effectiveDate?: string;
  relatedOrderId?: string;
  cuttingPlanId?: string; // 关联的切割计划
  createdAt: string;
  updatedAt: string;
}

// 余料物料变体库存
export interface WasteMaterialVariantStock {
  id: string;
  originalMaterialVariantId: string;
  originalMaterialVariant: MaterialVariant;
  currentDimensions: Dimensions;
  remainingArea: number; // 对于玻璃
  remainingLength: number; // 对于型材
  quality: 'good' | 'damaged' | 'unusable';
  locationId: string;
  createdDate: string;
  lastUsedDate?: string;
  potentialUses: PotentialUse[]; // 可能的用途匹配
}

// 尺寸信息
export interface Dimensions {
  width?: number; // mm
  height?: number; // mm
  length?: number; // mm
  thickness?: number; // mm
}

// 潜在用途匹配
export interface PotentialUse {
  orderItemId: string;
  requiredDimensions: Dimensions;
  matchScore: number; // 匹配度评分 (0-100)
  wasteAfterUse: number;
}

// 物料变体补货规则
export interface MaterialVariantReorderRule {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  minQuantity: number;
  maxQuantity: number;
  reorderQuantity: number;
  leadTime: number;
  supplierId: string;
  isActive: boolean;
  seasonalAdjustment?: SeasonalAdjustment[];
  createdAt: string;
  updatedAt: string;
}

// 季节性调整
export interface SeasonalAdjustment {
  month: number; // 1-12
  adjustmentFactor: number; // 季节性调整系数
}

// 物料变体库存预警
export interface MaterialVariantStockAlert {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  alertType: 'low_stock' | 'overstock' | 'no_stock' | 'expiring' | 'slow_moving';
  currentQuantity: number;
  thresholdQuantity: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  createdDate: string;
  isResolved: boolean;
  resolvedDate?: string;
}

// 物料变体需求预测
export interface MaterialVariantDemandForecast {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  forecastPeriod: 'weekly' | 'monthly' | 'quarterly';
  startDate: string;
  endDate: string;
  predictedDemand: number;
  confidence: number; // 预测置信度 (0-100)
  historicalData: HistoricalDemandData[];
  createdAt: string;
}

// 历史需求数据
export interface HistoricalDemandData {
  period: string; // 时间段
  actualDemand: number;
  factors: string[]; // 影响因素
}

// 物料变体优化相关类型
export interface MaterialVariantOptimizationInput {
  requiredPieces: RequiredPiece[]; // 客户订单需要的玻璃片
  availableMaterialVariants: GlassSheetVariant[]; // 可用的玻璃原片物料变体
  optimizationGoal: 'minimize_waste' | 'minimize_cost' | 'minimize_variants' | 'balanced';
  constraints: OptimizationConstraints;
}

// 需求片段
export interface RequiredPiece {
  id: string;
  width: number;
  height: number;
  thickness: number;
  color: string;
  grade: string;
  quantity: number;
  orderItemId: string;
  allowRotation: boolean;
}

// 优化约束条件
export interface OptimizationConstraints {
  maxVariantsToUse: number; // 最多使用的变体数量
  minUtilizationRate: number; // 最低利用率要求
  preferredVariants: string[]; // 优先使用的变体ID
  excludedVariants: string[]; // 排除的变体ID
}

// 物料变体优化结果
export interface MaterialVariantOptimizationResult {
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  overallUtilizationRate: number;
  variantCuttingPlans: VariantCuttingPlan[];
  alternativeOptions: AlternativeOption[];
}

// 选中的物料变体
export interface SelectedMaterialVariant {
  materialVariantId: string;
  materialVariant: GlassSheetVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteArea: number;
}

// 变体切割计划
export interface VariantCuttingPlan {
  materialVariantId: string;
  sheetIndex: number; // 第几张原片
  pieces: CuttingPiece[];
  wasteAreas: WasteArea[];
}

// 切割片段
export interface CuttingPiece {
  pieceId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  orderItemId: string;
  requiredPieceId: string;
}

// 废料区域
export interface WasteArea {
  x: number;
  y: number;
  width: number;
  height: number;
  area: number;
  isReusable: boolean; // 是否可作为余料重复利用
}

// 替代方案
export interface AlternativeOption {
  id: string;
  description: string;
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  utilizationRate: number;
  pros: string[];
  cons: string[];
}

// 型材切割优化相关类型
export interface ProfileMaterialOptimizationInput {
  requiredLengths: RequiredLength[];
  availableProfileMaterialVariants: ProfileVariant[];
  optimizationGoal: 'minimize_waste' | 'minimize_cost';
}

// 需求长度
export interface RequiredLength {
  id: string;
  length: number;
  quantity: number;
  orderItemId: string;
  profileTemplateId: string;
}

// 型材物料优化结果
export interface ProfileMaterialOptimizationResult {
  selectedProfileMaterialVariants: SelectedProfileMaterialVariant[];
  cuttingPlans: ProfileCuttingPlan[];
  totalWasteLength: number;
  utilizationRate: number;
}

// 选中的型材物料变体
export interface SelectedProfileMaterialVariant {
  materialVariantId: string;
  materialVariant: ProfileVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteLength: number;
}

// 型材切割计划
export interface ProfileCuttingPlan {
  materialVariantId: string;
  profileIndex: number;
  cuts: ProfileCut[];
  wasteLength: number;
}

// 型材切割
export interface ProfileCut {
  cutId: string;
  startPosition: number;
  length: number;
  orderItemId: string;
  requiredLengthId: string;
}

// 物料变体组合方案
export interface MaterialVariantCombination {
  id: string;
  materialVariants: MaterialVariant[];
  requirements: RequiredPiece[] | RequiredLength[];
  estimatedCost: number;
  estimatedWaste: number;
  utilizationRate: number;
  complexity: number; // 方案复杂度评分
}

// 物料变体评估结果
export interface MaterialVariantEvaluationResult {
  combinationId: string;
  totalCost: number;
  totalWaste: number;
  utilizationRate: number;
  materialVariantCount: number;
  score: number; // 综合评分
  cuttingPlans: (VariantCuttingPlan | ProfileCuttingPlan)[];
}

// 物料变体使用统计
export interface MaterialVariantUsageStats {
  materialVariantId: string;
  materialVariant: MaterialVariant;
  totalUsed: number;
  totalWaste: number;
  averageUtilizationRate: number;
  usageFrequency: number;
  lastUsedDate: string;
  costEfficiency: number; // 成本效率评分
}