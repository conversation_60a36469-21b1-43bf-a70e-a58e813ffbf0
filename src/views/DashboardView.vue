<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">仪表盘</h1>
    </div>
    
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">今日订单</CardTitle>
          <ShoppingCart class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.recentOrders.length }}</div>
          <p class="text-xs text-muted-foreground">
            总订单数量
          </p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">生产进度</CardTitle>
          <Factory class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.inProductionOrders.length }}</div>
          <p class="text-xs text-muted-foreground">
            生产中订单
          </p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">库存预警</CardTitle>
          <AlertTriangle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ businessStore.lowStockCount }}</div>
          <p class="text-xs text-muted-foreground">
            需要补货的物料
          </p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">质量合格率</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">98.5%</div>
          <p class="text-xs text-muted-foreground">
            +0.2% 较昨日
          </p>
        </CardContent>
      </Card>
    </div>
    
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
      <Card class="col-span-4">
        <CardHeader>
          <CardTitle>生产概览</CardTitle>
        </CardHeader>
        <CardContent class="pl-2">
          <div class="h-[200px] flex items-center justify-center text-muted-foreground">
            生产数据图表区域
          </div>
        </CardContent>
      </Card>
      
      <Card class="col-span-3">
        <CardHeader>
          <CardTitle>最近活动</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="ml-4 space-y-1">
                <p class="text-sm font-medium leading-none">
                  订单 #2024001 已完成生产
                </p>
                <p class="text-sm text-muted-foreground">
                  2小时前
                </p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="ml-4 space-y-1">
                <p class="text-sm font-medium leading-none">
                  新客户注册：建筑公司B
                </p>
                <p class="text-sm text-muted-foreground">
                  4小时前
                </p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="ml-4 space-y-1">
                <p class="text-sm font-medium leading-none">
                  库存补货：6mm透明玻璃
                </p>
                <p class="text-sm text-muted-foreground">
                  6小时前
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useBusinessStore } from '@/stores/business'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ShoppingCart, Factory, AlertTriangle, CheckCircle } from 'lucide-vue-next'

const businessStore = useBusinessStore()

onMounted(async () => {
  // 初始化业务数据
  await businessStore.initializeBusinessData()
})
</script>