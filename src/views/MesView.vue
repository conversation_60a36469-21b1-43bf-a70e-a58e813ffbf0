<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">生产执行系统</h1>
      <Button>
        <Plus class="mr-2 h-4 w-4" />
        新建生产任务
      </Button>
    </div>
    
    <Card>
      <CardHeader>
        <CardTitle>生产监控</CardTitle>
        <CardDescription>
          实时监控玻璃深加工生产线状态
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="h-[400px] flex items-center justify-center text-muted-foreground">
          生产监控界面区域
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus } from 'lucide-vue-next'
</script>