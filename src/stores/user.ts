/**
 * 用户状态管理Store
 * 实现用户认证、权限管理、用户信息管理等功能
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, Role, LoginRequest, PermissionCheckOptions } from '@/types/user'
import { CommonDataService } from '../../utils/dataService'

export const useUserStore = defineStore('user', () => {
  // 状态定义
  const isAuthenticated = ref<boolean>(false)
  const currentUser = ref<User | null>(null)
  const userRoles = ref<Role[]>([])
  const token = ref<string | null>(null)
  const isLoading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // 计算属性
  const permissions = computed<string[]>(() => {
    if (!userRoles.value.length) return []
    
    // 如果用户有admin角色，返回所有权限
    if (userRoles.value.some(role => role.code === 'admin')) {
      return ['*']
    }
    
    // 合并所有角色的权限
    const allPermissions = userRoles.value.reduce<string[]>((acc, role) => {
      return [...acc, ...role.permissions]
    }, [])
    
    // 去重并返回
    return [...new Set(allPermissions)]
  })

  const userInfo = computed(() => {
    if (!currentUser.value) return null
    
    return {
      id: currentUser.value.id,
      name: currentUser.value.name,
      username: currentUser.value.username,
      email: currentUser.value.email,
      avatar: currentUser.value.avatar,
      department: currentUser.value.department,
      roles: currentUser.value.roles,
      roleNames: userRoles.value.map(role => role.name)
    }
  })

  const isAdmin = computed<boolean>(() => {
    return currentUser.value?.roles.includes('admin') || false
  })

  // Actions
  
  /**
   * 模拟登录功能
   * 在实际项目中，这里会调用真实的登录API
   */
  const login = async (credentials: LoginRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      error.value = null

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 从Mock数据中查找用户
      const usersResponse = await CommonDataService.getUsers()
      if (!usersResponse.success || !usersResponse.data) {
        throw new Error('无法加载用户数据')
      }

      const user = usersResponse.data.find((u: any) => u.username === credentials.username) as any
      if (!user) {
        throw new Error('用户名或密码错误')
      }

      if (!user.isActive) {
        throw new Error('用户账户已被禁用')
      }

      // 加载用户角色信息
      const rolesResponse = await CommonDataService.getRoles()
      if (!rolesResponse.success || !rolesResponse.data) {
        throw new Error('无法加载角色数据')
      }

      const userRoleList = rolesResponse.data.filter((role: any) =>
        user.roles.includes(role.code)
      )

      // 设置用户状态
      currentUser.value = user as User
      userRoles.value = userRoleList as Role[]
      isAuthenticated.value = true
      token.value = `mock_token_${user.id}_${Date.now()}`

      // 持久化到localStorage
      persistAuthState()

      return true
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 登出功能
   */
  const logout = async (): Promise<void> => {
    try {
      isLoading.value = true
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 清除状态
      clearAuthState()
      
      // 清除持久化数据
      clearPersistedAuthState()
      
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 权限检查
   */
  const hasPermission = (
    requiredPermissions: string | string[], 
    options: PermissionCheckOptions = {}
  ): boolean => {
    if (!isAuthenticated.value || !currentUser.value) {
      return false
    }

    // 管理员拥有所有权限
    if (isAdmin.value || permissions.value.includes('*')) {
      return true
    }

    const required = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions]

    if (options.requireAll) {
      // 需要所有权限都满足
      return required.every(permission => permissions.value.includes(permission))
    } else {
      // 满足任一权限即可
      return required.some(permission => permissions.value.includes(permission))
    }
  }

  /**
   * 角色检查
   */
  const hasRole = (requiredRoles: string | string[]): boolean => {
    if (!isAuthenticated.value || !currentUser.value) {
      return false
    }

    const required = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
    return required.some(role => currentUser.value!.roles.includes(role))
  }

  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async (): Promise<boolean> => {
    if (!currentUser.value) return false

    try {
      isLoading.value = true
      
      const userResponse = await CommonDataService.getUserById(currentUser.value.id)
      if (userResponse.success && userResponse.data) {
        currentUser.value = userResponse.data as User
        persistAuthState()
        return true
      }
      
      return false
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刷新用户信息失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 持久化认证状态
   */
  const persistAuthState = (): void => {
    if (typeof window === 'undefined') return

    const authData = {
      isAuthenticated: isAuthenticated.value,
      currentUser: currentUser.value,
      userRoles: userRoles.value,
      token: token.value,
      timestamp: Date.now()
    }

    localStorage.setItem('auth_state', JSON.stringify(authData))
  }

  /**
   * 从本地存储恢复认证状态
   */
  const restoreAuthState = (): boolean => {
    if (typeof window === 'undefined') return false

    try {
      const stored = localStorage.getItem('auth_state')
      if (!stored) return false

      const authData = JSON.parse(stored)
      
      // 检查数据是否过期（24小时）
      const isExpired = Date.now() - authData.timestamp > 24 * 60 * 60 * 1000
      if (isExpired) {
        clearPersistedAuthState()
        return false
      }

      // 恢复状态
      isAuthenticated.value = authData.isAuthenticated
      currentUser.value = authData.currentUser
      userRoles.value = authData.userRoles
      token.value = authData.token

      return true
    } catch (err) {
      console.error('恢复认证状态失败:', err)
      clearPersistedAuthState()
      return false
    }
  }

  /**
   * 清除认证状态
   */
  const clearAuthState = (): void => {
    isAuthenticated.value = false
    currentUser.value = null
    userRoles.value = []
    token.value = null
    error.value = null
  }

  /**
   * 清除持久化的认证状态
   */
  const clearPersistedAuthState = (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem('auth_state')
  }

  /**
   * 清除错误状态
   */
  const clearError = (): void => {
    error.value = null
  }

  return {
    // 状态
    isAuthenticated,
    currentUser,
    userRoles,
    token,
    isLoading,
    error,
    
    // 计算属性
    permissions,
    userInfo,
    isAdmin,
    
    // 方法
    login,
    logout,
    hasPermission,
    hasRole,
    refreshUserInfo,
    restoreAuthState,
    clearError
  }
})
