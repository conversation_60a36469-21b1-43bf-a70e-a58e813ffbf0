# SuperClaude 框架配置 - Glass ERP 原型系统

Glass ERP 原型系统 Vue.js 应用的 SuperClaude 专用配置文件。

## 项目概述
- **技术栈**: Vue 3 + Vite + TypeScript + TailwindCSS + ShadCN 组件库
- **系统架构**: ERP/制造业系统，包含客户管理、库存管理、生产执行、采购管理、质量管理等模块
- **开发状态**: 早期原型阶段
- **测试环境**: 开发服务器通过 `pnpm dev` 启动

## 框架命令

### 构建和开发命令
- `pnpm dev` - 启动开发服务器
- `pnpm build` - 生产环境构建（包含类型检查）
- `pnpm type-check` - TypeScript 类型验证
- `pnpm lint` - ESLint 代码检查（自动修复）
- `pnpm format` - Prettier 代码格式化

### 常用工作流程
1. 代码修改 → `pnpm lint` → `pnpm type-check` → 功能测试
2. 提交前 → `pnpm build` 验证生产环境就绪状态

## 项目结构

### 核心目录
- `src/components/ui/` - ShadCN UI 组件库（完整组件集）
- `src/views/` - 主要应用视图（仪表板、客户管理、库存管理、生产执行等）
- `src/components/layout/` - 布局组件（页头、侧边栏、主布局）
- `public/mock/` - 开发用模拟数据（按业务领域组织）
- `utils/` - 共享工具（API 调用、数据服务、错误处理）

### 技术栈组成
- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite + TypeScript
- **样式方案**: TailwindCSS + ShadCN 组件
- **状态管理**: Pinia 状态库
- **表单验证**: Vee-validate + Zod
- **数据表格**: TanStack Vue Table
- **图标库**: Lucide Vue

## SuperClaude 集成配置

### 自动激活的人格角色
- **前端专家**: 检测到 Vue.js、TailwindCSS、ShadCN 组件 → 自动激活 `--persona-frontend`
- **架构师**: ERP 系统复杂度 → 自动激活 `--persona-architect` 用于系统设计
- **质量保证**: 构建前验证 → 自动激活 `--persona-qa` 用于测试验证

### MCP 服务器偏好配置
- **Magic**: 用于 ShadCN 组件生成和 Vue 组件模式
- **Context7**: 用于 Vue.js 文档和最佳实践
- **Sequential**: 用于复杂的 ERP 业务逻辑分析

### 质量门控
- 任务完成前必须执行 `pnpm type-check`（lint 存在已知问题）
- 部署前使用 `pnpm build` 进行最终验证
- 组件实现后在开发服务器中测试 UI 组件

### 已知问题
- ESLint 配置存在 48 个已知违例（主要是 TypeScript `any` 类型和 Vue 命名问题）
- ShadCN 组件使用单词命名（标准模式，与 Vue ESLint 多词组件名规则冲突）
- 遗留代码包含 `any` 类型，需要渐进式 TypeScript 迁移
- 初期优先关注新代码质量，而非修复现有违例

### 常用开发模式
- 遵循 `src/components/ui/` 中现有的 ShadCN 组件结构
- 所有新 Vue 组件使用 Composition API
- 在 `src/types/` 中维护一致的 TypeScript 接口定义
- 遵循 `src/router/` 中已建立的路由模式
- 使用 `utils/` 目录中现有的工具函数

## 开发工作流程
1. 组件创建 → 使用现有的 ShadCN 模式
2. 视图开发 → 遵循已建立的布局结构
3. 数据处理 → 利用现有的模拟数据和工具函数
4. 功能测试 → 开发服务器验证 + 类型检查
5. 质量保证 → 完成前执行 Lint + 类型检查

## 交流语言

- 永远使用简体中文进行思考和对话

## 文档管理

- 编写 .md 文档时，也要用中文
- 正式文档 → 放在 docs/
- 讨论/方案文档 → 放在 discuss/

## 代码架构规范

### 硬性指标（适配原型开发特性）

#### 文件大小限制
- **Vue 组件文件**: 每个 .vue 文件不超过 500 行（含 template + script + style）
- **TypeScript 文件**: 工具类、服务类文件不超过 300 行
- **配置文件**: 路由、状态管理等配置文件不超过 200 行
- **类型定义文件**: interface/type 定义文件不超过 350 行

#### 目录结构限制
- **组件目录**: 每个组件文件夹中的文件不超过 10 个（含 index、types、hooks 等）
- **视图目录**: 每个业务模块视图文件夹不超过 8 个主要文件
- **工具目录**: utils 每个子目录不超过 8 个相关工具文件

#### 组件复杂度限制
- **UI 组件**: ShadCN 基础组件保持单一职责，复合组件拆分为多个子组件
- **业务组件**: 业务逻辑组件不超过 3 个主要功能点
- **页面组件**: 页面级组件通过组合多个业务组件构建

### 架构设计原则

#### 组件化设计
- **原子化组件**: 基础 UI 组件保持最小功能单元
- **组合式组件**: 业务组件通过组合原子组件构建
- **容器组件**: 页面级组件专注数据流和布局，不包含具体业务逻辑

#### 模块化设计
- **按业务域分离**: CRM、库存、MES、采购、质量等模块相互独立
- **共享服务层**: API 调用、数据转换、工具函数等跨模块共享
- **类型定义隔离**: 每个模块维护独立的 TypeScript 类型定义

#### 响应式设计
- **移动优先**: 所有组件优先考虑移动端适配
- **断点一致性**: 使用统一的 TailwindCSS 断点系统
- **组件自适应**: 每个组件内部处理不同屏幕尺寸的展示逻辑

### 代码质量「坏味道」识别与预防

#### 针对 Vue.js 项目的常见问题

**1. 组件僵化 (Component Rigidity)**
- 问题：组件耦合度过高，修改一个组件影响多个父子组件
- 预防：使用 Props/Emit 明确定义组件接口，避免直接访问父组件状态
- 识别：修改子组件需要同时修改多个父组件

**2. 状态冗余 (State Redundancy)**
- 问题：同样的状态在多个组件中重复定义和维护
- 预防：使用 Pinia store 集中管理共享状态，组件只维护局部状态
- 识别：相同的 reactive/ref 定义出现在多个组件中

**3. 组件循环依赖 (Component Circular Dependency)**
- 问题：组件之间形成相互引用的闭环
- 预防：建立清晰的组件层次结构，使用事件总线或状态管理解耦
- 识别：两个组件互相 import 对方

**4. 样式脆弱性 (Style Fragility)**
- 问题：CSS 样式修改导致其他组件样式异常
- 预防：使用 TailwindCSS 原子类，避免全局样式污染
- 识别：修改一个组件样式影响到无关组件

**5. 业务逻辑晦涩性 (Business Logic Obscurity)**
- 问题：复杂的 ERP 业务逻辑集中在组件中，难以理解和测试
- 预防：将业务逻辑提取到 composables 或 service 层
- 识别：template 中出现复杂的计算逻辑或条件判断

**6. 接口数据泥团 (API Data Clump)**
- 问题：多个组件都在处理相同的 API 数据结构转换
- 预防：定义统一的 TypeScript 接口和数据转换函数
- 识别：相同的数据处理逻辑在多个组件中重复

**7. 组件过度复杂性 (Component Over-Engineering)**
- 问题：简单的展示组件被设计得过于灵活和复杂
- 预防：遵循 YAGNI 原则，只实现当前需要的功能
- 识别：组件有过多的 props、slots 或复杂的条件渲染

### 原型开发特殊考虑

#### 快速迭代优先
- **可读性优先**: 代码优先考虑可读性和可维护性，便于快速调整
- **组件复用**: 优先使用现有 ShadCN 组件，减少重复开发
- **数据模拟**: 使用结构化的 mock 数据，便于后期对接真实 API

#### 渐进增强
- **TypeScript 渐进**: 允许现有代码中的 `any` 类型，新代码严格类型定义
- **功能分层**: 核心功能优先，次要功能后续迭代
- **性能平衡**: 原型阶段优先功能完整性，后续优化性能

### 代码审核检查点

在每次代码提交前，检查以下要点：

1. **文件大小**: 是否超出规定的行数限制
2. **目录结构**: 文件数量是否合理，是否需要进一步拆分
3. **组件职责**: 每个组件是否职责单一，功能明确
4. **类型定义**: 新增代码是否有完整的 TypeScript 类型定义
5. **样式规范**: 是否使用 TailwindCSS 原子类，避免自定义样式
6. **业务逻辑**: 复杂逻辑是否提取到 composables 或 service 层
7. **测试覆盖**: 关键业务逻辑是否可以通过开发服务器验证

### 重构触发条件

当出现以下情况时，应立即考虑重构：

1. 单个文件超过规定行数限制的 20%
2. 修改一个组件需要同时修改 3 个以上相关组件
3. 相同的业务逻辑在 3 个以上组件中重复出现
4. 组件的 props 数量超过 10 个
5. 单个函数的参数超过 5 个
6. 条件嵌套层数超过 3 层
7. 一个 composable 承担超过 2 个不相关的职责

## MOCK 数据管理规范

### 数据存储与组织

#### 目录结构规范
```
public/mock/
├── common/                 # 通用数据
│   ├── users.json         # 用户和角色数据
│   ├── departments.json   # 部门组织架构
│   └── permissions.json   # 权限配置
├── metadata/              # 元数据管理
│   ├── materialCategory.json    # 物料分类
│   ├── materialTemplate.json    # 物料模板
│   ├── materialVariant.json     # 物料变体
│   ├── processRoutes.json       # 工艺路线
│   └── qualityStandards.json    # 质量标准
├── crm/                   # 客户关系管理
│   ├── customers.json     # 客户信息
│   ├── contacts.json      # 联系人信息
│   └── contracts.json     # 合同信息
├── inventory/             # 库存管理
│   ├── materialVariantStock.json    # 物料变体库存
│   ├── stockLocations.json         # 库位信息
│   ├── stockMovements.json         # 出入库记录
│   └── wasteStock.json             # 余料库存
├── mes/                   # 生产执行系统
│   ├── orders.json        # 生产订单
│   ├── workCenters.json   # 工作中心
│   ├── productionPlans.json # 生产计划
│   └── qualityRecords.json  # 质量记录
├── procurement/           # 采购管理
│   ├── suppliers.json     # 供应商信息
│   ├── purchaseOrders.json # 采购订单
│   └── quotations.json    # 询价记录
└── quality/               # 质量管理
    ├── inspectionPlans.json # 检验计划
    ├── defectTypes.json     # 缺陷类型
    └── correctionActions.json # 纠正措施
```

### 数据模型设计原则

#### 1. 物料变体数据结构
基于玻璃深加工行业特性，严格按照 "模板 + 变体" 的管理模式：

```json
// materialTemplate.json - 物料模板定义
{
  "glassMaterialTemplates": [
    {
      "id": "TPL_GLASS_FLOAT",
      "name": "浮法玻璃",
      "code": "GLASS_FLOAT",
      "materialType": "raw_glass",
      "baseAttributes": [
        {"name": "厚度", "type": "select", "options": ["4mm", "5mm", "6mm", "8mm", "10mm", "12mm"]},
        {"name": "颜色", "type": "select", "options": ["透明", "茶色", "蓝色", "绿色", "灰色"]},
        {"name": "等级", "type": "select", "options": ["优等品", "一等品", "合格品"]}
      ],
      "variantAttributes": [
        {"name": "宽度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 3660},
        {"name": "高度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 2440}
      ]
    }
  ],
  "profileMaterialTemplates": [
    {
      "id": "TPL_PROFILE_ALU",
      "name": "铝合金型材",
      "code": "PROFILE_ALU",
      "materialType": "profile",
      "baseAttributes": [
        {"name": "截面", "type": "select", "options": ["50x30mm", "60x40mm", "80x50mm"]},
        {"name": "颜色", "type": "select", "options": ["银白色", "香槟色", "古铜色"]},
        {"name": "壁厚", "type": "select", "options": ["1.2mm", "1.4mm", "1.6mm", "2.0mm"]}
      ],
      "variantAttributes": [
        {"name": "长度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 6000}
      ]
    }
  ]
}
```

```json
// materialVariant.json - 具体物料变体
{
  "glassVariants": [
    {
      "id": "VAR_GLASS_6MM_CLEAR_3300x2140",
      "templateId": "TPL_GLASS_FLOAT",
      "sku": "GLASS_FLOAT_6MM_CLEAR_3300x2140",
      "displayName": "6mm透明浮法玻璃 3300x2140mm",
      "baseAttributeValues": [
        {"attributeName": "厚度", "value": "6mm"},
        {"attributeName": "颜色", "value": "透明"},
        {"attributeName": "等级", "value": "优等品"}
      ],
      "variantAttributeValues": [
        {"attributeName": "宽度", "value": 3300, "unit": "mm"},
        {"attributeName": "高度", "value": 2140, "unit": "mm"}
      ],
      "cost": 85.50,
      "weight": 33.8,
      "area": 7.062,
      "supplier": "guardian_glass",
      "leadTime": 7,
      "isActive": true
    }
  ],
  "profileVariants": [
    {
      "id": "VAR_PROFILE_50x30_SILVER_6000",
      "templateId": "TPL_PROFILE_ALU",
      "sku": "PROFILE_ALU_50x30_SILVER_6000",
      "displayName": "50x30mm银白色铝型材 6000mm长",
      "baseAttributeValues": [
        {"attributeName": "截面", "value": "50x30mm"},
        {"attributeName": "颜色", "value": "银白色"},
        {"attributeName": "壁厚", "value": "1.4mm"}
      ],
      "variantAttributeValues": [
        {"attributeName": "长度", "value": 6000, "unit": "mm"}
      ],
      "cost": 28.50,
      "weight": 2.85,
      "supplier": "ykk_profiles",
      "leadTime": 14,
      "isActive": true
    }
  ]
}
```

#### 2. MTO 业务流程数据
展现 Make-to-Order 模式的完整业务链条：

```json
// orders.json - 客户订单结构
{
  "customerOrders": [
    {
      "id": "ORD_2024_001",
      "customerName": "万科地产",
      "projectName": "万科时代中心A栋",
      "orderDate": "2024-08-01",
      "deliveryDate": "2024-09-15",
      "status": "in_production",
      "orderItems": [
        {
          "id": "ITEM_001",
          "productType": "curtain_wall",
          "specifications": {
            "width": 1500,
            "height": 2100,
            "thickness": 6,
            "glassType": "low_e",
            "edgeWork": "polished",
            "specialProcessing": ["tempering", "coating"]
          },
          "quantity": 85,
          "unitPrice": 450.00,
          "materialRequirements": [
            {
              "materialVariantId": "VAR_GLASS_6MM_CLEAR_3300x2140",
              "requiredQuantity": 35,
              "optimizationResult": {
                "utilizationRate": 0.87,
                "wasteArea": 2.3
              }
            }
          ]
        }
      ],
      "totalValue": 38250.00,
      "specialRequirements": "防火等级A级，符合GB15763标准"
    }
  ]
}
```

#### 3. 物料变体库存数据
支持精细化的库存管理：

```json
// materialVariantStock.json - 物料变体库存
{
  "materialVariantStocks": [
    {
      "id": "STOCK_001",
      "materialVariantId": "VAR_GLASS_6MM_CLEAR_3300x2140",
      "locationId": "LOC_MAIN_A01",
      "quantity": 120,
      "reservedQuantity": 25,
      "availableQuantity": 95,
      "unitCost": 85.50,
      "totalValue": 10260.00,
      "lastMovementDate": "2024-08-05",
      "reorderPoint": 20,
      "maxStock": 200,
      "lotNumbers": ["LOT_240801_001", "LOT_240803_002"],
      "qualityStatus": "qualified"
    }
  ],
  "wasteStock": [
    {
      "id": "WASTE_001",
      "originalMaterialVariantId": "VAR_GLASS_6MM_CLEAR_3300x2140",
      "currentDimensions": {"width": 1200, "height": 800},
      "remainingArea": 0.96,
      "quality": "good",
      "locationId": "LOC_WASTE_B01",
      "createdDate": "2024-08-03",
      "potentialUses": [
        {
          "orderItemId": "ITEM_002",
          "requiredDimensions": {"width": 1000, "height": 700},
          "matchScore": 0.85,
          "wasteAfterUse": 0.26
        }
      ]
    }
  ]
}
```

### 数据质量标准

#### 1. 数据一致性要求
- **关联完整性**: 所有外键引用必须有效，如 `materialVariantId` 必须在 materialVariant.json 中存在
- **枚举值统一**: 所有枚举类型值（如 `productType`, `glassType`）必须在元数据中预定义
- **单位统一**: 长度单位统一使用 mm，面积使用 m²，重量使用 kg
- **日期格式**: 统一使用 ISO 8601 格式 "YYYY-MM-DD" 或 "YYYY-MM-DDTHH:mm:ss"

#### 2. 业务逻辑约束
- **库存数值**: `availableQuantity = quantity - reservedQuantity`，且所有数值 ≥ 0
- **物料变体**: 每个变体的 SKU 必须唯一，displayName 要体现所有关键属性
- **切割优化**: 原片尺寸必须大于所需切割尺寸，利用率应在 0.6-0.95 之间
- **工艺路线**: 每个加工步骤必须有明确的前后依赖关系

#### 3. 玻璃行业特征数据
- **原片规格**: 使用行业标准规格（如 3300x2140, 3660x2440），符合实际供应商产品
- **厚度系列**: 遵循玻璃行业标准厚度（4mm, 5mm, 6mm, 8mm, 10mm, 12mm, 15mm, 19mm）
- **型材截面**: 使用真实的铝合金型材截面规格（如 50x30, 60x40, 80x50）
- **工艺参数**: 钢化温度、夹胶厚度等符合实际工艺要求

### 数据加载与缓存策略

#### 1. API 封装规范
```typescript
// utils/mockApi.ts
interface MockDataLoader {
  loadMaterialVariants(filters?: MaterialVariantFilter): Promise<MaterialVariant[]>;
  loadCustomerOrders(customerId?: string): Promise<CustomerOrder[]>;
  loadStockByLocation(locationId: string): Promise<MaterialVariantStock[]>;
  loadWasteStock(materialType?: string): Promise<WasteMaterialStock[]>;
}

// 使用示例
const api = new MockApiClient();
const glassVariants = await api.loadMaterialVariants({
  materialType: 'raw_glass',
  thickness: '6mm',
  minArea: 5.0
});
```

#### 2. 数据验证机制
```typescript
// utils/dataValidator.ts
interface DataValidationRule {
  field: string;
  type: 'required' | 'range' | 'enum' | 'reference';
  constraint: any;
  message: string;
}

const materialVariantRules: DataValidationRule[] = [
  {field: 'sku', type: 'required', constraint: null, message: 'SKU是必填项'},
  {field: 'cost', type: 'range', constraint: {min: 0, max: 10000}, message: '成本必须在合理范围内'},
  {field: 'materialType', type: 'enum', constraint: ['raw_glass', 'profile', 'hardware'], message: '物料类型无效'}
];
```

#### 3. 缓存与性能优化
- **本地缓存**: 使用 SessionStorage 缓存频繁访问的数据（物料变体、工艺路线）
- **懒加载**: 大型数据集采用分页加载，避免一次性加载过多数据
- **增量更新**: 库存数据支持增量更新，减少网络传输
- **预加载**: 根据用户角色预加载相关业务数据

### 测试与验证

#### 1. 数据完整性测试
```typescript
describe('Mock Data Integrity', () => {
  test('所有物料变体引用的模板都存在', async () => {
    const variants = await loadMockData('/metadata/materialVariant');
    const templates = await loadMockData('/metadata/materialTemplate');
    const templateIds = new Set(templates.data.map(t => t.id));
    
    variants.data.forEach(variant => {
      expect(templateIds.has(variant.templateId)).toBe(true);
    });
  });
  
  test('库存数据数值逻辑正确', async () => {
    const stocks = await loadMockData('/inventory/materialVariantStock');
    
    stocks.data.forEach(stock => {
      expect(stock.availableQuantity).toBe(stock.quantity - stock.reservedQuantity);
      expect(stock.totalValue).toBe(stock.quantity * stock.unitCost);
    });
  });
});
```

#### 2. 业务场景验证
```typescript
describe('Business Scenario Validation', () => {
  test('MTO订单流程数据完整', async () => {
    const orders = await loadMockData('/mes/orders');
    
    orders.data.forEach(order => {
      expect(order.orderItems.length).toBeGreaterThan(0);
      order.orderItems.forEach(item => {
        expect(item.materialRequirements.length).toBeGreaterThan(0);
        expect(item.specifications).toBeDefined();
      });
    });
  });
});
```

### 数据维护工作流

#### 1. 数据更新流程
1. **需求分析** → 确定新增或修改的数据需求
2. **数据设计** → 按照规范设计数据结构
3. **数据生成** → 创建符合业务逻辑的示例数据
4. **一致性检查** → 验证数据间的引用关系
5. **测试验证** → 运行完整性测试
6. **集成测试** → 在组件中验证数据使用

#### 2. 数据质量监控
- **定期检查**: 每次重大功能开发后检查数据完整性
- **业务验证**: 与业务专家确认数据的真实性和合理性
