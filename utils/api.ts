/**
 * API 工具类 - 统一的数据加载接口和错误处理机制
 * 支持 Mock 数据和真实 API 的无缝切换
 */

// API 响应接口定义
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message: string;
  code?: string;
  timestamp?: string;
}

// API 错误接口定义
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// API 配置接口
export interface ApiConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  useMock?: boolean;
}

// 默认配置
const DEFAULT_CONFIG: ApiConfig = {
  baseURL: process.env.NODE_ENV === 'development' ? '/mock' : '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  useMock: process.env.NODE_ENV === 'development',
};

// 全局配置
let globalConfig: ApiConfig = { ...DEFAULT_CONFIG };

/**
 * 设置全局 API 配置
 */
export function setApiConfig(config: Partial<ApiConfig>): void {
  globalConfig = { ...globalConfig, ...config };
}

/**
 * 获取当前 API 配置
 */
export function getApiConfig(): ApiConfig {
  return { ...globalConfig };
}

/**
 * 创建 API 错误对象
 */
function createApiError(message: string, code: string = 'API_ERROR', details?: any): ApiError {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 统一的 fetch 封装函数
 * 支持自动错误处理、超时控制、请求重试等功能
 */
export async function apiFetch<T = any>(
  endpoint: string,
  options: RequestInit & { 
    config?: Partial<ApiConfig>;
    retries?: number;
    retryDelay?: number;
  } = {}
): Promise<ApiResponse<T>> {
  const { config = {}, retries = 0, retryDelay = 1000, ...fetchOptions } = options;
  const finalConfig = { ...globalConfig, ...config };
  
  // 构建完整 URL
  const baseURL = finalConfig.baseURL || '';
  const url = endpoint.startsWith('http') ? endpoint : `${baseURL}${endpoint}`;
  
  // 设置默认请求头
  const headers = {
    ...finalConfig.headers,
    ...fetchOptions.headers,
  };

  // 创建 AbortController 用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, finalConfig.timeout || 10000);

  try {
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // 检查 HTTP 状态
    if (!response.ok) {
      const errorText = await response.text();
      let errorData: any;
      
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      throw createApiError(
        errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        `HTTP_${response.status}`,
        errorData
      );
    }

    // 解析响应数据
    const contentType = response.headers.get('content-type');
    let data: any;

    if (contentType?.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    // 如果是 Mock 数据，直接返回
    if (finalConfig.useMock && typeof data === 'object' && data.success !== undefined) {
      return data as ApiResponse<T>;
    }

    // 包装成标准响应格式
    return {
      data: data as T,
      success: true,
      message: 'Request successful',
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    clearTimeout(timeoutId);

    // 如果是 AbortError（超时），创建超时错误
    if (error instanceof Error && error.name === 'AbortError') {
      const timeoutError = createApiError(
        `Request timeout after ${finalConfig.timeout}ms`,
        'TIMEOUT_ERROR'
      );
      
      // 重试逻辑
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return apiFetch<T>(endpoint, { 
          ...options, 
          retries: retries - 1,
          retryDelay: retryDelay * 1.5 // 指数退避
        });
      }
      
      throw timeoutError;
    }

    // 如果是网络错误，尝试重试
    if (error instanceof TypeError && error.message.includes('fetch')) {
      const networkError = createApiError(
        'Network error - please check your connection',
        'NETWORK_ERROR',
        error
      );
      
      if (retries > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return apiFetch<T>(endpoint, { 
          ...options, 
          retries: retries - 1,
          retryDelay: retryDelay * 1.5
        });
      }
      
      throw networkError;
    }

    // 重新抛出 API 错误
    if (typeof error === 'object' && error !== null && 'code' in error) {
      throw error;
    }

    // 包装未知错误
    throw createApiError(
      error instanceof Error ? error.message : 'Unknown error occurred',
      'UNKNOWN_ERROR',
      error
    );
  }
}

/**
 * GET 请求封装
 */
export async function apiGet<T = any>(
  endpoint: string,
  params?: Record<string, any>,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  let url = endpoint;
  
  // 添加查询参数
  if (params && Object.keys(params).length > 0) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    url += `?${searchParams.toString()}`;
  }

  return apiFetch<T>(url, {
    ...options,
    method: 'GET',
  });
}

/**
 * POST 请求封装
 */
export async function apiPost<T = any>(
  endpoint: string,
  data?: any,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * PUT 请求封装
 */
export async function apiPut<T = any>(
  endpoint: string,
  data?: any,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * DELETE 请求封装
 */
export async function apiDelete<T = any>(
  endpoint: string,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T>> {
  return apiFetch<T>(endpoint, {
    ...options,
    method: 'DELETE',
  });
}

/**
 * 批量请求工具
 */
export async function apiBatch<T = any>(
  requests: Array<{
    endpoint: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    params?: Record<string, any>;
  }>,
  options?: Parameters<typeof apiFetch>[1]
): Promise<ApiResponse<T[]>> {
  try {
    const promises = requests.map(request => {
      switch (request.method || 'GET') {
        case 'GET':
          return apiGet(request.endpoint, request.params, options);
        case 'POST':
          return apiPost(request.endpoint, request.data, options);
        case 'PUT':
          return apiPut(request.endpoint, request.data, options);
        case 'DELETE':
          return apiDelete(request.endpoint, options);
        default:
          throw createApiError(`Unsupported method: ${request.method}`, 'INVALID_METHOD');
      }
    });

    const results = await Promise.allSettled(promises);
    const data: T[] = [];
    const errors: ApiError[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        data.push(result.value.data);
      } else {
        errors.push(createApiError(
          `Batch request ${index} failed: ${result.reason.message}`,
          'BATCH_ERROR',
          result.reason
        ));
      }
    });

    return {
      data,
      success: errors.length === 0,
      message: errors.length === 0 
        ? 'All batch requests completed successfully'
        : `${errors.length} of ${requests.length} requests failed`,
      code: errors.length === 0 ? 'BATCH_SUCCESS' : 'BATCH_PARTIAL_FAILURE',
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    throw createApiError(
      'Batch request failed',
      'BATCH_ERROR',
      error
    );
  }
}

/**
 * 错误处理工具函数
 */
export function handleApiError(error: unknown): ApiError {
  if (typeof error === 'object' && error !== null && 'code' in error) {
    return error as ApiError;
  }
  
  return createApiError(
    error instanceof Error ? error.message : 'Unknown error occurred',
    'UNKNOWN_ERROR',
    error
  );
}

/**
 * 检查响应是否成功
 */
export function isApiSuccess<T>(response: ApiResponse<T>): boolean {
  return response.success === true;
}

/**
 * 从响应中提取数据，如果失败则抛出错误
 */
export function extractApiData<T>(response: ApiResponse<T>): T {
  if (!isApiSuccess(response)) {
    throw createApiError(
      response.message || 'API request failed',
      'API_FAILURE',
      response
    );
  }
  return response.data;
}