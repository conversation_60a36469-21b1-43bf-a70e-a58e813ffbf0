/**
 * 错误处理工具 - 统一的错误处理机制
 * 提供错误分类、错误展示、错误上报等功能
 */

import { type ApiError } from './api';

// 错误级别枚举
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  BUSINESS = 'business',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

// 错误处理配置
export interface ErrorHandlerConfig {
  showNotification?: boolean;
  logToConsole?: boolean;
  reportToServer?: boolean;
  retryable?: boolean;
}

// 标准化错误对象
export interface StandardError {
  id: string;
  type: ErrorType;
  level: ErrorLevel;
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  stack?: string;
  context?: Record<string, any>;
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private static config: ErrorHandlerConfig = {
    showNotification: true,
    logToConsole: true,
    reportToServer: false,
    retryable: false,
  };

  /**
   * 设置全局错误处理配置
   */
  static setConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  static getConfig(): ErrorHandlerConfig {
    return { ...this.config };
  }

  /**
   * 将各种错误转换为标准化错误对象
   */
  static normalize(error: unknown, context?: Record<string, any>): StandardError {
    const timestamp = new Date().toISOString();
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 处理 API 错误
    if (this.isApiError(error)) {
      return {
        id,
        type: this.getErrorTypeFromCode(error.code),
        level: this.getErrorLevelFromCode(error.code),
        code: error.code,
        message: error.message,
        details: error.details,
        timestamp: error.timestamp || timestamp,
        context,
      };
    }

    // 处理标准 Error 对象
    if (error instanceof Error) {
      return {
        id,
        type: ErrorType.SYSTEM,
        level: ErrorLevel.ERROR,
        code: error.name || 'UNKNOWN_ERROR',
        message: error.message,
        timestamp,
        stack: error.stack,
        context,
      };
    }

    // 处理字符串错误
    if (typeof error === 'string') {
      return {
        id,
        type: ErrorType.UNKNOWN,
        level: ErrorLevel.ERROR,
        code: 'STRING_ERROR',
        message: error,
        timestamp,
        context,
      };
    }

    // 处理其他类型错误
    return {
      id,
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.ERROR,
      code: 'UNKNOWN_ERROR',
      message: 'An unknown error occurred',
      details: error,
      timestamp,
      context,
    };
  }

  /**
   * 处理错误
   */
  static handle(
    error: unknown,
    context?: Record<string, any>,
    config?: Partial<ErrorHandlerConfig>
  ): StandardError {
    const finalConfig = { ...this.config, ...config };
    const standardError = this.normalize(error, context);

    // 控制台日志
    if (finalConfig.logToConsole) {
      this.logToConsole(standardError);
    }

    // 显示通知（在实际应用中，这里会调用通知组件）
    if (finalConfig.showNotification) {
      this.showNotification(standardError);
    }

    // 上报到服务器（在实际应用中实现）
    if (finalConfig.reportToServer) {
      this.reportToServer(standardError);
    }

    return standardError;
  }

  /**
   * 检查是否为 API 错误
   */
  private static isApiError(error: unknown): error is ApiError {
    return (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      'message' in error &&
      'timestamp' in error
    );
  }

  /**
   * 根据错误代码获取错误类型
   */
  private static getErrorTypeFromCode(code: string): ErrorType {
    if (code.startsWith('HTTP_4')) {
      if (code === 'HTTP_401' || code === 'HTTP_403') {
        return ErrorType.PERMISSION;
      }
      if (code === 'HTTP_400' || code === 'HTTP_422') {
        return ErrorType.VALIDATION;
      }
      return ErrorType.BUSINESS;
    }

    if (code.startsWith('HTTP_5')) {
      return ErrorType.SYSTEM;
    }

    if (code === 'NETWORK_ERROR' || code === 'TIMEOUT_ERROR') {
      return ErrorType.NETWORK;
    }

    return ErrorType.UNKNOWN;
  }

  /**
   * 根据错误代码获取错误级别
   */
  private static getErrorLevelFromCode(code: string): ErrorLevel {
    if (code === 'HTTP_500' || code === 'SYSTEM_ERROR') {
      return ErrorLevel.CRITICAL;
    }

    if (code.startsWith('HTTP_4') || code === 'VALIDATION_ERROR') {
      return ErrorLevel.WARNING;
    }

    if (code === 'NETWORK_ERROR' || code === 'TIMEOUT_ERROR') {
      return ErrorLevel.ERROR;
    }

    return ErrorLevel.ERROR;
  }

  /**
   * 输出到控制台
   */
  private static logToConsole(error: StandardError): void {
    const logMethod = this.getConsoleMethod(error.level);
    const prefix = `[${error.level.toUpperCase()}] ${error.code}:`;
    
    logMethod(prefix, error.message);
    
    if (error.details) {
      console.group('Error Details:');
      console.log(error.details);
      console.groupEnd();
    }

    if (error.context) {
      console.group('Error Context:');
      console.log(error.context);
      console.groupEnd();
    }

    if (error.stack) {
      console.group('Stack Trace:');
      console.log(error.stack);
      console.groupEnd();
    }
  }

  /**
   * 获取对应的控制台方法
   */
  private static getConsoleMethod(level: ErrorLevel): typeof console.log {
    switch (level) {
      case ErrorLevel.INFO:
        return console.info;
      case ErrorLevel.WARNING:
        return console.warn;
      case ErrorLevel.ERROR:
      case ErrorLevel.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  /**
   * 显示通知（占位实现）
   */
  private static showNotification(error: StandardError): void {
    // 在实际应用中，这里会调用 UI 通知组件
    console.log(`[NOTIFICATION] ${error.level}: ${error.message}`);
  }

  /**
   * 上报到服务器（占位实现）
   */
  private static reportToServer(error: StandardError): void {
    // 在实际应用中，这里会发送错误报告到服务器
    console.log(`[REPORT] Error ${error.id} would be reported to server`);
  }

  /**
   * 创建错误处理装饰器
   */
  static createDecorator(
    config?: Partial<ErrorHandlerConfig>
  ) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          const context = {
            className: target.constructor.name,
            methodName: propertyKey,
            arguments: args,
          };
          
          ErrorHandler.handle(error, context, config);
          throw error; // 重新抛出错误，让调用方决定如何处理
        }
      };

      return descriptor;
    };
  }

  /**
   * 创建 Promise 错误处理包装器
   */
  static wrapPromise<T>(
    promise: Promise<T>,
    context?: Record<string, any>,
    config?: Partial<ErrorHandlerConfig>
  ): Promise<T> {
    return promise.catch((error) => {
      this.handle(error, context, config);
      throw error;
    });
  }

  /**
   * 创建函数错误处理包装器
   */
  static wrapFunction<T extends (...args: any[]) => any>(
    fn: T,
    context?: Record<string, any>,
    config?: Partial<ErrorHandlerConfig>
  ): T {
    return ((...args: any[]) => {
      try {
        const result = fn(...args);
        
        // 如果返回 Promise，包装错误处理
        if (result instanceof Promise) {
          return this.wrapPromise(result, context, config);
        }
        
        return result;
      } catch (error) {
        this.handle(error, context, config);
        throw error;
      }
    }) as T;
  }
}

// 便捷的错误处理函数
export const handleError = ErrorHandler.handle.bind(ErrorHandler);
export const normalizeError = ErrorHandler.normalize.bind(ErrorHandler);
export const wrapPromise = ErrorHandler.wrapPromise.bind(ErrorHandler);
export const wrapFunction = ErrorHandler.wrapFunction.bind(ErrorHandler);

// 默认导出
export default ErrorHandler;