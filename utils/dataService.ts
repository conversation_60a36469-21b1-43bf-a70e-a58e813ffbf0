/**
 * 数据服务层 - 统一的数据加载接口
 * 为各业务模块提供标准化的数据访问方法
 */

import { apiGet, apiPost, apiPut, apiDelete, ApiResponse } from './api';

// 通用数据类型定义
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt?: string;
  isActive?: boolean;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 通用数据服务类
 */
export class DataService {
  /**
   * 通用列表查询
   */
  static async getList<T>(
    module: string,
    resource: string,
    params?: PaginationParams & Record<string, any>
  ): Promise<ApiResponse<T[]>> {
    const endpoint = `/${module}/${resource}.json`;
    return apiGet<T[]>(endpoint, params);
  }

  /**
   * 通用详情查询
   */
  static async getById<T>(
    module: string,
    resource: string,
    id: string
  ): Promise<ApiResponse<T>> {
    const endpoint = `/${module}/${resource}/${id}.json`;
    return apiGet<T>(endpoint);
  }

  /**
   * 通用创建
   */
  static async create<T>(
    module: string,
    resource: string,
    data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<ApiResponse<T>> {
    const endpoint = `/${module}/${resource}`;
    return apiPost<T>(endpoint, data);
  }

  /**
   * 通用更新
   */
  static async update<T>(
    module: string,
    resource: string,
    id: string,
    data: Partial<T>
  ): Promise<ApiResponse<T>> {
    const endpoint = `/${module}/${resource}/${id}`;
    return apiPut<T>(endpoint, data);
  }

  /**
   * 通用删除
   */
  static async delete(
    module: string,
    resource: string,
    id: string
  ): Promise<ApiResponse<void>> {
    const endpoint = `/${module}/${resource}/${id}`;
    return apiDelete<void>(endpoint);
  }
}

/**
 * 用户和权限相关数据服务
 */
export class CommonDataService extends DataService {
  // 获取用户列表
  static async getUsers(params?: PaginationParams) {
    return this.getList('common', 'users', params);
  }

  // 获取角色列表
  static async getRoles(params?: PaginationParams) {
    return this.getList('common', 'roles', params);
  }

  // 获取用户详情
  static async getUserById(id: string) {
    return this.getById('common', 'users', id);
  }

  // 获取角色详情
  static async getRoleById(id: string) {
    return this.getById('common', 'roles', id);
  }
}

/**
 * 元数据相关数据服务
 */
export class MetadataService extends DataService {
  // 获取物料分类
  static async getMaterialCategories(params?: PaginationParams) {
    return this.getList('metadata', 'materialCategory', params);
  }

  // 获取物料列表
  static async getMaterials(params?: PaginationParams & { categoryId?: string }) {
    return this.getList('metadata', 'material', params);
  }

  // 获取物料详情
  static async getMaterialById(id: string) {
    return this.getById('metadata', 'material', id);
  }

  // 根据分类获取物料
  static async getMaterialsByCategory(categoryId: string, params?: PaginationParams) {
    return this.getList('metadata', 'material', { ...params, categoryId });
  }
}

/**
 * CRM 相关数据服务
 */
export class CrmDataService extends DataService {
  // 获取客户列表
  static async getCustomers(params?: PaginationParams & { type?: string; industry?: string }) {
    return this.getList('crm', 'customers', params);
  }

  // 获取客户详情
  static async getCustomerById(id: string) {
    return this.getById('crm', 'customers', id);
  }

  // 获取订单列表
  static async getOrders(params?: PaginationParams & { customerId?: string; status?: string }) {
    return this.getList('crm', 'orders', params);
  }

  // 获取订单详情
  static async getOrderById(id: string) {
    return this.getById('crm', 'orders', id);
  }

  // 根据客户获取订单
  static async getOrdersByCustomer(customerId: string, params?: PaginationParams) {
    return this.getList('crm', 'orders', { ...params, customerId });
  }
}

/**
 * 库存相关数据服务
 */
export class InventoryDataService extends DataService {
  // 获取库存列表
  static async getStock(params?: PaginationParams & { materialId?: string; locationId?: string }) {
    return this.getList('inventory', 'stock', params);
  }

  // 获取库存详情
  static async getStockById(id: string) {
    return this.getById('inventory', 'stock', id);
  }

  // 根据物料获取库存
  static async getStockByMaterial(materialId: string, params?: PaginationParams) {
    return this.getList('inventory', 'stock', { ...params, materialId });
  }

  // 获取库存预警列表
  static async getLowStockItems(params?: PaginationParams) {
    return this.getList('inventory', 'stock', { ...params, lowStock: true });
  }
}

/**
 * MES 相关数据服务
 */
export class MesDataService extends DataService {
  // 获取工单列表
  static async getWorkOrders(params?: PaginationParams & { status?: string; priority?: string }) {
    return this.getList('mes', 'workOrders', params);
  }

  // 获取工单详情
  static async getWorkOrderById(id: string) {
    return this.getById('mes', 'workOrders', id);
  }

  // 根据订单获取工单
  static async getWorkOrdersByOrder(orderId: string, params?: PaginationParams) {
    return this.getList('mes', 'workOrders', { ...params, orderId });
  }

  // 获取生产进度
  static async getProductionProgress(workOrderId: string) {
    return this.getById('mes', 'workOrders', workOrderId);
  }
}

/**
 * 采购相关数据服务
 */
export class ProcurementDataService extends DataService {
  // 获取供应商列表
  static async getSuppliers(params?: PaginationParams & { type?: string; category?: string }) {
    return this.getList('procurement', 'suppliers', params);
  }

  // 获取供应商详情
  static async getSupplierById(id: string) {
    return this.getById('procurement', 'suppliers', id);
  }

  // 根据分类获取供应商
  static async getSuppliersByCategory(category: string, params?: PaginationParams) {
    return this.getList('procurement', 'suppliers', { ...params, category });
  }
}

/**
 * 质量管理相关数据服务
 */
export class QualityDataService extends DataService {
  // 获取质检记录列表
  static async getInspections(params?: PaginationParams & { type?: string; status?: string }) {
    return this.getList('quality', 'inspections', params);
  }

  // 获取质检记录详情
  static async getInspectionById(id: string) {
    return this.getById('quality', 'inspections', id);
  }

  // 根据工单获取质检记录
  static async getInspectionsByWorkOrder(workOrderId: string, params?: PaginationParams) {
    return this.getList('quality', 'inspections', { ...params, workOrderId });
  }

  // 根据物料获取质检记录
  static async getInspectionsByMaterial(materialId: string, params?: PaginationParams) {
    return this.getList('quality', 'inspections', { ...params, materialId });
  }
}

/**
 * 统一数据服务导出
 */
export const dataServices = {
  common: CommonDataService,
  metadata: MetadataService,
  crm: CrmDataService,
  inventory: InventoryDataService,
  mes: MesDataService,
  procurement: ProcurementDataService,
  quality: QualityDataService,
};

// 默认导出
export default dataServices;