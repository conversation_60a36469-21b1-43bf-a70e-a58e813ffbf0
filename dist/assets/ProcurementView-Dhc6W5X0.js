import{d as _,c as f,a as n,b as s,w as a,u as t,_ as o,e as i,f as l,g as r,h as u,i as d,o as m}from"./index-xR3mVGqH.js";import{P as c,_ as p}from"./CardDescription.vue_vue_type_script_setup_true_lang-BxK2WQlc.js";const x={class:"space-y-6"},$={class:"flex items-center justify-between"},k=_({__name:"ProcurementView",setup(w){return(g,e)=>(m(),f("div",x,[n("div",$,[e[1]||(e[1]=n("h1",{class:"text-3xl font-bold tracking-tight"},"采购管理",-1)),s(t(o),null,{default:a(()=>[s(t(c),{class:"mr-2 h-4 w-4"}),e[0]||(e[0]=l(" 新建采购单 ",-1))]),_:1,__:[0]})]),s(t(i),null,{default:a(()=>[s(t(r),null,{default:a(()=>[s(t(u),null,{default:a(()=>e[2]||(e[2]=[l("采购订单",-1)])),_:1,__:[2]}),s(t(p),null,{default:a(()=>e[3]||(e[3]=[l(" 管理物料采购和供应商关系 ",-1)])),_:1,__:[3]})]),_:1}),s(t(d),null,{default:a(()=>e[4]||(e[4]=[n("div",{class:"h-[400px] flex items-center justify-center text-muted-foreground"}," 采购数据表格区域 ",-1)])),_:1,__:[4]})]),_:1})]))}});export{k as default};
