const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/CrmView-1fx4fd9T.js","assets/CardDescription.vue_vue_type_script_setup_true_lang-BxK2WQlc.js","assets/InventoryView-NbnKMQZj.js","assets/MesView-Dx3BE192.js","assets/ProcurementView-Dhc6W5X0.js","assets/QualityView-B-Okd0Mt.js"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ns(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Se={},Fn=[],It=()=>{},Hc=()=>!1,go=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),js=e=>e.startsWith("onUpdate:"),Ye=Object.assign,zs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Uc=Object.prototype.hasOwnProperty,we=(e,t)=>Uc.call(e,t),se=Array.isArray,Ln=e=>vo(e)==="[object Map]",il=e=>vo(e)==="[object Set]",ie=e=>typeof e=="function",De=e=>typeof e=="string",Zt=e=>typeof e=="symbol",Oe=e=>e!==null&&typeof e=="object",al=e=>(Oe(e)||ie(e))&&ie(e.then)&&ie(e.catch),ll=Object.prototype.toString,vo=e=>ll.call(e),Wc=e=>vo(e).slice(8,-1),ul=e=>vo(e)==="[object Object]",Vs=e=>De(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ur=Ns(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),yo=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Kc=/-(\w)/g,it=yo(e=>e.replace(Kc,(t,n)=>n?n.toUpperCase():"")),Gc=/\B([A-Z])/g,hn=yo(e=>e.replace(Gc,"-$1").toLowerCase()),bo=yo(e=>e.charAt(0).toUpperCase()+e.slice(1)),cr=yo(e=>e?`on${bo(e)}`:""),un=(e,t)=>!Object.is(e,t),eo=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ds=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},fs=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Oi;const _o=()=>Oi||(Oi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function mn(e){if(se(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=De(r)?Qc(r):mn(r);if(o)for(const s in o)t[s]=o[s]}return t}else if(De(e)||Oe(e))return e}const Yc=/;(?![^(]*\))/g,Xc=/:([^]+)/,Jc=/\/\*[^]*?\*\//g;function Qc(e){const t={};return e.replace(Jc,"").split(Yc).forEach(n=>{if(n){const r=n.split(Xc);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function ye(e){let t="";if(De(e))t=e;else if(se(e))for(let n=0;n<e.length;n++){const r=ye(e[n]);r&&(t+=r+" ")}else if(Oe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function et(e){if(!e)return null;let{class:t,style:n}=e;return t&&!De(t)&&(e.class=ye(t)),n&&(e.style=mn(n)),e}const Zc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ed=Ns(Zc);function cl(e){return!!e||e===""}const dl=e=>!!(e&&e.__v_isRef===!0),Ue=e=>De(e)?e:e==null?"":se(e)||Oe(e)&&(e.toString===ll||!ie(e.toString))?dl(e)?Ue(e.value):JSON.stringify(e,fl,2):String(e),fl=(e,t)=>dl(t)?fl(e,t.value):Ln(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],s)=>(n[jo(r,s)+" =>"]=o,n),{})}:il(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>jo(n))}:Zt(t)?jo(t):Oe(t)&&!se(t)&&!ul(t)?String(t):t,jo=(e,t="")=>{var n;return Zt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let He;class pl{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=He,!t&&He&&(this.index=(He.scopes||(He.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=He;try{return He=this,t()}finally{He=n}}}on(){++this._on===1&&(this.prevScope=He,He=this)}off(){this._on>0&&--this._on===0&&(He=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Hs(e){return new pl(e)}function wo(){return He}function Us(e,t=!1){He&&He.cleanups.push(e)}let ke;const zo=new WeakSet;class hl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,He&&He.active&&He.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,zo.has(this)&&(zo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||gl(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Pi(this),vl(this);const t=ke,n=bt;ke=this,bt=!0;try{return this.fn()}finally{yl(this),ke=t,bt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Gs(t);this.deps=this.depsTail=void 0,Pi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?zo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ps(this)&&this.run()}get dirty(){return ps(this)}}let ml=0,dr,fr;function gl(e,t=!1){if(e.flags|=8,t){e.next=fr,fr=e;return}e.next=dr,dr=e}function Ws(){ml++}function Ks(){if(--ml>0)return;if(fr){let t=fr;for(fr=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;dr;){let t=dr;for(dr=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function vl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function yl(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),Gs(r),td(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function ps(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(bl(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function bl(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===br)||(e.globalVersion=br,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ps(e))))return;e.flags|=2;const t=e.dep,n=ke,r=bt;ke=e,bt=!0;try{vl(e);const o=e.fn(e._value);(t.version===0||un(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{ke=n,bt=r,yl(e),e.flags&=-3}}function Gs(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)Gs(s,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function td(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let bt=!0;const _l=[];function Yt(){_l.push(bt),bt=!1}function Xt(){const e=_l.pop();bt=e===void 0?!0:e}function Pi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ke;ke=void 0;try{t()}finally{ke=n}}}let br=0;class nd{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class xo{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ke||!bt||ke===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ke)n=this.activeLink=new nd(ke,this),ke.deps?(n.prevDep=ke.depsTail,ke.depsTail.nextDep=n,ke.depsTail=n):ke.deps=ke.depsTail=n,wl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=ke.depsTail,n.nextDep=void 0,ke.depsTail.nextDep=n,ke.depsTail=n,ke.deps===n&&(ke.deps=r)}return n}trigger(t){this.version++,br++,this.notify(t)}notify(t){Ws();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ks()}}}function wl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)wl(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const oo=new WeakMap,_n=Symbol(""),hs=Symbol(""),_r=Symbol("");function We(e,t,n){if(bt&&ke){let r=oo.get(e);r||oo.set(e,r=new Map);let o=r.get(n);o||(r.set(n,o=new xo),o.map=r,o.key=n),o.track()}}function Wt(e,t,n,r,o,s){const i=oo.get(e);if(!i){br++;return}const a=l=>{l&&l.trigger()};if(Ws(),t==="clear")i.forEach(a);else{const l=se(e),c=l&&Vs(n);if(l&&n==="length"){const u=Number(r);i.forEach((d,p)=>{(p==="length"||p===_r||!Zt(p)&&p>=u)&&a(d)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),c&&a(i.get(_r)),t){case"add":l?c&&a(i.get("length")):(a(i.get(_n)),Ln(e)&&a(i.get(hs)));break;case"delete":l||(a(i.get(_n)),Ln(e)&&a(i.get(hs)));break;case"set":Ln(e)&&a(i.get(_n));break}}Ks()}function rd(e,t){const n=oo.get(e);return n&&n.get(t)}function An(e){const t=ve(e);return t===e?t:(We(t,"iterate",_r),yt(e)?t:t.map(je))}function Co(e){return We(e=ve(e),"iterate",_r),e}const od={__proto__:null,[Symbol.iterator](){return Vo(this,Symbol.iterator,je)},concat(...e){return An(this).concat(...e.map(t=>se(t)?An(t):t))},entries(){return Vo(this,"entries",e=>(e[1]=je(e[1]),e))},every(e,t){return jt(this,"every",e,t,void 0,arguments)},filter(e,t){return jt(this,"filter",e,t,n=>n.map(je),arguments)},find(e,t){return jt(this,"find",e,t,je,arguments)},findIndex(e,t){return jt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return jt(this,"findLast",e,t,je,arguments)},findLastIndex(e,t){return jt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return jt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ho(this,"includes",e)},indexOf(...e){return Ho(this,"indexOf",e)},join(e){return An(this).join(e)},lastIndexOf(...e){return Ho(this,"lastIndexOf",e)},map(e,t){return jt(this,"map",e,t,void 0,arguments)},pop(){return er(this,"pop")},push(...e){return er(this,"push",e)},reduce(e,...t){return Mi(this,"reduce",e,t)},reduceRight(e,...t){return Mi(this,"reduceRight",e,t)},shift(){return er(this,"shift")},some(e,t){return jt(this,"some",e,t,void 0,arguments)},splice(...e){return er(this,"splice",e)},toReversed(){return An(this).toReversed()},toSorted(e){return An(this).toSorted(e)},toSpliced(...e){return An(this).toSpliced(...e)},unshift(...e){return er(this,"unshift",e)},values(){return Vo(this,"values",je)}};function Vo(e,t,n){const r=Co(e),o=r[t]();return r!==e&&!yt(e)&&(o._next=o.next,o.next=()=>{const s=o._next();return s.value&&(s.value=n(s.value)),s}),o}const sd=Array.prototype;function jt(e,t,n,r,o,s){const i=Co(e),a=i!==e&&!yt(e),l=i[t];if(l!==sd[t]){const d=l.apply(e,s);return a?je(d):d}let c=n;i!==e&&(a?c=function(d,p){return n.call(this,je(d),p,e)}:n.length>2&&(c=function(d,p){return n.call(this,d,p,e)}));const u=l.call(i,c,r);return a&&o?o(u):u}function Mi(e,t,n,r){const o=Co(e);let s=n;return o!==e&&(yt(e)?n.length>3&&(s=function(i,a,l){return n.call(this,i,a,l,e)}):s=function(i,a,l){return n.call(this,i,je(a),l,e)}),o[t](s,...r)}function Ho(e,t,n){const r=ve(e);We(r,"iterate",_r);const o=r[t](...n);return(o===-1||o===!1)&&Ys(n[0])?(n[0]=ve(n[0]),r[t](...n)):o}function er(e,t,n=[]){Yt(),Ws();const r=ve(e)[t].apply(e,n);return Ks(),Xt(),r}const id=Ns("__proto__,__v_isRef,__isVue"),xl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Zt));function ad(e){Zt(e)||(e=String(e));const t=ve(this);return We(t,"has",e),t.hasOwnProperty(e)}class Cl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return s;if(n==="__v_raw")return r===(o?s?Pl:Ol:s?kl:Al).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=se(t);if(!o){let l;if(i&&(l=od[n]))return l;if(n==="hasOwnProperty")return ad}const a=Reflect.get(t,n,Re(t)?t:r);return(Zt(n)?xl.has(n):id(n))||(o||We(t,"get",n),s)?a:Re(a)?i&&Vs(n)?a:a.value:Oe(a)?o?Eo(a):cn(a):a}}class Sl extends Cl{constructor(t=!1){super(!1,t)}set(t,n,r,o){let s=t[n];if(!this._isShallow){const l=dn(s);if(!yt(r)&&!dn(r)&&(s=ve(s),r=ve(r)),!se(t)&&Re(s)&&!Re(r))return l?!1:(s.value=r,!0)}const i=se(t)&&Vs(n)?Number(n)<t.length:we(t,n),a=Reflect.set(t,n,r,Re(t)?t:o);return t===ve(o)&&(i?un(r,s)&&Wt(t,"set",n,r):Wt(t,"add",n,r)),a}deleteProperty(t,n){const r=we(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&Wt(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Zt(n)||!xl.has(n))&&We(t,"has",n),r}ownKeys(t){return We(t,"iterate",se(t)?"length":_n),Reflect.ownKeys(t)}}class El extends Cl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ld=new Sl,ud=new El,cd=new Sl(!0),dd=new El(!0),ms=e=>e,Lr=e=>Reflect.getPrototypeOf(e);function fd(e,t,n){return function(...r){const o=this.__v_raw,s=ve(o),i=Ln(s),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,c=o[e](...r),u=n?ms:t?so:je;return!t&&We(s,"iterate",l?hs:_n),{next(){const{value:d,done:p}=c.next();return p?{value:d,done:p}:{value:a?[u(d[0]),u(d[1])]:u(d),done:p}},[Symbol.iterator](){return this}}}}function Nr(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function pd(e,t){const n={get(o){const s=this.__v_raw,i=ve(s),a=ve(o);e||(un(o,a)&&We(i,"get",o),We(i,"get",a));const{has:l}=Lr(i),c=t?ms:e?so:je;if(l.call(i,o))return c(s.get(o));if(l.call(i,a))return c(s.get(a));s!==i&&s.get(o)},get size(){const o=this.__v_raw;return!e&&We(ve(o),"iterate",_n),Reflect.get(o,"size",o)},has(o){const s=this.__v_raw,i=ve(s),a=ve(o);return e||(un(o,a)&&We(i,"has",o),We(i,"has",a)),o===a?s.has(o):s.has(o)||s.has(a)},forEach(o,s){const i=this,a=i.__v_raw,l=ve(a),c=t?ms:e?so:je;return!e&&We(l,"iterate",_n),a.forEach((u,d)=>o.call(s,c(u),c(d),i))}};return Ye(n,e?{add:Nr("add"),set:Nr("set"),delete:Nr("delete"),clear:Nr("clear")}:{add(o){!t&&!yt(o)&&!dn(o)&&(o=ve(o));const s=ve(this);return Lr(s).has.call(s,o)||(s.add(o),Wt(s,"add",o,o)),this},set(o,s){!t&&!yt(s)&&!dn(s)&&(s=ve(s));const i=ve(this),{has:a,get:l}=Lr(i);let c=a.call(i,o);c||(o=ve(o),c=a.call(i,o));const u=l.call(i,o);return i.set(o,s),c?un(s,u)&&Wt(i,"set",o,s):Wt(i,"add",o,s),this},delete(o){const s=ve(this),{has:i,get:a}=Lr(s);let l=i.call(s,o);l||(o=ve(o),l=i.call(s,o)),a&&a.call(s,o);const c=s.delete(o);return l&&Wt(s,"delete",o,void 0),c},clear(){const o=ve(this),s=o.size!==0,i=o.clear();return s&&Wt(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=fd(o,e,t)}),n}function So(e,t){const n=pd(e,t);return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(we(n,o)&&o in r?n:r,o,s)}const hd={get:So(!1,!1)},md={get:So(!1,!0)},gd={get:So(!0,!1)},vd={get:So(!0,!0)},Al=new WeakMap,kl=new WeakMap,Ol=new WeakMap,Pl=new WeakMap;function yd(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function bd(e){return e.__v_skip||!Object.isExtensible(e)?0:yd(Wc(e))}function cn(e){return dn(e)?e:Ao(e,!1,ld,hd,Al)}function Ml(e){return Ao(e,!1,cd,md,kl)}function Eo(e){return Ao(e,!0,ud,gd,Ol)}function kn(e){return Ao(e,!0,dd,vd,Pl)}function Ao(e,t,n,r,o){if(!Oe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=bd(e);if(s===0)return e;const i=o.get(e);if(i)return i;const a=new Proxy(e,s===2?r:n);return o.set(e,a),a}function Nn(e){return dn(e)?Nn(e.__v_raw):!!(e&&e.__v_isReactive)}function dn(e){return!!(e&&e.__v_isReadonly)}function yt(e){return!!(e&&e.__v_isShallow)}function Ys(e){return e?!!e.__v_raw:!1}function ve(e){const t=e&&e.__v_raw;return t?ve(t):e}function Xs(e){return!we(e,"__v_skip")&&Object.isExtensible(e)&&ds(e,"__v_skip",!0),e}const je=e=>Oe(e)?cn(e):e,so=e=>Oe(e)?Eo(e):e;function Re(e){return e?e.__v_isRef===!0:!1}function G(e){return Tl(e,!1)}function Gt(e){return Tl(e,!0)}function Tl(e,t){return Re(e)?e:new _d(e,t)}class _d{constructor(t,n){this.dep=new xo,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ve(t),this._value=n?t:je(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||yt(t)||dn(t);t=r?t:ve(t),un(t,n)&&(this._rawValue=t,this._value=r?t:je(t),this.dep.trigger())}}function f(e){return Re(e)?e.value:e}function Be(e){return ie(e)?e():f(e)}const wd={get:(e,t,n)=>t==="__v_raw"?e:f(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Re(o)&&!Re(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Rl(e){return Nn(e)?e:new Proxy(e,wd)}class xd{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new xo,{get:r,set:o}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=o}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Cd(e){return new xd(e)}function qt(e){const t=se(e)?new Array(e.length):{};for(const n in e)t[n]=Dl(e,n);return t}class Sd{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return rd(ve(this._object),this._key)}}class Ed{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Ad(e,t,n){return Re(e)?e:ie(e)?new Ed(e):Oe(e)&&arguments.length>1?Dl(e,t,n):G(e)}function Dl(e,t,n){const r=e[t];return Re(r)?r:new Sd(e,t,n)}class kd{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new xo(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=br-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&ke!==this)return gl(this,!0),!0}get value(){const t=this.dep.track();return bl(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Od(e,t,n=!1){let r,o;return ie(e)?r=e:(r=e.get,o=e.set),new kd(r,o,n)}const jr={},io=new WeakMap;let bn;function Pd(e,t=!1,n=bn){if(n){let r=io.get(n);r||io.set(n,r=[]),r.push(e)}}function Md(e,t,n=Se){const{immediate:r,deep:o,once:s,scheduler:i,augmentJob:a,call:l}=n,c=C=>o?C:yt(C)||o===!1||o===0?Kt(C,1):Kt(C);let u,d,p,h,g=!1,y=!1;if(Re(e)?(d=()=>e.value,g=yt(e)):Nn(e)?(d=()=>c(e),g=!0):se(e)?(y=!0,g=e.some(C=>Nn(C)||yt(C)),d=()=>e.map(C=>{if(Re(C))return C.value;if(Nn(C))return c(C);if(ie(C))return l?l(C,2):C()})):ie(e)?t?d=l?()=>l(e,2):e:d=()=>{if(p){Yt();try{p()}finally{Xt()}}const C=bn;bn=u;try{return l?l(e,3,[h]):e(h)}finally{bn=C}}:d=It,t&&o){const C=d,P=o===!0?1/0:o;d=()=>Kt(C(),P)}const _=wo(),x=()=>{u.stop(),_&&_.active&&zs(_.effects,u)};if(s&&t){const C=t;t=(...P)=>{C(...P),x()}}let A=y?new Array(e.length).fill(jr):jr;const S=C=>{if(!(!(u.flags&1)||!u.dirty&&!C))if(t){const P=u.run();if(o||g||(y?P.some((H,F)=>un(H,A[F])):un(P,A))){p&&p();const H=bn;bn=u;try{const F=[P,A===jr?void 0:y&&A[0]===jr?[]:A,h];A=P,l?l(t,3,F):t(...F)}finally{bn=H}}}else u.run()};return a&&a(S),u=new hl(d),u.scheduler=i?()=>i(S,!1):S,h=C=>Pd(C,!1,u),p=u.onStop=()=>{const C=io.get(u);if(C){if(l)l(C,4);else for(const P of C)P();io.delete(u)}},t?r?S(!0):A=u.run():i?i(S.bind(null,!0),!0):u.run(),x.pause=u.pause.bind(u),x.resume=u.resume.bind(u),x.stop=x,x}function Kt(e,t=1/0,n){if(t<=0||!Oe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Re(e))Kt(e.value,t,n);else if(se(e))for(let r=0;r<e.length;r++)Kt(e[r],t,n);else if(il(e)||Ln(e))e.forEach(r=>{Kt(r,t,n)});else if(ul(e)){for(const r in e)Kt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Kt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Tr(e,t,n,r){try{return r?e(...r):e()}catch(o){ko(o,t,n)}}function Bt(e,t,n,r){if(ie(e)){const o=Tr(e,t,n,r);return o&&al(o)&&o.catch(s=>{ko(s,t,n)}),o}if(se(e)){const o=[];for(let s=0;s<e.length;s++)o.push(Bt(e[s],t,n,r));return o}}function ko(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Se;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,l,c)===!1)return}a=a.parent}if(s){Yt(),Tr(s,null,10,[e,l,c]),Xt();return}}Td(e,n,o,r,i)}function Td(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}const Ze=[];let Tt=-1;const jn=[];let sn=null,In=0;const Il=Promise.resolve();let ao=null;function Ge(e){const t=ao||Il;return e?t.then(this?e.bind(this):e):t}function Rd(e){let t=Tt+1,n=Ze.length;for(;t<n;){const r=t+n>>>1,o=Ze[r],s=wr(o);s<e||s===e&&o.flags&2?t=r+1:n=r}return t}function Js(e){if(!(e.flags&1)){const t=wr(e),n=Ze[Ze.length-1];!n||!(e.flags&2)&&t>=wr(n)?Ze.push(e):Ze.splice(Rd(t),0,e),e.flags|=1,$l()}}function $l(){ao||(ao=Il.then(Bl))}function Dd(e){se(e)?jn.push(...e):sn&&e.id===-1?sn.splice(In+1,0,e):e.flags&1||(jn.push(e),e.flags|=1),$l()}function Ti(e,t,n=Tt+1){for(;n<Ze.length;n++){const r=Ze[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ze.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function ql(e){if(jn.length){const t=[...new Set(jn)].sort((n,r)=>wr(n)-wr(r));if(jn.length=0,sn){sn.push(...t);return}for(sn=t,In=0;In<sn.length;In++){const n=sn[In];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}sn=null,In=0}}const wr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Bl(e){try{for(Tt=0;Tt<Ze.length;Tt++){const t=Ze[Tt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Tr(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Tt<Ze.length;Tt++){const t=Ze[Tt];t&&(t.flags&=-2)}Tt=-1,Ze.length=0,ql(),ao=null,(Ze.length||jn.length)&&Bl()}}let Ve=null,Fl=null;function lo(e){const t=Ve;return Ve=e,Fl=e&&e.type.__scopeId||null,t}function b(e,t=Ve,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&Vi(-1);const s=lo(t);let i;try{i=e(...o)}finally{lo(s),r._d&&Vi(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Ll(e,t){if(Ve===null)return e;const n=Ro(Ve),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[s,i,a,l=Se]=t[o];s&&(ie(s)&&(s={mounted:s,updated:s}),s.deep&&Kt(i),r.push({dir:s,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function gn(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const a=o[i];s&&(a.oldValue=s[i].value);let l=a.dir[r];l&&(Yt(),Bt(l,n,8,[e.el,a,e,t]),Xt())}}const Nl=Symbol("_vte"),Id=e=>e.__isTeleport,pr=e=>e&&(e.disabled||e.disabled===""),Ri=e=>e&&(e.defer||e.defer===""),Di=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ii=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,gs=(e,t)=>{const n=e&&e.to;return De(n)?t?t(n):null:n},jl={name:"Teleport",__isTeleport:!0,process(e,t,n,r,o,s,i,a,l,c){const{mc:u,pc:d,pbc:p,o:{insert:h,querySelector:g,createText:y,createComment:_}}=c,x=pr(t.props);let{shapeFlag:A,children:S,dynamicChildren:C}=t;if(e==null){const P=t.el=y(""),H=t.anchor=y("");h(P,n,r),h(H,n,r);const F=(T,Z)=>{A&16&&(o&&o.isCE&&(o.ce._teleportTarget=T),u(S,T,Z,o,s,i,a,l))},$=()=>{const T=t.target=gs(t.props,g),Z=zl(T,t,y,h);T&&(i!=="svg"&&Di(T)?i="svg":i!=="mathml"&&Ii(T)&&(i="mathml"),x||(F(T,Z),to(t,!1)))};x&&(F(n,H),to(t,!0)),Ri(t.props)?(t.el.__isMounted=!1,Qe(()=>{$(),delete t.el.__isMounted},s)):$()}else{if(Ri(t.props)&&e.el.__isMounted===!1){Qe(()=>{jl.process(e,t,n,r,o,s,i,a,l,c)},s);return}t.el=e.el,t.targetStart=e.targetStart;const P=t.anchor=e.anchor,H=t.target=e.target,F=t.targetAnchor=e.targetAnchor,$=pr(e.props),T=$?n:H,Z=$?P:F;if(i==="svg"||Di(H)?i="svg":(i==="mathml"||Ii(H))&&(i="mathml"),C?(p(e.dynamicChildren,C,T,o,s,i,a),ni(e,t,!0)):l||d(e,t,T,Z,o,s,i,a,!1),x)$?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):zr(t,n,P,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const ae=t.target=gs(t.props,g);ae&&zr(t,ae,null,c,0)}else $&&zr(t,H,F,c,1);to(t,x)}},remove(e,t,n,{um:r,o:{remove:o}},s){const{shapeFlag:i,children:a,anchor:l,targetStart:c,targetAnchor:u,target:d,props:p}=e;if(d&&(o(c),o(u)),s&&o(l),i&16){const h=s||!pr(p);for(let g=0;g<a.length;g++){const y=a[g];r(y,t,n,h,!!y.dynamicChildren)}}},move:zr,hydrate:$d};function zr(e,t,n,{o:{insert:r},m:o},s=2){s===0&&r(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:l,children:c,props:u}=e,d=s===2;if(d&&r(i,t,n),(!d||pr(u))&&l&16)for(let p=0;p<c.length;p++)o(c[p],t,n,2);d&&r(a,t,n)}function $d(e,t,n,r,o,s,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:c,createText:u}},d){const p=t.target=gs(t.props,l);if(p){const h=pr(t.props),g=p._lpa||p.firstChild;if(t.shapeFlag&16)if(h)t.anchor=d(i(e),t,a(e),n,r,o,s),t.targetStart=g,t.targetAnchor=g&&i(g);else{t.anchor=i(e);let y=g;for(;y;){if(y&&y.nodeType===8){if(y.data==="teleport start anchor")t.targetStart=y;else if(y.data==="teleport anchor"){t.targetAnchor=y,p._lpa=t.targetAnchor&&i(t.targetAnchor);break}}y=i(y)}t.targetAnchor||zl(p,t,u,c),d(g&&i(g),t,p,n,r,o,s)}to(t,h)}return t.anchor&&i(t.anchor)}const qd=jl;function to(e,t){const n=e.ctx;if(n&&n.ut){let r,o;for(t?(r=e.el,o=e.anchor):(r=e.targetStart,o=e.targetAnchor);r&&r!==o;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function zl(e,t,n,r){const o=t.targetStart=n(""),s=t.targetAnchor=n("");return o[Nl]=s,e&&(r(o,e),r(s,e)),s}function Qs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Qs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function D(e,t){return ie(e)?Ye({name:e.name},t,{setup:e}):e}function Bd(){const e=at();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function Vl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function hr(e,t,n,r,o=!1){if(se(e)){e.forEach((g,y)=>hr(g,t&&(se(t)?t[y]:t),n,r,o));return}if(zn(r)&&!o){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&hr(e,t,n,r.component.subTree);return}const s=r.shapeFlag&4?Ro(r.component):r.el,i=o?null:s,{i:a,r:l}=e,c=t&&t.r,u=a.refs===Se?a.refs={}:a.refs,d=a.setupState,p=ve(d),h=d===Se?()=>!1:g=>we(p,g);if(c!=null&&c!==l&&(De(c)?(u[c]=null,h(c)&&(d[c]=null)):Re(c)&&(c.value=null)),ie(l))Tr(l,a,12,[i,u]);else{const g=De(l),y=Re(l);if(g||y){const _=()=>{if(e.f){const x=g?h(l)?d[l]:u[l]:l.value;o?se(x)&&zs(x,s):se(x)?x.includes(s)||x.push(s):g?(u[l]=[s],h(l)&&(d[l]=u[l])):(l.value=[s],e.k&&(u[e.k]=l.value))}else g?(u[l]=i,h(l)&&(d[l]=i)):y&&(l.value=i,e.k&&(u[e.k]=i))};i?(_.id=-1,Qe(_,n)):_()}}}_o().requestIdleCallback;_o().cancelIdleCallback;const zn=e=>!!e.type.__asyncLoader,Hl=e=>e.type.__isKeepAlive;function Fd(e,t){Ul(e,"a",t)}function Ld(e,t){Ul(e,"da",t)}function Ul(e,t,n=Ke){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Oo(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Hl(o.parent.vnode)&&Nd(r,t,n,o),o=o.parent}}function Nd(e,t,n,r){const o=Oo(t,e,r,!0);Sn(()=>{zs(r[t],o)},n)}function Oo(e,t,n=Ke,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Yt();const a=Dr(n),l=Bt(t,n,e,i);return a(),Xt(),l});return r?o.unshift(s):o.push(s),s}}const en=e=>(t,n=Ke)=>{(!Er||e==="sp")&&Oo(e,(...r)=>t(...r),n)},jd=en("bm"),ht=en("m"),zd=en("bu"),Vd=en("u"),Wl=en("bum"),Sn=en("um"),Hd=en("sp"),Ud=en("rtg"),Wd=en("rtc");function Kd(e,t=Ke){Oo("ec",e,t)}const Kl="components";function Zs(e,t){return Yl(Kl,e,!0,t)||e}const Gl=Symbol.for("v-ndc");function xr(e){return De(e)?Yl(Kl,e,!1)||e:e||Gl}function Yl(e,t,n=!0,r=!1){const o=Ve||Ke;if(o){const s=o.type;{const a=If(s,!1);if(a&&(a===t||a===it(t)||a===bo(it(t))))return s}const i=$i(o[e]||s[e],t)||$i(o.appContext[e],t);return!i&&r?s:i}}function $i(e,t){return e&&(e[t]||e[it(t)]||e[bo(it(t))])}function vs(e,t,n,r){let o;const s=n,i=se(e);if(i||De(e)){const a=i&&Nn(e);let l=!1,c=!1;a&&(l=!yt(e),c=dn(e),e=Co(e)),o=new Array(e.length);for(let u=0,d=e.length;u<d;u++)o[u]=t(l?c?so(je(e[u])):je(e[u]):e[u],u,void 0,s)}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,s)}else if(Oe(e))if(e[Symbol.iterator])o=Array.from(e,(a,l)=>t(a,l,void 0,s));else{const a=Object.keys(e);o=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];o[l]=t(e[u],u,l,s)}}else o=[];return o}function q(e,t,n={},r,o){if(Ve.ce||Ve.parent&&zn(Ve.parent)&&Ve.parent.ce)return O(),N(ze,null,[E("slot",n,r&&r())],64);let s=e[t];s&&s._c&&(s._d=!1),O();const i=s&&Xl(s(n)),a=n.key||i&&i.key,l=N(ze,{key:(a&&!Zt(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),s&&s._c&&(s._d=!0),l}function Xl(e){return e.some(t=>Sr(t)?!(t.type===wt||t.type===ze&&!Xl(t.children)):!0)?e:null}function Gd(e,t){const n={};for(const r in e)n[cr(r)]=e[r];return n}const ys=e=>e?vu(e)?Ro(e):ys(e.parent):null,mr=Ye(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ys(e.parent),$root:e=>ys(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Zl(e),$forceUpdate:e=>e.f||(e.f=()=>{Js(e.update)}),$nextTick:e=>e.n||(e.n=Ge.bind(e.proxy)),$watch:e=>yf.bind(e)}),Uo=(e,t)=>e!==Se&&!e.__isScriptSetup&&we(e,t),Yd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:s,accessCache:i,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const h=i[t];if(h!==void 0)switch(h){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(Uo(r,t))return i[t]=1,r[t];if(o!==Se&&we(o,t))return i[t]=2,o[t];if((c=e.propsOptions[0])&&we(c,t))return i[t]=3,s[t];if(n!==Se&&we(n,t))return i[t]=4,n[t];_s&&(i[t]=0)}}const u=mr[t];let d,p;if(u)return t==="$attrs"&&We(e.attrs,"get",""),u(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(n!==Se&&we(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,we(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:s}=e;return Uo(o,t)?(o[t]=n,!0):r!==Se&&we(r,t)?(r[t]=n,!0):we(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:s}},i){let a;return!!n[i]||e!==Se&&we(e,i)||Uo(t,i)||(a=s[0])&&we(a,i)||we(r,i)||we(mr,i)||we(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:we(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Xd(){return Jd().slots}function Jd(e){const t=at();return t.setupContext||(t.setupContext=bu(t))}function bs(e){return se(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function Jl(e,t){const n=bs(e);for(const r in t){if(r.startsWith("__skip"))continue;let o=n[r];o?se(o)||ie(o)?o=n[r]={type:o,default:t[r]}:o.default=t[r]:o===null&&(o=n[r]={default:t[r]}),o&&t[`__skip_${r}`]&&(o.skipFactory=!0)}return n}let _s=!0;function Qd(e){const t=Zl(e),n=e.proxy,r=e.ctx;_s=!1,t.beforeCreate&&qi(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:a,provide:l,inject:c,created:u,beforeMount:d,mounted:p,beforeUpdate:h,updated:g,activated:y,deactivated:_,beforeDestroy:x,beforeUnmount:A,destroyed:S,unmounted:C,render:P,renderTracked:H,renderTriggered:F,errorCaptured:$,serverPrefetch:T,expose:Z,inheritAttrs:ae,components:fe,directives:Ce,filters:be}=t;if(c&&Zd(c,r,null),i)for(const re in i){const B=i[re];ie(B)&&(r[re]=B.bind(n))}if(o){const re=o.call(n,n);Oe(re)&&(e.data=cn(re))}if(_s=!0,s)for(const re in s){const B=s[re],qe=ie(B)?B.bind(n,n):ie(B.get)?B.get.bind(n,n):It,lt=!ie(B)&&ie(B.set)?B.set.bind(n):It,ut=U({get:qe,set:lt});Object.defineProperty(r,re,{enumerable:!0,configurable:!0,get:()=>ut.value,set:Ne=>ut.value=Ne})}if(a)for(const re in a)Ql(a[re],r,n,re);if(l){const re=ie(l)?l.call(n):l;Reflect.ownKeys(re).forEach(B=>{Vn(B,re[B])})}u&&qi(u,e,"c");function ue(re,B){se(B)?B.forEach(qe=>re(qe.bind(n))):B&&re(B.bind(n))}if(ue(jd,d),ue(ht,p),ue(zd,h),ue(Vd,g),ue(Fd,y),ue(Ld,_),ue(Kd,$),ue(Wd,H),ue(Ud,F),ue(Wl,A),ue(Sn,C),ue(Hd,T),se(Z))if(Z.length){const re=e.exposed||(e.exposed={});Z.forEach(B=>{Object.defineProperty(re,B,{get:()=>n[B],set:qe=>n[B]=qe,enumerable:!0})})}else e.exposed||(e.exposed={});P&&e.render===It&&(e.render=P),ae!=null&&(e.inheritAttrs=ae),fe&&(e.components=fe),Ce&&(e.directives=Ce),T&&Vl(e)}function Zd(e,t,n=It){se(e)&&(e=ws(e));for(const r in e){const o=e[r];let s;Oe(o)?"default"in o?s=ot(o.from||r,o.default,!0):s=ot(o.from||r):s=ot(o),Re(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function qi(e,t,n){Bt(se(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ql(e,t,n,r){let o=r.includes(".")?fu(n,r):()=>n[r];if(De(e)){const s=t[e];ie(s)&&$e(o,s)}else if(ie(e))$e(o,e.bind(n));else if(Oe(e))if(se(e))e.forEach(s=>Ql(s,t,n,r));else{const s=ie(e.handler)?e.handler.bind(n):t[e.handler];ie(s)&&$e(o,s,e)}}function Zl(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,a=s.get(t);let l;return a?l=a:!o.length&&!n&&!r?l=t:(l={},o.length&&o.forEach(c=>uo(l,c,i,!0)),uo(l,t,i)),Oe(t)&&s.set(t,l),l}function uo(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&uo(e,s,n,!0),o&&o.forEach(i=>uo(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=ef[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const ef={data:Bi,props:Fi,emits:Fi,methods:ar,computed:ar,beforeCreate:Je,created:Je,beforeMount:Je,mounted:Je,beforeUpdate:Je,updated:Je,beforeDestroy:Je,beforeUnmount:Je,destroyed:Je,unmounted:Je,activated:Je,deactivated:Je,errorCaptured:Je,serverPrefetch:Je,components:ar,directives:ar,watch:nf,provide:Bi,inject:tf};function Bi(e,t){return t?e?function(){return Ye(ie(e)?e.call(this,this):e,ie(t)?t.call(this,this):t)}:t:e}function tf(e,t){return ar(ws(e),ws(t))}function ws(e){if(se(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Je(e,t){return e?[...new Set([].concat(e,t))]:t}function ar(e,t){return e?Ye(Object.create(null),e,t):t}function Fi(e,t){return e?se(e)&&se(t)?[...new Set([...e,...t])]:Ye(Object.create(null),bs(e),bs(t??{})):t}function nf(e,t){if(!e)return t;if(!t)return e;const n=Ye(Object.create(null),e);for(const r in t)n[r]=Je(e[r],t[r]);return n}function eu(){return{app:null,config:{isNativeTag:Hc,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let rf=0;function of(e,t){return function(r,o=null){ie(r)||(r=Ye({},r)),o!=null&&!Oe(o)&&(o=null);const s=eu(),i=new WeakSet,a=[];let l=!1;const c=s.app={_uid:rf++,_component:r,_props:o,_container:null,_context:s,_instance:null,version:qf,get config(){return s.config},set config(u){},use(u,...d){return i.has(u)||(u&&ie(u.install)?(i.add(u),u.install(c,...d)):ie(u)&&(i.add(u),u(c,...d))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,d){return d?(s.components[u]=d,c):s.components[u]},directive(u,d){return d?(s.directives[u]=d,c):s.directives[u]},mount(u,d,p){if(!l){const h=c._ceVNode||E(r,o);return h.appContext=s,p===!0?p="svg":p===!1&&(p=void 0),e(h,u,p),l=!0,c._container=u,u.__vue_app__=c,Ro(h.component)}},onUnmount(u){a.push(u)},unmount(){l&&(Bt(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,d){return s.provides[u]=d,c},runWithContext(u){const d=wn;wn=c;try{return u()}finally{wn=d}}};return c}}let wn=null;function Vn(e,t){if(Ke){let n=Ke.provides;const r=Ke.parent&&Ke.parent.provides;r===n&&(n=Ke.provides=Object.create(r)),n[e]=t}}function ot(e,t,n=!1){const r=at();if(r||wn){let o=wn?wn._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&ie(t)?t.call(r&&r.proxy):t}}function tu(){return!!(at()||wn)}const nu={},ru=()=>Object.create(nu),ou=e=>Object.getPrototypeOf(e)===nu;function sf(e,t,n,r=!1){const o={},s=ru();e.propsDefaults=Object.create(null),su(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:Ml(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function af(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,a=ve(o),[l]=e.propsOptions;let c=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let p=u[d];if(Mo(e.emitsOptions,p))continue;const h=t[p];if(l)if(we(s,p))h!==s[p]&&(s[p]=h,c=!0);else{const g=it(p);o[g]=xs(l,a,g,h,e,!1)}else h!==s[p]&&(s[p]=h,c=!0)}}}else{su(e,t,o,s)&&(c=!0);let u;for(const d in a)(!t||!we(t,d)&&((u=hn(d))===d||!we(t,u)))&&(l?n&&(n[d]!==void 0||n[u]!==void 0)&&(o[d]=xs(l,a,d,void 0,e,!0)):delete o[d]);if(s!==a)for(const d in s)(!t||!we(t,d))&&(delete s[d],c=!0)}c&&Wt(e.attrs,"set","")}function su(e,t,n,r){const[o,s]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(ur(l))continue;const c=t[l];let u;o&&we(o,u=it(l))?!s||!s.includes(u)?n[u]=c:(a||(a={}))[u]=c:Mo(e.emitsOptions,l)||(!(l in r)||c!==r[l])&&(r[l]=c,i=!0)}if(s){const l=ve(n),c=a||Se;for(let u=0;u<s.length;u++){const d=s[u];n[d]=xs(o,l,d,c[d],e,!we(c,d))}}return i}function xs(e,t,n,r,o,s){const i=e[n];if(i!=null){const a=we(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&ie(l)){const{propsDefaults:c}=o;if(n in c)r=c[n];else{const u=Dr(o);r=c[n]=l.call(null,t),u()}}else r=l;o.ce&&o.ce._setProp(n,r)}i[0]&&(s&&!a?r=!1:i[1]&&(r===""||r===hn(n))&&(r=!0))}return r}const lf=new WeakMap;function iu(e,t,n=!1){const r=n?lf:t.propsCache,o=r.get(e);if(o)return o;const s=e.props,i={},a=[];let l=!1;if(!ie(e)){const u=d=>{l=!0;const[p,h]=iu(d,t,!0);Ye(i,p),h&&a.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!s&&!l)return Oe(e)&&r.set(e,Fn),Fn;if(se(s))for(let u=0;u<s.length;u++){const d=it(s[u]);Li(d)&&(i[d]=Se)}else if(s)for(const u in s){const d=it(u);if(Li(d)){const p=s[u],h=i[d]=se(p)||ie(p)?{type:p}:Ye({},p),g=h.type;let y=!1,_=!0;if(se(g))for(let x=0;x<g.length;++x){const A=g[x],S=ie(A)&&A.name;if(S==="Boolean"){y=!0;break}else S==="String"&&(_=!1)}else y=ie(g)&&g.name==="Boolean";h[0]=y,h[1]=_,(y||we(h,"default"))&&a.push(d)}}const c=[i,a];return Oe(e)&&r.set(e,c),c}function Li(e){return e[0]!=="$"&&!ur(e)}const ei=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",ti=e=>se(e)?e.map(Rt):[Rt(e)],uf=(e,t,n)=>{if(t._n)return t;const r=b((...o)=>ti(t(...o)),n);return r._c=!1,r},au=(e,t,n)=>{const r=e._ctx;for(const o in e){if(ei(o))continue;const s=e[o];if(ie(s))t[o]=uf(o,s,r);else if(s!=null){const i=ti(s);t[o]=()=>i}}},lu=(e,t)=>{const n=ti(t);e.slots.default=()=>n},uu=(e,t,n)=>{for(const r in t)(n||!ei(r))&&(e[r]=t[r])},cf=(e,t,n)=>{const r=e.slots=ru();if(e.vnode.shapeFlag&32){const o=t.__;o&&ds(r,"__",o,!0);const s=t._;s?(uu(r,t,n),n&&ds(r,"_",s,!0)):au(t,r)}else t&&lu(e,t)},df=(e,t,n)=>{const{vnode:r,slots:o}=e;let s=!0,i=Se;if(r.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:uu(o,t,n):(s=!t.$stable,au(t,o)),i=t}else t&&(lu(e,t),i={default:1});if(s)for(const a in o)!ei(a)&&i[a]==null&&delete o[a]},Qe=Ef;function ff(e){return pf(e)}function pf(e,t){const n=_o();n.__VUE__=!0;const{insert:r,remove:o,patchProp:s,createElement:i,createText:a,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:p,setScopeId:h=It,insertStaticContent:g}=e,y=(m,v,w,M=null,I=null,R=null,W=void 0,z=null,j=!!v.dynamicChildren)=>{if(m===v)return;m&&!tr(m,v)&&(M=k(m),Ne(m,I,R,!0),m=null),v.patchFlag===-2&&(j=!1,v.dynamicChildren=null);const{type:L,ref:ne,shapeFlag:K}=v;switch(L){case To:_(m,v,w,M);break;case wt:x(m,v,w,M);break;case Ko:m==null&&A(v,w,M,W);break;case ze:fe(m,v,w,M,I,R,W,z,j);break;default:K&1?P(m,v,w,M,I,R,W,z,j):K&6?Ce(m,v,w,M,I,R,W,z,j):(K&64||K&128)&&L.process(m,v,w,M,I,R,W,z,j,Q)}ne!=null&&I?hr(ne,m&&m.ref,R,v||m,!v):ne==null&&m&&m.ref!=null&&hr(m.ref,null,R,m,!0)},_=(m,v,w,M)=>{if(m==null)r(v.el=a(v.children),w,M);else{const I=v.el=m.el;v.children!==m.children&&c(I,v.children)}},x=(m,v,w,M)=>{m==null?r(v.el=l(v.children||""),w,M):v.el=m.el},A=(m,v,w,M)=>{[m.el,m.anchor]=g(m.children,v,w,M,m.el,m.anchor)},S=({el:m,anchor:v},w,M)=>{let I;for(;m&&m!==v;)I=p(m),r(m,w,M),m=I;r(v,w,M)},C=({el:m,anchor:v})=>{let w;for(;m&&m!==v;)w=p(m),o(m),m=w;o(v)},P=(m,v,w,M,I,R,W,z,j)=>{v.type==="svg"?W="svg":v.type==="math"&&(W="mathml"),m==null?H(v,w,M,I,R,W,z,j):T(m,v,I,R,W,z,j)},H=(m,v,w,M,I,R,W,z)=>{let j,L;const{props:ne,shapeFlag:K,transition:te,dirs:le}=m;if(j=m.el=i(m.type,R,ne&&ne.is,ne),K&8?u(j,m.children):K&16&&$(m.children,j,null,M,I,Wo(m,R),W,z),le&&gn(m,null,M,"created"),F(j,m,m.scopeId,W,M),ne){for(const Ae in ne)Ae!=="value"&&!ur(Ae)&&s(j,Ae,null,ne[Ae],R,M);"value"in ne&&s(j,"value",null,ne.value,R),(L=ne.onVnodeBeforeMount)&&Mt(L,M,m)}le&&gn(m,null,M,"beforeMount");const me=hf(I,te);me&&te.beforeEnter(j),r(j,v,w),((L=ne&&ne.onVnodeMounted)||me||le)&&Qe(()=>{L&&Mt(L,M,m),me&&te.enter(j),le&&gn(m,null,M,"mounted")},I)},F=(m,v,w,M,I)=>{if(w&&h(m,w),M)for(let R=0;R<M.length;R++)h(m,M[R]);if(I){let R=I.subTree;if(v===R||hu(R.type)&&(R.ssContent===v||R.ssFallback===v)){const W=I.vnode;F(m,W,W.scopeId,W.slotScopeIds,I.parent)}}},$=(m,v,w,M,I,R,W,z,j=0)=>{for(let L=j;L<m.length;L++){const ne=m[L]=z?an(m[L]):Rt(m[L]);y(null,ne,v,w,M,I,R,W,z)}},T=(m,v,w,M,I,R,W)=>{const z=v.el=m.el;let{patchFlag:j,dynamicChildren:L,dirs:ne}=v;j|=m.patchFlag&16;const K=m.props||Se,te=v.props||Se;let le;if(w&&vn(w,!1),(le=te.onVnodeBeforeUpdate)&&Mt(le,w,v,m),ne&&gn(v,m,w,"beforeUpdate"),w&&vn(w,!0),(K.innerHTML&&te.innerHTML==null||K.textContent&&te.textContent==null)&&u(z,""),L?Z(m.dynamicChildren,L,z,w,M,Wo(v,I),R):W||B(m,v,z,null,w,M,Wo(v,I),R,!1),j>0){if(j&16)ae(z,K,te,w,I);else if(j&2&&K.class!==te.class&&s(z,"class",null,te.class,I),j&4&&s(z,"style",K.style,te.style,I),j&8){const me=v.dynamicProps;for(let Ae=0;Ae<me.length;Ae++){const xe=me[Ae],nt=K[xe],rt=te[xe];(rt!==nt||xe==="value")&&s(z,xe,nt,rt,I,w)}}j&1&&m.children!==v.children&&u(z,v.children)}else!W&&L==null&&ae(z,K,te,w,I);((le=te.onVnodeUpdated)||ne)&&Qe(()=>{le&&Mt(le,w,v,m),ne&&gn(v,m,w,"updated")},M)},Z=(m,v,w,M,I,R,W)=>{for(let z=0;z<v.length;z++){const j=m[z],L=v[z],ne=j.el&&(j.type===ze||!tr(j,L)||j.shapeFlag&198)?d(j.el):w;y(j,L,ne,null,M,I,R,W,!0)}},ae=(m,v,w,M,I)=>{if(v!==w){if(v!==Se)for(const R in v)!ur(R)&&!(R in w)&&s(m,R,v[R],null,I,M);for(const R in w){if(ur(R))continue;const W=w[R],z=v[R];W!==z&&R!=="value"&&s(m,R,z,W,I,M)}"value"in w&&s(m,"value",v.value,w.value,I)}},fe=(m,v,w,M,I,R,W,z,j)=>{const L=v.el=m?m.el:a(""),ne=v.anchor=m?m.anchor:a("");let{patchFlag:K,dynamicChildren:te,slotScopeIds:le}=v;le&&(z=z?z.concat(le):le),m==null?(r(L,w,M),r(ne,w,M),$(v.children||[],w,ne,I,R,W,z,j)):K>0&&K&64&&te&&m.dynamicChildren?(Z(m.dynamicChildren,te,w,I,R,W,z),(v.key!=null||I&&v===I.subTree)&&ni(m,v,!0)):B(m,v,w,ne,I,R,W,z,j)},Ce=(m,v,w,M,I,R,W,z,j)=>{v.slotScopeIds=z,m==null?v.shapeFlag&512?I.ctx.activate(v,w,M,W,j):be(v,w,M,I,R,W,j):Ee(m,v,j)},be=(m,v,w,M,I,R,W)=>{const z=m.component=Mf(m,M,I);if(Hl(m)&&(z.ctx.renderer=Q),Tf(z,!1,W),z.asyncDep){if(I&&I.registerDep(z,ue,W),!m.el){const j=z.subTree=E(wt);x(null,j,v,w),m.placeholder=j.el}}else ue(z,m,v,w,I,R,W)},Ee=(m,v,w)=>{const M=v.component=m.component;if(Cf(m,v,w))if(M.asyncDep&&!M.asyncResolved){re(M,v,w);return}else M.next=v,M.update();else v.el=m.el,M.vnode=v},ue=(m,v,w,M,I,R,W)=>{const z=()=>{if(m.isMounted){let{next:K,bu:te,u:le,parent:me,vnode:Ae}=m;{const Ot=cu(m);if(Ot){K&&(K.el=Ae.el,re(m,K,W)),Ot.asyncDep.then(()=>{m.isUnmounted||z()});return}}let xe=K,nt;vn(m,!1),K?(K.el=Ae.el,re(m,K,W)):K=Ae,te&&eo(te),(nt=K.props&&K.props.onVnodeBeforeUpdate)&&Mt(nt,me,K,Ae),vn(m,!0);const rt=ji(m),kt=m.subTree;m.subTree=rt,y(kt,rt,d(kt.el),k(kt),m,I,R),K.el=rt.el,xe===null&&Sf(m,rt.el),le&&Qe(le,I),(nt=K.props&&K.props.onVnodeUpdated)&&Qe(()=>Mt(nt,me,K,Ae),I)}else{let K;const{el:te,props:le}=v,{bm:me,m:Ae,parent:xe,root:nt,type:rt}=m,kt=zn(v);vn(m,!1),me&&eo(me),!kt&&(K=le&&le.onVnodeBeforeMount)&&Mt(K,xe,v),vn(m,!0);{nt.ce&&nt.ce._def.shadowRoot!==!1&&nt.ce._injectChildStyle(rt);const Ot=m.subTree=ji(m);y(null,Ot,w,M,m,I,R),v.el=Ot.el}if(Ae&&Qe(Ae,I),!kt&&(K=le&&le.onVnodeMounted)){const Ot=v;Qe(()=>Mt(K,xe,Ot),I)}(v.shapeFlag&256||xe&&zn(xe.vnode)&&xe.vnode.shapeFlag&256)&&m.a&&Qe(m.a,I),m.isMounted=!0,v=w=M=null}};m.scope.on();const j=m.effect=new hl(z);m.scope.off();const L=m.update=j.run.bind(j),ne=m.job=j.runIfDirty.bind(j);ne.i=m,ne.id=m.uid,j.scheduler=()=>Js(ne),vn(m,!0),L()},re=(m,v,w)=>{v.component=m;const M=m.vnode.props;m.vnode=v,m.next=null,af(m,v.props,M,w),df(m,v.children,w),Yt(),Ti(m),Xt()},B=(m,v,w,M,I,R,W,z,j=!1)=>{const L=m&&m.children,ne=m?m.shapeFlag:0,K=v.children,{patchFlag:te,shapeFlag:le}=v;if(te>0){if(te&128){lt(L,K,w,M,I,R,W,z,j);return}else if(te&256){qe(L,K,w,M,I,R,W,z,j);return}}le&8?(ne&16&&Xe(L,I,R),K!==L&&u(w,K)):ne&16?le&16?lt(L,K,w,M,I,R,W,z,j):Xe(L,I,R,!0):(ne&8&&u(w,""),le&16&&$(K,w,M,I,R,W,z,j))},qe=(m,v,w,M,I,R,W,z,j)=>{m=m||Fn,v=v||Fn;const L=m.length,ne=v.length,K=Math.min(L,ne);let te;for(te=0;te<K;te++){const le=v[te]=j?an(v[te]):Rt(v[te]);y(m[te],le,w,null,I,R,W,z,j)}L>ne?Xe(m,I,R,!0,!1,K):$(v,w,M,I,R,W,z,j,K)},lt=(m,v,w,M,I,R,W,z,j)=>{let L=0;const ne=v.length;let K=m.length-1,te=ne-1;for(;L<=K&&L<=te;){const le=m[L],me=v[L]=j?an(v[L]):Rt(v[L]);if(tr(le,me))y(le,me,w,null,I,R,W,z,j);else break;L++}for(;L<=K&&L<=te;){const le=m[K],me=v[te]=j?an(v[te]):Rt(v[te]);if(tr(le,me))y(le,me,w,null,I,R,W,z,j);else break;K--,te--}if(L>K){if(L<=te){const le=te+1,me=le<ne?v[le].el:M;for(;L<=te;)y(null,v[L]=j?an(v[L]):Rt(v[L]),w,me,I,R,W,z,j),L++}}else if(L>te)for(;L<=K;)Ne(m[L],I,R,!0),L++;else{const le=L,me=L,Ae=new Map;for(L=me;L<=te;L++){const ct=v[L]=j?an(v[L]):Rt(v[L]);ct.key!=null&&Ae.set(ct.key,L)}let xe,nt=0;const rt=te-me+1;let kt=!1,Ot=0;const Zn=new Array(rt);for(L=0;L<rt;L++)Zn[L]=0;for(L=le;L<=K;L++){const ct=m[L];if(nt>=rt){Ne(ct,I,R,!0);continue}let Pt;if(ct.key!=null)Pt=Ae.get(ct.key);else for(xe=me;xe<=te;xe++)if(Zn[xe-me]===0&&tr(ct,v[xe])){Pt=xe;break}Pt===void 0?Ne(ct,I,R,!0):(Zn[Pt-me]=L+1,Pt>=Ot?Ot=Pt:kt=!0,y(ct,v[Pt],w,null,I,R,W,z,j),nt++)}const Ei=kt?mf(Zn):Fn;for(xe=Ei.length-1,L=rt-1;L>=0;L--){const ct=me+L,Pt=v[ct],Ai=v[ct+1],ki=ct+1<ne?Ai.el||Ai.placeholder:M;Zn[L]===0?y(null,Pt,w,ki,I,R,W,z,j):kt&&(xe<0||L!==Ei[xe]?ut(Pt,w,ki,2):xe--)}}},ut=(m,v,w,M,I=null)=>{const{el:R,type:W,transition:z,children:j,shapeFlag:L}=m;if(L&6){ut(m.component.subTree,v,w,M);return}if(L&128){m.suspense.move(v,w,M);return}if(L&64){W.move(m,v,w,Q);return}if(W===ze){r(R,v,w);for(let K=0;K<j.length;K++)ut(j[K],v,w,M);r(m.anchor,v,w);return}if(W===Ko){S(m,v,w);return}if(M!==2&&L&1&&z)if(M===0)z.beforeEnter(R),r(R,v,w),Qe(()=>z.enter(R),I);else{const{leave:K,delayLeave:te,afterLeave:le}=z,me=()=>{m.ctx.isUnmounted?o(R):r(R,v,w)},Ae=()=>{K(R,()=>{me(),le&&le()})};te?te(R,me,Ae):Ae()}else r(R,v,w)},Ne=(m,v,w,M=!1,I=!1)=>{const{type:R,props:W,ref:z,children:j,dynamicChildren:L,shapeFlag:ne,patchFlag:K,dirs:te,cacheIndex:le}=m;if(K===-2&&(I=!1),z!=null&&(Yt(),hr(z,null,w,m,!0),Xt()),le!=null&&(v.renderCache[le]=void 0),ne&256){v.ctx.deactivate(m);return}const me=ne&1&&te,Ae=!zn(m);let xe;if(Ae&&(xe=W&&W.onVnodeBeforeUnmount)&&Mt(xe,v,m),ne&6)Nt(m.component,w,M);else{if(ne&128){m.suspense.unmount(w,M);return}me&&gn(m,null,v,"beforeUnmount"),ne&64?m.type.remove(m,v,w,Q,M):L&&!L.hasOnce&&(R!==ze||K>0&&K&64)?Xe(L,v,w,!1,!0):(R===ze&&K&384||!I&&ne&16)&&Xe(j,v,w),M&&Ie(m)}(Ae&&(xe=W&&W.onVnodeUnmounted)||me)&&Qe(()=>{xe&&Mt(xe,v,m),me&&gn(m,null,v,"unmounted")},w)},Ie=m=>{const{type:v,el:w,anchor:M,transition:I}=m;if(v===ze){Fe(w,M);return}if(v===Ko){C(m);return}const R=()=>{o(w),I&&!I.persisted&&I.afterLeave&&I.afterLeave()};if(m.shapeFlag&1&&I&&!I.persisted){const{leave:W,delayLeave:z}=I,j=()=>W(w,R);z?z(m.el,R,j):j()}else R()},Fe=(m,v)=>{let w;for(;m!==v;)w=p(m),o(m),m=w;o(v)},Nt=(m,v,w)=>{const{bum:M,scope:I,job:R,subTree:W,um:z,m:j,a:L,parent:ne,slots:{__:K}}=m;Ni(j),Ni(L),M&&eo(M),ne&&se(K)&&K.forEach(te=>{ne.renderCache[te]=void 0}),I.stop(),R&&(R.flags|=8,Ne(W,m,v,w)),z&&Qe(z,v),Qe(()=>{m.isUnmounted=!0},v),v&&v.pendingBranch&&!v.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===v.pendingId&&(v.deps--,v.deps===0&&v.resolve())},Xe=(m,v,w,M=!1,I=!1,R=0)=>{for(let W=R;W<m.length;W++)Ne(m[W],v,w,M,I)},k=m=>{if(m.shapeFlag&6)return k(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const v=p(m.anchor||m.el),w=v&&v[Nl];return w?p(w):v};let Y=!1;const V=(m,v,w)=>{m==null?v._vnode&&Ne(v._vnode,null,null,!0):y(v._vnode||null,m,v,null,null,null,w),v._vnode=m,Y||(Y=!0,Ti(),ql(),Y=!1)},Q={p:y,um:Ne,m:ut,r:Ie,mt:be,mc:$,pc:B,pbc:Z,n:k,o:e};return{render:V,hydrate:void 0,createApp:of(V)}}function Wo({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function hf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ni(e,t,n=!1){const r=e.children,o=t.children;if(se(r)&&se(o))for(let s=0;s<r.length;s++){const i=r[s];let a=o[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[s]=an(o[s]),a.el=i.el),!n&&a.patchFlag!==-2&&ni(i,a)),a.type===To&&(a.el=i.el),a.type===wt&&!a.el&&(a.el=i.el)}}function mf(e){const t=e.slice(),n=[0];let r,o,s,i,a;const l=e.length;for(r=0;r<l;r++){const c=e[r];if(c!==0){if(o=n[n.length-1],e[o]<c){t[r]=o,n.push(r);continue}for(s=0,i=n.length-1;s<i;)a=s+i>>1,e[n[a]]<c?s=a+1:i=a;c<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function cu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:cu(t)}function Ni(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const gf=Symbol.for("v-scx"),vf=()=>ot(gf);function tt(e,t){return Po(e,null,t)}function du(e,t){return Po(e,null,{flush:"post"})}function $e(e,t,n){return Po(e,t,n)}function Po(e,t,n=Se){const{immediate:r,deep:o,flush:s,once:i}=n,a=Ye({},n),l=t&&r||!t&&s!=="post";let c;if(Er){if(s==="sync"){const h=vf();c=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=It,h.resume=It,h.pause=It,h}}const u=Ke;a.call=(h,g,y)=>Bt(h,u,g,y);let d=!1;s==="post"?a.scheduler=h=>{Qe(h,u&&u.suspense)}:s!=="sync"&&(d=!0,a.scheduler=(h,g)=>{g?h():Js(h)}),a.augmentJob=h=>{t&&(h.flags|=4),d&&(h.flags|=2,u&&(h.id=u.uid,h.i=u))};const p=Md(e,t,a);return Er&&(c?c.push(p):l&&p()),p}function yf(e,t,n){const r=this.proxy,o=De(e)?e.includes(".")?fu(r,e):()=>r[e]:e.bind(r,r);let s;ie(t)?s=t:(s=t.handler,n=t);const i=Dr(this),a=Po(o,s.bind(r),n);return i(),a}function fu(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const bf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${it(t)}Modifiers`]||e[`${hn(t)}Modifiers`];function _f(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Se;let o=n;const s=t.startsWith("update:"),i=s&&bf(r,t.slice(7));i&&(i.trim&&(o=n.map(u=>De(u)?u.trim():u)),i.number&&(o=n.map(fs)));let a,l=r[a=cr(t)]||r[a=cr(it(t))];!l&&s&&(l=r[a=cr(hn(t))]),l&&Bt(l,e,6,o);const c=r[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Bt(c,e,6,o)}}function pu(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const s=e.emits;let i={},a=!1;if(!ie(e)){const l=c=>{const u=pu(c,t,!0);u&&(a=!0,Ye(i,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!s&&!a?(Oe(e)&&r.set(e,null),null):(se(s)?s.forEach(l=>i[l]=null):Ye(i,s),Oe(e)&&r.set(e,i),i)}function Mo(e,t){return!e||!go(t)?!1:(t=t.slice(2).replace(/Once$/,""),we(e,t[0].toLowerCase()+t.slice(1))||we(e,hn(t))||we(e,t))}function ji(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:i,attrs:a,emit:l,render:c,renderCache:u,props:d,data:p,setupState:h,ctx:g,inheritAttrs:y}=e,_=lo(e);let x,A;try{if(n.shapeFlag&4){const C=o||r,P=C;x=Rt(c.call(P,C,u,d,h,p,g)),A=a}else{const C=t;x=Rt(C.length>1?C(d,{attrs:a,slots:i,emit:l}):C(d,null)),A=t.props?a:wf(a)}}catch(C){gr.length=0,ko(C,e,1),x=E(wt)}let S=x;if(A&&y!==!1){const C=Object.keys(A),{shapeFlag:P}=S;C.length&&P&7&&(s&&C.some(js)&&(A=xf(A,s)),S=xn(S,A,!1,!0))}return n.dirs&&(S=xn(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&Qs(S,n.transition),x=S,lo(_),x}const wf=e=>{let t;for(const n in e)(n==="class"||n==="style"||go(n))&&((t||(t={}))[n]=e[n]);return t},xf=(e,t)=>{const n={};for(const r in e)(!js(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Cf(e,t,n){const{props:r,children:o,component:s}=e,{props:i,children:a,patchFlag:l}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?zi(r,i,c):!!i;if(l&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const p=u[d];if(i[p]!==r[p]&&!Mo(c,p))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?zi(r,i,c):!0:!!i;return!1}function zi(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Mo(n,s))return!0}return!1}function Sf({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const hu=e=>e.__isSuspense;function Ef(e,t){t&&t.pendingBranch?se(e)?t.effects.push(...e):t.effects.push(e):Dd(e)}const ze=Symbol.for("v-fgt"),To=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Ko=Symbol.for("v-stc"),gr=[];let ft=null;function O(e=!1){gr.push(ft=e?null:[])}function Af(){gr.pop(),ft=gr[gr.length-1]||null}let Cr=1;function Vi(e,t=!1){Cr+=e,e<0&&ft&&t&&(ft.hasOnce=!0)}function mu(e){return e.dynamicChildren=Cr>0?ft||Fn:null,Af(),Cr>0&&ft&&ft.push(e),e}function pe(e,t,n,r,o,s){return mu(ee(e,t,n,r,o,s,!0))}function N(e,t,n,r,o){return mu(E(e,t,n,r,o,!0))}function Sr(e){return e?e.__v_isVNode===!0:!1}function tr(e,t){return e.type===t.type&&e.key===t.key}const gu=({key:e})=>e??null,no=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?De(e)||Re(e)||ie(e)?{i:Ve,r:e,k:t,f:!!n}:e:null);function ee(e,t=null,n=null,r=0,o=null,s=e===ze?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&gu(t),ref:t&&no(t),scopeId:Fl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ve};return a?(ri(l,n),s&128&&e.normalize(l)):n&&(l.shapeFlag|=De(n)?8:16),Cr>0&&!i&&ft&&(l.patchFlag>0||s&6)&&l.patchFlag!==32&&ft.push(l),l}const E=kf;function kf(e,t=null,n=null,r=0,o=null,s=!1){if((!e||e===Gl)&&(e=wt),Sr(e)){const a=xn(e,t,!0);return n&&ri(a,n),Cr>0&&!s&&ft&&(a.shapeFlag&6?ft[ft.indexOf(e)]=a:ft.push(a)),a.patchFlag=-2,a}if($f(e)&&(e=e.__vccOpts),t){t=mt(t);let{class:a,style:l}=t;a&&!De(a)&&(t.class=ye(a)),Oe(l)&&(Ys(l)&&!se(l)&&(l=Ye({},l)),t.style=mn(l))}const i=De(e)?1:hu(e)?128:Id(e)?64:Oe(e)?4:ie(e)?2:0;return ee(e,t,n,r,o,i,s,!0)}function mt(e){return e?Ys(e)||ou(e)?Ye({},e):e:null}function xn(e,t,n=!1,r=!1){const{props:o,ref:s,patchFlag:i,children:a,transition:l}=e,c=t?oe(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&gu(c),ref:t&&t.ref?n&&s?se(s)?s.concat(no(t)):[s,no(t)]:no(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ze?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&xn(e.ssContent),ssFallback:e.ssFallback&&xn(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Qs(u,l.clone(u)),u}function Me(e=" ",t=0){return E(To,null,e,t)}function Rr(e="",t=!1){return t?(O(),N(wt,null,e)):E(wt,null,e)}function Rt(e){return e==null||typeof e=="boolean"?E(wt):se(e)?E(ze,null,e.slice()):Sr(e)?an(e):E(To,null,String(e))}function an(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:xn(e)}function ri(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(se(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),ri(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!ou(t)?t._ctx=Ve:o===3&&Ve&&(Ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ie(t)?(t={default:t,_ctx:Ve},n=32):(t=String(t),r&64?(n=16,t=[Me(t)]):n=8);e.children=t,e.shapeFlag|=n}function oe(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=ye([t.class,r.class]));else if(o==="style")t.style=mn([t.style,r.style]);else if(go(o)){const s=t[o],i=r[o];i&&s!==i&&!(se(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=r[o])}return t}function Mt(e,t,n,r=null){Bt(e,t,7,[n,r])}const Of=eu();let Pf=0;function Mf(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||Of,s={uid:Pf++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new pl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:iu(r,o),emitsOptions:pu(r,o),emit:null,emitted:null,propsDefaults:Se,inheritAttrs:r.inheritAttrs,ctx:Se,data:Se,props:Se,attrs:Se,slots:Se,refs:Se,setupState:Se,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=_f.bind(null,s),e.ce&&e.ce(s),s}let Ke=null;const at=()=>Ke||Ve;let co,Cs;{const e=_o(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),s=>{o.length>1?o.forEach(i=>i(s)):o[0](s)}};co=t("__VUE_INSTANCE_SETTERS__",n=>Ke=n),Cs=t("__VUE_SSR_SETTERS__",n=>Er=n)}const Dr=e=>{const t=Ke;return co(e),e.scope.on(),()=>{e.scope.off(),co(t)}},Hi=()=>{Ke&&Ke.scope.off(),co(null)};function vu(e){return e.vnode.shapeFlag&4}let Er=!1;function Tf(e,t=!1,n=!1){t&&Cs(t);const{props:r,children:o}=e.vnode,s=vu(e);sf(e,r,s,t),cf(e,o,n||t);const i=s?Rf(e,t):void 0;return t&&Cs(!1),i}function Rf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Yd);const{setup:r}=n;if(r){Yt();const o=e.setupContext=r.length>1?bu(e):null,s=Dr(e),i=Tr(r,e,0,[e.props,o]),a=al(i);if(Xt(),s(),(a||e.sp)&&!zn(e)&&Vl(e),a){if(i.then(Hi,Hi),t)return i.then(l=>{Ui(e,l)}).catch(l=>{ko(l,e,0)});e.asyncDep=i}else Ui(e,i)}else yu(e)}function Ui(e,t,n){ie(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Oe(t)&&(e.setupState=Rl(t)),yu(e)}function yu(e,t,n){const r=e.type;e.render||(e.render=r.render||It);{const o=Dr(e);Yt();try{Qd(e)}finally{Xt(),o()}}}const Df={get(e,t){return We(e,"get",""),e[t]}};function bu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Df),slots:e.slots,emit:e.emit,expose:t}}function Ro(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Rl(Xs(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in mr)return mr[n](e)},has(t,n){return n in t||n in mr}})):e.proxy}function If(e,t=!0){return ie(e)?e.displayName||e.name:e.name||t&&e.__name}function $f(e){return ie(e)&&"__vccOpts"in e}const U=(e,t)=>Od(e,t,Er);function _t(e,t,n){const r=arguments.length;return r===2?Oe(t)&&!se(t)?Sr(t)?E(e,null,[t]):E(e,t):E(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Sr(n)&&(n=[n]),E(e,t,n))}const qf="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ss;const Wi=typeof window<"u"&&window.trustedTypes;if(Wi)try{Ss=Wi.createPolicy("vue",{createHTML:e=>e})}catch{}const _u=Ss?e=>Ss.createHTML(e):e=>e,Bf="http://www.w3.org/2000/svg",Ff="http://www.w3.org/1998/Math/MathML",Ut=typeof document<"u"?document:null,Ki=Ut&&Ut.createElement("template"),Lf={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Ut.createElementNS(Bf,e):t==="mathml"?Ut.createElementNS(Ff,e):n?Ut.createElement(e,{is:n}):Ut.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Ut.createTextNode(e),createComment:e=>Ut.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ut.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const i=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{Ki.innerHTML=_u(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=Ki.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Nf=Symbol("_vtc");function jf(e,t,n){const r=e[Nf];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const fo=Symbol("_vod"),wu=Symbol("_vsh"),zf={beforeMount(e,{value:t},{transition:n}){e[fo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):nr(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),nr(e,!0),r.enter(e)):r.leave(e,()=>{nr(e,!1)}):nr(e,t))},beforeUnmount(e,{value:t}){nr(e,t)}};function nr(e,t){e.style.display=t?e[fo]:"none",e[wu]=!t}const Vf=Symbol(""),Hf=/(^|;)\s*display\s*:/;function Uf(e,t,n){const r=e.style,o=De(n);let s=!1;if(n&&!o){if(t)if(De(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&ro(r,a,"")}else for(const i in t)n[i]==null&&ro(r,i,"");for(const i in n)i==="display"&&(s=!0),ro(r,i,n[i])}else if(o){if(t!==n){const i=r[Vf];i&&(n+=";"+i),r.cssText=n,s=Hf.test(n)}}else t&&e.removeAttribute("style");fo in e&&(e[fo]=s?r.display:"",e[wu]&&(r.display="none"))}const Gi=/\s*!important$/;function ro(e,t,n){if(se(n))n.forEach(r=>ro(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Wf(e,t);Gi.test(n)?e.setProperty(hn(r),n.replace(Gi,""),"important"):e[r]=n}}const Yi=["Webkit","Moz","ms"],Go={};function Wf(e,t){const n=Go[t];if(n)return n;let r=it(t);if(r!=="filter"&&r in e)return Go[t]=r;r=bo(r);for(let o=0;o<Yi.length;o++){const s=Yi[o]+r;if(s in e)return Go[t]=s}return t}const Xi="http://www.w3.org/1999/xlink";function Ji(e,t,n,r,o,s=ed(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Xi,t.slice(6,t.length)):e.setAttributeNS(Xi,t,n):n==null||s&&!cl(n)?e.removeAttribute(t):e.setAttribute(t,s?"":Zt(n)?String(n):n)}function Qi(e,t,n,r,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?_u(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const a=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=cl(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function $n(e,t,n,r){e.addEventListener(t,n,r)}function Kf(e,t,n,r){e.removeEventListener(t,n,r)}const Zi=Symbol("_vei");function Gf(e,t,n,r,o=null){const s=e[Zi]||(e[Zi]={}),i=s[t];if(r&&i)i.value=r;else{const[a,l]=Yf(t);if(r){const c=s[t]=Qf(r,o);$n(e,a,c,l)}else i&&(Kf(e,a,i,l),s[t]=void 0)}}const ea=/(?:Once|Passive|Capture)$/;function Yf(e){let t;if(ea.test(e)){t={};let r;for(;r=e.match(ea);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):hn(e.slice(2)),t]}let Yo=0;const Xf=Promise.resolve(),Jf=()=>Yo||(Xf.then(()=>Yo=0),Yo=Date.now());function Qf(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;Bt(Zf(r,n.value),t,5,[r])};return n.value=e,n.attached=Jf(),n}function Zf(e,t){if(se(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const ta=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,ep=(e,t,n,r,o,s)=>{const i=o==="svg";t==="class"?jf(e,r,i):t==="style"?Uf(e,n,r):go(t)?js(t)||Gf(e,t,n,r,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):tp(e,t,r,i))?(Qi(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ji(e,t,r,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!De(r))?Qi(e,it(t),r,s,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Ji(e,t,r,i))};function tp(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&ta(t)&&ie(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return ta(t)&&De(n)?!1:t in e}const na=e=>{const t=e.props["onUpdate:modelValue"]||!1;return se(t)?n=>eo(t,n):t};function np(e){e.target.composing=!0}function ra(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Xo=Symbol("_assign"),rp={created(e,{modifiers:{lazy:t,trim:n,number:r}},o){e[Xo]=na(o);const s=r||o.props&&o.props.type==="number";$n(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),s&&(a=fs(a)),e[Xo](a)}),n&&$n(e,"change",()=>{e.value=e.value.trim()}),t||($n(e,"compositionstart",np),$n(e,"compositionend",ra),$n(e,"change",ra))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:o,number:s}},i){if(e[Xo]=na(i),e.composing)return;const a=(s||e.type==="number")&&!/^0\d/.test(e.value)?fs(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||o&&e.value.trim()===l)||(e.value=l))}},op=["ctrl","shift","alt","meta"],sp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>op.some(n=>e[`${n}Key`]&&!t.includes(n))},xu=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(o,...s)=>{for(let i=0;i<t.length;i++){const a=sp[t[i]];if(a&&a(o,t))return}return e(o,...s)})},ip={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Cu=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=o=>{if(!("key"in o))return;const s=hn(o.key);if(t.some(i=>i===s||ip[i]===s))return e(o)})},ap=Ye({patchProp:ep},Lf);let oa;function lp(){return oa||(oa=ff(ap))}const up=(...e)=>{const t=lp().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=dp(r);if(!o)return;const s=t._component;!ie(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,cp(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function cp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function dp(e){return De(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const fp=Symbol();var sa;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(sa||(sa={}));function pp(){const e=Hs(!0),t=e.run(()=>G({}));let n=[],r=[];const o=Xs({install(s){o._a=s,s.provide(fp,o),s.config.globalProperties.$pinia=o,r.forEach(i=>n.push(i)),r=[]},use(s){return this._a?n.push(s):r.push(s),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}function Su(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Su(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Eu(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Su(e))&&(r&&(r+=" "),r+=t);return r}const ia=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,aa=Eu,oi=(e,t)=>n=>{var r;if(t?.variants==null)return aa(e,n?.class,n?.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(c=>{const u=n?.[c],d=s?.[c];if(u===null)return null;const p=ia(u)||ia(d);return o[c][p]}),a=n&&Object.entries(n).reduce((c,u)=>{let[d,p]=u;return p===void 0||(c[d]=p),c},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,u)=>{let{class:d,className:p,...h}=u;return Object.entries(h).every(g=>{let[y,_]=g;return Array.isArray(_)?_.includes({...s,...a}[y]):{...s,...a}[y]===_})?[...c,d,p]:c},[]);return aa(e,i,l,n?.class,n?.className)},si="-",hp=e=>{const t=gp(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(si);return a[0]===""&&a.length!==1&&a.shift(),Au(a,t)||mp(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},Au=(e,t)=>{if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Au(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(si);return t.validators.find(({validator:i})=>i(s))?.classGroupId},la=/^\[(.+)\]$/,mp=e=>{if(la.test(e)){const t=la.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},gp=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const o in n)Es(n[o],r,o,t);return r},Es=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:ua(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(vp(o)){Es(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Es(i,ua(t,s),n,r)})})},ua=(e,t)=>{let n=e;return t.split(si).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},vp=e=>e.isThemeGetter,yp=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},As="!",ks=":",bp=ks.length,_p=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=o=>{const s=[];let i=0,a=0,l=0,c;for(let g=0;g<o.length;g++){let y=o[g];if(i===0&&a===0){if(y===ks){s.push(o.slice(l,g)),l=g+bp;continue}if(y==="/"){c=g;continue}}y==="["?i++:y==="]"?i--:y==="("?a++:y===")"&&a--}const u=s.length===0?o:o.substring(l),d=wp(u),p=d!==u,h=c&&c>l?c-l:void 0;return{modifiers:s,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:h}};if(t){const o=t+ks,s=r;r=i=>i.startsWith(o)?s(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(n){const o=r;r=s=>n({className:s,parseClassName:o})}return r},wp=e=>e.endsWith(As)?e.substring(0,e.length-1):e.startsWith(As)?e.substring(1):e,xp=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const o=[];let s=[];return r.forEach(i=>{i[0]==="["||t[i]?(o.push(...s.sort(),i),s=[]):s.push(i)}),o.push(...s.sort()),o}},Cp=e=>({cache:yp(e.cacheSize),parseClassName:_p(e),sortModifiers:xp(e),...hp(e)}),Sp=/\s+/,Ep=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:s}=t,i=[],a=e.trim().split(Sp);let l="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{isExternal:d,modifiers:p,hasImportantModifier:h,baseClassName:g,maybePostfixModifierPosition:y}=n(u);if(d){l=u+(l.length>0?" "+l:l);continue}let _=!!y,x=r(_?g.substring(0,y):g);if(!x){if(!_){l=u+(l.length>0?" "+l:l);continue}if(x=r(g),!x){l=u+(l.length>0?" "+l:l);continue}_=!1}const A=s(p).join(":"),S=h?A+As:A,C=S+x;if(i.includes(C))continue;i.push(C);const P=o(x,_);for(let H=0;H<P.length;++H){const F=P[H];i.push(S+F)}l=u+(l.length>0?" "+l:l)}return l};function Ap(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=ku(t))&&(r&&(r+=" "),r+=n);return r}const ku=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=ku(e[r]))&&(n&&(n+=" "),n+=t);return n};function kp(e,...t){let n,r,o,s=i;function i(l){const c=t.reduce((u,d)=>d(u),e());return n=Cp(c),r=n.cache.get,o=n.cache.set,s=a,a(l)}function a(l){const c=r(l);if(c)return c;const u=Ep(l,n);return o(l,u),u}return function(){return s(Ap.apply(null,arguments))}}const Le=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ou=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Pu=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Op=/^\d+\/\d+$/,Pp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Mp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Tp=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Rp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Dp=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,On=e=>Op.test(e),de=e=>!!e&&!Number.isNaN(Number(e)),nn=e=>!!e&&Number.isInteger(Number(e)),Jo=e=>e.endsWith("%")&&de(e.slice(0,-1)),zt=e=>Pp.test(e),Ip=()=>!0,$p=e=>Mp.test(e)&&!Tp.test(e),Mu=()=>!1,qp=e=>Rp.test(e),Bp=e=>Dp.test(e),Fp=e=>!X(e)&&!J(e),Lp=e=>Yn(e,Du,Mu),X=e=>Ou.test(e),yn=e=>Yn(e,Iu,$p),Qo=e=>Yn(e,Hp,de),ca=e=>Yn(e,Tu,Mu),Np=e=>Yn(e,Ru,Bp),Vr=e=>Yn(e,$u,qp),J=e=>Pu.test(e),rr=e=>Xn(e,Iu),jp=e=>Xn(e,Up),da=e=>Xn(e,Tu),zp=e=>Xn(e,Du),Vp=e=>Xn(e,Ru),Hr=e=>Xn(e,$u,!0),Yn=(e,t,n)=>{const r=Ou.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},Xn=(e,t,n=!1)=>{const r=Pu.exec(e);return r?r[1]?t(r[1]):n:!1},Tu=e=>e==="position"||e==="percentage",Ru=e=>e==="image"||e==="url",Du=e=>e==="length"||e==="size"||e==="bg-size",Iu=e=>e==="length",Hp=e=>e==="number",Up=e=>e==="family-name",$u=e=>e==="shadow",Wp=()=>{const e=Le("color"),t=Le("font"),n=Le("text"),r=Le("font-weight"),o=Le("tracking"),s=Le("leading"),i=Le("breakpoint"),a=Le("container"),l=Le("spacing"),c=Le("radius"),u=Le("shadow"),d=Le("inset-shadow"),p=Le("text-shadow"),h=Le("drop-shadow"),g=Le("blur"),y=Le("perspective"),_=Le("aspect"),x=Le("ease"),A=Le("animate"),S=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...C(),J,X],H=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],$=()=>[J,X,l],T=()=>[On,"full","auto",...$()],Z=()=>[nn,"none","subgrid",J,X],ae=()=>["auto",{span:["full",nn,J,X]},nn,J,X],fe=()=>[nn,"auto",J,X],Ce=()=>["auto","min","max","fr",J,X],be=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ee=()=>["start","end","center","stretch","center-safe","end-safe"],ue=()=>["auto",...$()],re=()=>[On,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],B=()=>[e,J,X],qe=()=>[...C(),da,ca,{position:[J,X]}],lt=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ut=()=>["auto","cover","contain",zp,Lp,{size:[J,X]}],Ne=()=>[Jo,rr,yn],Ie=()=>["","none","full",c,J,X],Fe=()=>["",de,rr,yn],Nt=()=>["solid","dashed","dotted","double"],Xe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],k=()=>[de,Jo,da,ca],Y=()=>["","none",g,J,X],V=()=>["none",de,J,X],Q=()=>["none",de,J,X],ge=()=>[de,J,X],m=()=>[On,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[zt],breakpoint:[zt],color:[Ip],container:[zt],"drop-shadow":[zt],ease:["in","out","in-out"],font:[Fp],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[zt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[zt],shadow:[zt],spacing:["px",de],text:[zt],"text-shadow":[zt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",On,X,J,_]}],container:["container"],columns:[{columns:[de,X,J,a]}],"break-after":[{"break-after":S()}],"break-before":[{"break-before":S()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:H()}],"overflow-x":[{"overflow-x":H()}],"overflow-y":[{"overflow-y":H()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[nn,"auto",J,X]}],basis:[{basis:[On,"full","auto",a,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[de,On,"auto","initial","none",X]}],grow:[{grow:["",de,J,X]}],shrink:[{shrink:["",de,J,X]}],order:[{order:[nn,"first","last","none",J,X]}],"grid-cols":[{"grid-cols":Z()}],"col-start-end":[{col:ae()}],"col-start":[{"col-start":fe()}],"col-end":[{"col-end":fe()}],"grid-rows":[{"grid-rows":Z()}],"row-start-end":[{row:ae()}],"row-start":[{"row-start":fe()}],"row-end":[{"row-end":fe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Ce()}],"auto-rows":[{"auto-rows":Ce()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...be(),"normal"]}],"justify-items":[{"justify-items":[...Ee(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ee()]}],"align-content":[{content:["normal",...be()]}],"align-items":[{items:[...Ee(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ee(),{baseline:["","last"]}]}],"place-content":[{"place-content":be()}],"place-items":[{"place-items":[...Ee(),"baseline"]}],"place-self":[{"place-self":["auto",...Ee()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:ue()}],mx:[{mx:ue()}],my:[{my:ue()}],ms:[{ms:ue()}],me:[{me:ue()}],mt:[{mt:ue()}],mr:[{mr:ue()}],mb:[{mb:ue()}],ml:[{ml:ue()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:re()}],w:[{w:[a,"screen",...re()]}],"min-w":[{"min-w":[a,"screen","none",...re()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...re()]}],h:[{h:["screen","lh",...re()]}],"min-h":[{"min-h":["screen","lh","none",...re()]}],"max-h":[{"max-h":["screen","lh",...re()]}],"font-size":[{text:["base",n,rr,yn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,J,Qo]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Jo,X]}],"font-family":[{font:[jp,X,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,J,X]}],"line-clamp":[{"line-clamp":[de,"none",J,Qo]}],leading:[{leading:[s,...$()]}],"list-image":[{"list-image":["none",J,X]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",J,X]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Nt(),"wavy"]}],"text-decoration-thickness":[{decoration:[de,"from-font","auto",J,yn]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[de,"auto",J,X]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J,X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J,X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:qe()}],"bg-repeat":[{bg:lt()}],"bg-size":[{bg:ut()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},nn,J,X],radial:["",J,X],conic:[nn,J,X]},Vp,Np]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:Ne()}],"gradient-via-pos":[{via:Ne()}],"gradient-to-pos":[{to:Ne()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:Ie()}],"rounded-s":[{"rounded-s":Ie()}],"rounded-e":[{"rounded-e":Ie()}],"rounded-t":[{"rounded-t":Ie()}],"rounded-r":[{"rounded-r":Ie()}],"rounded-b":[{"rounded-b":Ie()}],"rounded-l":[{"rounded-l":Ie()}],"rounded-ss":[{"rounded-ss":Ie()}],"rounded-se":[{"rounded-se":Ie()}],"rounded-ee":[{"rounded-ee":Ie()}],"rounded-es":[{"rounded-es":Ie()}],"rounded-tl":[{"rounded-tl":Ie()}],"rounded-tr":[{"rounded-tr":Ie()}],"rounded-br":[{"rounded-br":Ie()}],"rounded-bl":[{"rounded-bl":Ie()}],"border-w":[{border:Fe()}],"border-w-x":[{"border-x":Fe()}],"border-w-y":[{"border-y":Fe()}],"border-w-s":[{"border-s":Fe()}],"border-w-e":[{"border-e":Fe()}],"border-w-t":[{"border-t":Fe()}],"border-w-r":[{"border-r":Fe()}],"border-w-b":[{"border-b":Fe()}],"border-w-l":[{"border-l":Fe()}],"divide-x":[{"divide-x":Fe()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Fe()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Nt(),"hidden","none"]}],"divide-style":[{divide:[...Nt(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...Nt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[de,J,X]}],"outline-w":[{outline:["",de,rr,yn]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",u,Hr,Vr]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",d,Hr,Vr]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:Fe()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[de,yn]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":Fe()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",p,Hr,Vr]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[de,J,X]}],"mix-blend":[{"mix-blend":[...Xe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Xe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[de]}],"mask-image-linear-from-pos":[{"mask-linear-from":k()}],"mask-image-linear-to-pos":[{"mask-linear-to":k()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":k()}],"mask-image-t-to-pos":[{"mask-t-to":k()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":k()}],"mask-image-r-to-pos":[{"mask-r-to":k()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":k()}],"mask-image-b-to-pos":[{"mask-b-to":k()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":k()}],"mask-image-l-to-pos":[{"mask-l-to":k()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":k()}],"mask-image-x-to-pos":[{"mask-x-to":k()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":k()}],"mask-image-y-to-pos":[{"mask-y-to":k()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[J,X]}],"mask-image-radial-from-pos":[{"mask-radial-from":k()}],"mask-image-radial-to-pos":[{"mask-radial-to":k()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":C()}],"mask-image-conic-pos":[{"mask-conic":[de]}],"mask-image-conic-from-pos":[{"mask-conic-from":k()}],"mask-image-conic-to-pos":[{"mask-conic-to":k()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:qe()}],"mask-repeat":[{mask:lt()}],"mask-size":[{mask:ut()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",J,X]}],filter:[{filter:["","none",J,X]}],blur:[{blur:Y()}],brightness:[{brightness:[de,J,X]}],contrast:[{contrast:[de,J,X]}],"drop-shadow":[{"drop-shadow":["","none",h,Hr,Vr]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",de,J,X]}],"hue-rotate":[{"hue-rotate":[de,J,X]}],invert:[{invert:["",de,J,X]}],saturate:[{saturate:[de,J,X]}],sepia:[{sepia:["",de,J,X]}],"backdrop-filter":[{"backdrop-filter":["","none",J,X]}],"backdrop-blur":[{"backdrop-blur":Y()}],"backdrop-brightness":[{"backdrop-brightness":[de,J,X]}],"backdrop-contrast":[{"backdrop-contrast":[de,J,X]}],"backdrop-grayscale":[{"backdrop-grayscale":["",de,J,X]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[de,J,X]}],"backdrop-invert":[{"backdrop-invert":["",de,J,X]}],"backdrop-opacity":[{"backdrop-opacity":[de,J,X]}],"backdrop-saturate":[{"backdrop-saturate":[de,J,X]}],"backdrop-sepia":[{"backdrop-sepia":["",de,J,X]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",J,X]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[de,"initial",J,X]}],ease:[{ease:["linear","initial",x,J,X]}],delay:[{delay:[de,J,X]}],animate:[{animate:["none",A,J,X]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,J,X]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:V()}],"rotate-x":[{"rotate-x":V()}],"rotate-y":[{"rotate-y":V()}],"rotate-z":[{"rotate-z":V()}],scale:[{scale:Q()}],"scale-x":[{"scale-x":Q()}],"scale-y":[{"scale-y":Q()}],"scale-z":[{"scale-z":Q()}],"scale-3d":["scale-3d"],skew:[{skew:ge()}],"skew-x":[{"skew-x":ge()}],"skew-y":[{"skew-y":ge()}],transform:[{transform:[J,X,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:m()}],"translate-x":[{"translate-x":m()}],"translate-y":[{"translate-y":m()}],"translate-z":[{"translate-z":m()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J,X]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J,X]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[de,rr,yn,Qo]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Kp=kp(Wp);function ce(...e){return Kp(Eu(e))}function gt(e,t){const n=typeof e=="string"&&!t?`${e}Context`:t,r=Symbol(n);return[i=>{const a=ot(r,i);if(a||a===null)return a;throw new Error(`Injection \`${r.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(", ")}`:`\`${e}\``}`)},i=>(Vn(r,i),i)]}function st(){let e=document.activeElement;if(e==null)return null;for(;e!=null&&e.shadowRoot!=null&&e.shadowRoot.activeElement!=null;)e=e.shadowRoot.activeElement;return e}function qu(e,t,n){const r=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),r.dispatchEvent(o)}function ii(e){return e?e.flatMap(t=>t.type===ze?ii(t.children):[t]):[]}const Gp=["INPUT","TEXTAREA"];function Yp(e,t,n,r={}){if(!t||r.enableIgnoredElement&&Gp.includes(t.nodeName))return null;const{arrowKeyOptions:o="both",attributeName:s="[data-reka-collection-item]",itemsArray:i=[],loop:a=!0,dir:l="ltr",preventScroll:c=!0,focus:u=!1}=r,[d,p,h,g,y,_]=[e.key==="ArrowRight",e.key==="ArrowLeft",e.key==="ArrowUp",e.key==="ArrowDown",e.key==="Home",e.key==="End"],x=h||g,A=d||p;if(!y&&!_&&(!x&&!A||o==="vertical"&&A||o==="horizontal"&&x))return null;const S=n?Array.from(n.querySelectorAll(s)):i;if(!S.length)return null;c&&e.preventDefault();let C=null;return A||x?C=Bu(S,t,{goForward:x?g:l==="ltr"?d:p,loop:a}):y?C=S.at(0)||null:_&&(C=S.at(-1)||null),u&&C?.focus(),C}function Bu(e,t,n,r=e.length){if(--r===0)return null;const o=e.indexOf(t),s=n.goForward?o+1:o-1;if(!n.loop&&(s<0||s>=e.length))return null;const i=(s+e.length)%e.length,a=e[i];return a?a.hasAttribute("disabled")&&a.getAttribute("disabled")!=="false"?Bu(e,a,n,r):a:null}const[Fu,pw]=gt("ConfigProvider");function Xp(e,t){var n;const r=Gt();return tt(()=>{r.value=e()},{...t,flush:(n=void 0)!=null?n:"sync"}),Eo(r)}function Ir(e){return wo()?(Us(e),!0):!1}function Jp(){const e=new Set,t=s=>{e.delete(s)};return{on:s=>{e.add(s);const i=()=>t(s);return Ir(i),{off:i}},off:t,trigger:(...s)=>Promise.all(Array.from(e).map(i=>i(...s))),clear:()=>{e.clear()}}}function Qp(e){let t=!1,n;const r=Hs(!0);return(...o)=>(t||(n=r.run(()=>e(...o)),t=!0),n)}function Lu(e){let t=0,n,r;const o=()=>{t-=1,r&&t<=0&&(r.stop(),n=void 0,r=void 0)};return(...s)=>(t+=1,r||(r=Hs(!0),n=r.run(()=>e(...s))),Ir(o),n)}const Et=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Zp=e=>typeof e<"u",eh=Object.prototype.toString,th=e=>eh.call(e)==="[object Object]",fa=nh();function nh(){var e,t;return Et&&((e=window?.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window?.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function rh(e){return at()}function Zo(e){return Array.isArray(e)?e:[e]}function Nu(e,t=1e4){return Cd((n,r)=>{let o=Be(e),s;const i=()=>setTimeout(()=>{o=Be(e),r()},Be(t));return Ir(()=>{clearTimeout(s)}),{get(){return n(),o},set(a){o=a,r(),clearTimeout(s),s=i()}}})}function oh(e,t){rh()&&Wl(e,t)}function ju(e,t,n={}){const{immediate:r=!0,immediateCallback:o=!1}=n,s=Gt(!1);let i=null;function a(){i&&(clearTimeout(i),i=null)}function l(){s.value=!1,a()}function c(...u){o&&e(),a(),s.value=!0,i=setTimeout(()=>{s.value=!1,i=null,e(...u)},Be(t))}return r&&(s.value=!0,Et&&c()),Ir(l),{isPending:Eo(s),start:c,stop:l}}function sh(e,t,n){return $e(e,t,{...n,immediate:!0})}const ai=Et?window:void 0;function Jn(e){var t;const n=Be(e);return(t=n?.$el)!=null?t:n}function Un(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,l,c,u)=>(a.addEventListener(l,c,u),()=>a.removeEventListener(l,c,u)),o=U(()=>{const a=Zo(Be(e[0])).filter(l=>l!=null);return a.every(l=>typeof l!="string")?a:void 0}),s=sh(()=>{var a,l;return[(l=(a=o.value)==null?void 0:a.map(c=>Jn(c)))!=null?l:[ai].filter(c=>c!=null),Zo(Be(o.value?e[1]:e[0])),Zo(f(o.value?e[2]:e[1])),Be(o.value?e[3]:e[2])]},([a,l,c,u])=>{if(n(),!a?.length||!l?.length||!c?.length)return;const d=th(u)?{...u}:u;t.push(...a.flatMap(p=>l.flatMap(h=>c.map(g=>r(p,h,g,d)))))},{flush:"post"}),i=()=>{s(),n()};return Ir(n),i}function ih(){const e=Gt(!1),t=at();return t&&ht(()=>{e.value=!0},t),e}function ah(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function lh(...e){let t,n,r={};e.length===3?(t=e[0],n=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],r=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:o=ai,eventName:s="keydown",passive:i=!1,dedupe:a=!1}=r,l=ah(t);return Un(o,s,u=>{u.repeat&&Be(a)||l(u)&&n(u)},i)}function uh(e){return JSON.parse(JSON.stringify(e))}function $r(e,t,n,r={}){var o,s,i;const{clone:a=!1,passive:l=!1,eventName:c,deep:u=!1,defaultValue:d,shouldEmit:p}=r,h=at(),g=n||h?.emit||((o=h?.$emit)==null?void 0:o.bind(h))||((i=(s=h?.proxy)==null?void 0:s.$emit)==null?void 0:i.bind(h?.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const _=S=>a?typeof a=="function"?a(S):uh(S):S,x=()=>Zp(e[t])?_(e[t]):d,A=S=>{p?p(S)&&g(y,S):g(y,S)};if(l){const S=x(),C=G(S);let P=!1;return $e(()=>e[t],H=>{P||(P=!0,C.value=_(H),Ge(()=>P=!1))}),$e(C,H=>{!P&&(H!==e[t]||u)&&A(H)},{deep:u}),C}else return U({get(){return x()},set(S){A(S)}})}function es(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function Os(e,t,n=".",r){if(!es(t))return Os(e,{},n,r);const o=Object.assign({},t);for(const s in e){if(s==="__proto__"||s==="constructor")continue;const i=e[s];i!=null&&(r&&r(o,s,i,n)||(Array.isArray(i)&&Array.isArray(o[s])?o[s]=[...i,...o[s]]:es(i)&&es(o[s])?o[s]=Os(i,o[s],(n?`${n}.`:"")+s.toString(),r):o[s]=i))}return o}function ch(e){return(...t)=>t.reduce((n,r)=>Os(n,r,"",e),{})}const dh=ch(),fh=Lu(()=>{const e=G(new Map),t=G(),n=U(()=>{for(const i of e.value.values())if(i)return!0;return!1}),r=Fu({scrollBody:G(!0)});let o=null;const s=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.documentElement.style.removeProperty("--scrollbar-width"),document.body.style.overflow=t.value??"",fa&&o?.(),t.value=void 0};return $e(n,(i,a)=>{if(!Et)return;if(!i){a&&s();return}t.value===void 0&&(t.value=document.body.style.overflow);const l=window.innerWidth-document.documentElement.clientWidth,c={padding:l,margin:0},u=r.scrollBody?.value?typeof r.scrollBody.value=="object"?dh({padding:r.scrollBody.value.padding===!0?l:r.scrollBody.value.padding,margin:r.scrollBody.value.margin===!0?l:r.scrollBody.value.margin},c):c:{padding:0,margin:0};l>0&&(document.body.style.paddingRight=typeof u.padding=="number"?`${u.padding}px`:String(u.padding),document.body.style.marginRight=typeof u.margin=="number"?`${u.margin}px`:String(u.margin),document.documentElement.style.setProperty("--scrollbar-width",`${l}px`),document.body.style.overflow="hidden"),fa&&(o=Un(document,"touchmove",d=>ph(d),{passive:!1})),Ge(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function zu(e){const t=Math.random().toString(36).substring(2,7),n=fh();n.value.set(t,e??!1);const r=U({get:()=>n.value.get(t)??!1,set:o=>n.value.set(t,o)});return oh(()=>{n.value.delete(t)}),r}function Vu(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:Vu(n)}}function ph(e){const t=e||window.event,n=t.target;return n instanceof Element&&Vu(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.cancelable&&t.preventDefault(),!1)}function li(e){const t=Fu({dir:G("ltr")});return U(()=>e?.value||t.dir?.value||"ltr")}function qr(e){const t=at(),n=t?.type.emits,r={};return n?.length||console.warn(`No emitted event found. Please check component: ${t?.type.__name}`),n?.forEach(o=>{r[cr(it(o))]=(...s)=>e(o,...s)}),r}let ts=0;function hh(){tt(e=>{if(!Et)return;const t=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",t[0]??pa()),document.body.insertAdjacentElement("beforeend",t[1]??pa()),ts++,e(()=>{ts===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(n=>n.remove()),ts--})})}function pa(){const e=document.createElement("span");return e.setAttribute("data-reka-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function he(){const e=at(),t=G(),n=U(()=>["#text","#comment"].includes(t.value?.$el.nodeName)?t.value?.$el.nextElementSibling:Jn(t)),r=Object.assign({},e.exposed),o={};for(const i in e.props)Object.defineProperty(o,i,{enumerable:!0,configurable:!0,get:()=>e.props[i]});if(Object.keys(r).length>0)for(const i in r)Object.defineProperty(o,i,{enumerable:!0,configurable:!0,get:()=>r[i]});Object.defineProperty(o,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=o;function s(i){t.value=i,i&&(Object.defineProperty(o,"$el",{enumerable:!0,configurable:!0,get:()=>i instanceof Element?i:i.$el}),e.exposed=o)}return{forwardRef:s,currentRef:t,currentElement:n}}function Br(e){const t=at(),n=Object.keys(t?.type.props??{}).reduce((o,s)=>{const i=(t?.type.props[s]).default;return i!==void 0&&(o[s]=i),o},{}),r=Ad(e);return U(()=>{const o={},s=t?.vnode.props??{};return Object.keys(s).forEach(i=>{o[it(i)]=s[i]}),Object.keys({...n,...o}).reduce((i,a)=>(r.value[a]!==void 0&&(i[a]=r.value[a]),i),{})})}function At(e,t){const n=Br(e),r=t?qr(t):{};return U(()=>({...n.value,...r}))}function mh(e,t){const n=Nu(!1,300),r=G(null),o=Jp();function s(){r.value=null,n.value=!1}function i(a,l){const c=a.currentTarget,u={x:a.clientX,y:a.clientY},d=gh(u,c.getBoundingClientRect()),p=vh(u,d),h=yh(l.getBoundingClientRect()),g=_h([...p,...h]);r.value=g,n.value=!0}return tt(a=>{if(e.value&&t.value){const l=u=>i(u,t.value),c=u=>i(u,e.value);e.value.addEventListener("pointerleave",l),t.value.addEventListener("pointerleave",c),a(()=>{e.value?.removeEventListener("pointerleave",l),t.value?.removeEventListener("pointerleave",c)})}}),tt(a=>{if(r.value){const l=c=>{if(!r.value||!(c.target instanceof HTMLElement))return;const u=c.target,d={x:c.clientX,y:c.clientY},p=e.value?.contains(u)||t.value?.contains(u),h=!bh(d,r.value),g=!!u.closest("[data-grace-area-trigger]");p?s():(h||g)&&(s(),o.trigger())};e.value?.ownerDocument.addEventListener("pointermove",l),a(()=>e.value?.ownerDocument.removeEventListener("pointermove",l))}}),{isPointerInTransit:n,onPointerExit:o.on}}function gh(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function vh(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function yh(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function bh(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,c=t[i].x,u=t[i].y;l>r!=u>r&&n<(c-a)*(r-l)/(u-l)+a&&(o=!o)}return o}function _h(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),wh(t)}function wh(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var xh=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Pn=new WeakMap,Ur=new WeakMap,Wr={},ns=0,Hu=function(e){return e&&(e.host||Hu(e.parentNode))},Ch=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Hu(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Sh=function(e,t,n,r){var o=Ch(t,Array.isArray(e)?e:[e]);Wr[n]||(Wr[n]=new WeakMap);var s=Wr[n],i=[],a=new Set,l=new Set(o),c=function(d){!d||a.has(d)||(a.add(d),c(d.parentNode))};o.forEach(c);var u=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(p){if(a.has(p))u(p);else try{var h=p.getAttribute(r),g=h!==null&&h!=="false",y=(Pn.get(p)||0)+1,_=(s.get(p)||0)+1;Pn.set(p,y),s.set(p,_),i.push(p),y===1&&g&&Ur.set(p,!0),_===1&&p.setAttribute(n,"true"),g||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return u(t),a.clear(),ns++,function(){i.forEach(function(d){var p=Pn.get(d)-1,h=s.get(d)-1;Pn.set(d,p),s.set(d,h),p||(Ur.has(d)||d.removeAttribute(r),Ur.delete(d)),h||d.removeAttribute(n)}),ns--,ns||(Pn=new WeakMap,Pn=new WeakMap,Ur=new WeakMap,Wr={})}},Eh=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=xh(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Sh(r,o,n,"aria-hidden")):function(){return null}};function Uu(e){let t;$e(()=>Jn(e),n=>{n?t=Eh(n):t&&t()}),Sn(()=>{t&&t()})}function Ar(e,t="reka"){return`${t}-${Bd?.()}`}function Ah(e){const t=G(),n=U(()=>t.value?.width??0),r=U(()=>t.value?.height??0);return ht(()=>{const o=Jn(e);if(o){t.value={width:o.offsetWidth,height:o.offsetHeight};const s=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const a=i[0];let l,c;if("borderBoxSize"in a){const u=a.borderBoxSize,d=Array.isArray(u)?u[0]:u;l=d.inlineSize,c=d.blockSize}else l=o.offsetWidth,c=o.offsetHeight;t.value={width:l,height:c}});return s.observe(o,{box:"border-box"}),()=>s.unobserve(o)}else t.value=void 0}),{width:n,height:r}}function kh(e,t){const n=G(e);function r(s){return t[n.value][s]??n.value}return{state:n,dispatch:s=>{n.value=r(s)}}}function Oh(e){const t=Nu("",1e3);return{search:t,handleTypeaheadSearch:(o,s)=>{t.value=t.value+o;{const i=st(),a=s.map(p=>({...p,textValue:p.value?.textValue??p.ref.textContent?.trim()??""})),l=a.find(p=>p.ref===i),c=a.map(p=>p.textValue),u=Mh(c,t.value,l?.textValue),d=a.find(p=>p.textValue===u);return d&&d.ref.focus(),d?.ref}},resetTypeahead:()=>{t.value=""}}}function Ph(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Mh(e,t,n){const o=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=Ph(e,Math.max(s,0));o.length===1&&(i=i.filter(c=>c!==n));const l=i.find(c=>c.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function Th(e,t){const n=G({}),r=G("none"),o=G(e),s=e.value?"mounted":"unmounted";let i;const a=t.value?.ownerDocument.defaultView??ai,{state:l,dispatch:c}=kh(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),u=_=>{if(Et){const x=new CustomEvent(_,{bubbles:!1,cancelable:!1});t.value?.dispatchEvent(x)}};$e(e,async(_,x)=>{const A=x!==_;if(await Ge(),A){const S=r.value,C=Kr(t.value);_?(c("MOUNT"),u("enter"),C==="none"&&u("after-enter")):C==="none"||C==="undefined"||n.value?.display==="none"?(c("UNMOUNT"),u("leave"),u("after-leave")):x&&S!==C?(c("ANIMATION_OUT"),u("leave")):(c("UNMOUNT"),u("after-leave"))}},{immediate:!0});const d=_=>{const x=Kr(t.value),A=x.includes(_.animationName),S=l.value==="mounted"?"enter":"leave";if(_.target===t.value&&A&&(u(`after-${S}`),c("ANIMATION_END"),!o.value)){const C=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",i=a?.setTimeout(()=>{t.value?.style.animationFillMode==="forwards"&&(t.value.style.animationFillMode=C)})}_.target===t.value&&x==="none"&&c("ANIMATION_END")},p=_=>{_.target===t.value&&(r.value=Kr(t.value))},h=$e(t,(_,x)=>{_?(n.value=getComputedStyle(_),_.addEventListener("animationstart",p),_.addEventListener("animationcancel",d),_.addEventListener("animationend",d)):(c("ANIMATION_END"),i!==void 0&&a?.clearTimeout(i),x?.removeEventListener("animationstart",p),x?.removeEventListener("animationcancel",d),x?.removeEventListener("animationend",d))},{immediate:!0}),g=$e(l,()=>{const _=Kr(t.value);r.value=l.value==="mounted"?_:"none"});return Sn(()=>{h(),g()}),{isPresent:U(()=>["mounted","unmountSuspended"].includes(l.value))}}function Kr(e){return e&&getComputedStyle(e).animationName||"none"}var Do=D({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){const{present:r,forceMount:o}=qt(e),s=G(),{isPresent:i}=Th(r,s);n({present:i});let a=t.default({present:i.value});a=ii(a||[]);const l=at();if(a&&a?.length>1){const c=l?.parent?.type.name?`<${l.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${c}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(u=>`  - ${u}`).join(`
`)].join(`
`))}return()=>o.value||r.value||i.value?_t(t.default({present:i.value})[0],{ref:c=>{const u=Jn(c);return typeof u?.hasAttribute>"u"||(u?.hasAttribute("data-reka-popper-content-wrapper")?s.value=u.firstElementChild:s.value=u),u}}):null}});const Ps=D({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:n}){return()=>{if(!n.default)return null;const r=ii(n.default()),o=r.findIndex(l=>l.type!==wt);if(o===-1)return r;const s=r[o];delete s.props?.ref;const i=s.props?oe(t,s.props):t,a=xn({...s,props:{}},i);return r.length===1?a:(r[o]=a,r)}}}),Rh=["area","img","input"],Pe=D({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:n}){const r=e.asChild?"template":e.as;return typeof r=="string"&&Rh.includes(r)?()=>_t(r,t):r!=="template"?()=>_t(e.as,t,{default:n.default}):()=>_t(Ps,t,{default:n.default})}});function ha(){const e=G(),t=U(()=>["#text","#comment"].includes(e.value?.$el.nodeName)?e.value?.$el.nextElementSibling:Jn(e));return{primitiveElement:e,currentElement:t}}const[tn,Dh]=gt("DialogRoot");var Ih=D({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,required:!1,default:void 0},defaultOpen:{type:Boolean,required:!1,default:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,o=$r(n,"open",t,{defaultValue:n.defaultOpen,passive:n.open===void 0}),s=G(),i=G(),{modal:a}=qt(n);return Dh({open:o,modal:a,openModal:()=>{o.value=!0},onOpenChange:l=>{o.value=l},onOpenToggle:()=>{o.value=!o.value},contentId:"",titleId:"",descriptionId:"",triggerElement:s,contentElement:i}),(l,c)=>q(l.$slots,"default",{open:f(o),close:()=>o.value=!1})}}),$h=Ih,qh=D({__name:"DialogClose",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e;he();const n=tn();return(r,o)=>(O(),N(f(Pe),oe(t,{type:r.as==="button"?"button":void 0,onClick:o[0]||(o[0]=s=>f(n).onOpenChange(!1))}),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["type"]))}}),Bh=qh;const Fh="dismissableLayer.pointerDownOutside",Lh="dismissableLayer.focusOutside";function Wu(e,t){const n=t.closest("[data-dismissable-layer]"),r=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),o=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&(r===n||o.indexOf(r)<o.indexOf(n)))}function Nh(e,t,n=!0){const r=t?.value?.ownerDocument??globalThis?.document,o=G(!1),s=G(()=>{});return tt(i=>{if(!Et||!Be(n))return;const a=async c=>{const u=c.target;if(!(!t?.value||!u)){if(Wu(t.value,u)){o.value=!1;return}if(c.target&&!o.value){let p=function(){qu(Fh,e,d)};const d={originalEvent:c};c.pointerType==="touch"?(r.removeEventListener("click",s.value),s.value=p,r.addEventListener("click",s.value,{once:!0})):p()}else r.removeEventListener("click",s.value);o.value=!1}},l=window.setTimeout(()=>{r.addEventListener("pointerdown",a)},0);i(()=>{window.clearTimeout(l),r.removeEventListener("pointerdown",a),r.removeEventListener("click",s.value)})}),{onPointerDownCapture:()=>{Be(n)&&(o.value=!0)}}}function jh(e,t,n=!0){const r=t?.value?.ownerDocument??globalThis?.document,o=G(!1);return tt(s=>{if(!Et||!Be(n))return;const i=async a=>{if(!t?.value)return;await Ge(),await Ge();const l=a.target;!t.value||!l||Wu(t.value,l)||a.target&&!o.value&&qu(Lh,e,{originalEvent:a})};r.addEventListener("focusin",i),s(()=>r.removeEventListener("focusin",i))}),{onFocusCapture:()=>{Be(n)&&(o.value=!0)},onBlurCapture:()=>{Be(n)&&(o.value=!1)}}}const Vt=cn({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set});var zh=D({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:t}){const n=e,r=t,{forwardRef:o,currentElement:s}=he(),i=U(()=>s.value?.ownerDocument??globalThis.document),a=U(()=>Vt.layersRoot),l=U(()=>s.value?Array.from(a.value).indexOf(s.value):-1),c=U(()=>Vt.layersWithOutsidePointerEventsDisabled.size>0),u=U(()=>{const g=Array.from(a.value),[y]=[...Vt.layersWithOutsidePointerEventsDisabled].slice(-1),_=g.indexOf(y);return l.value>=_}),d=Nh(async g=>{const y=[...Vt.branches].some(_=>_?.contains(g.target));!u.value||y||(r("pointerDownOutside",g),r("interactOutside",g),await Ge(),g.defaultPrevented||r("dismiss"))},s),p=jh(g=>{[...Vt.branches].some(_=>_?.contains(g.target))||(r("focusOutside",g),r("interactOutside",g),g.defaultPrevented||r("dismiss"))},s);lh("Escape",g=>{l.value===a.value.size-1&&(r("escapeKeyDown",g),g.defaultPrevented||r("dismiss"))});let h;return tt(g=>{s.value&&(n.disableOutsidePointerEvents&&(Vt.layersWithOutsidePointerEventsDisabled.size===0&&(h=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),Vt.layersWithOutsidePointerEventsDisabled.add(s.value)),a.value.add(s.value),g(()=>{n.disableOutsidePointerEvents&&Vt.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=h)}))}),tt(g=>{g(()=>{s.value&&(a.value.delete(s.value),Vt.layersWithOutsidePointerEventsDisabled.delete(s.value))})}),(g,y)=>(O(),N(f(Pe),{ref:f(o),"as-child":g.asChild,as:g.as,"data-dismissable-layer":"",style:mn({pointerEvents:c.value?u.value?"auto":"none":void 0}),onFocusCapture:f(p).onFocusCapture,onBlurCapture:f(p).onBlurCapture,onPointerdownCapture:f(d).onPointerDownCapture},{default:b(()=>[q(g.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),ui=zh;const Vh=Qp(()=>G([]));function Hh(){const e=Vh();return{add(t){const n=e.value[0];t!==n&&n?.pause(),e.value=ma(e.value,t),e.value.unshift(t)},remove(t){e.value=ma(e.value,t),e.value[0]?.resume()}}}function ma(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Uh(e){return e.filter(t=>t.tagName!=="A")}const rs="focusScope.autoFocusOnMount",os="focusScope.autoFocusOnUnmount",ga={bubbles:!1,cancelable:!0};function Wh(e,{select:t=!1}={}){const n=st();for(const r of e)if(on(r,{select:t}),st()!==n)return!0}function Kh(e){const t=Ku(e),n=va(t,e),r=va(t.reverse(),e);return[n,r]}function Ku(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function va(e,t){for(const n of e)if(!Gh(n,{upTo:t}))return n}function Gh(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Yh(e){return e instanceof HTMLInputElement&&"select"in e}function on(e,{select:t=!1}={}){if(e&&e.focus){const n=st();e.focus({preventScroll:!0}),e!==n&&Yh(e)&&t&&e.select()}}var Xh=D({__name:"FocusScope",props:{loop:{type:Boolean,required:!1,default:!1},trapped:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:t}){const n=e,r=t,{currentRef:o,currentElement:s}=he(),i=G(null),a=Hh(),l=cn({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});tt(u=>{if(!Et)return;const d=s.value;if(!n.trapped)return;function p(_){if(l.paused||!d)return;const x=_.target;d.contains(x)?i.value=x:on(i.value,{select:!0})}function h(_){if(l.paused||!d)return;const x=_.relatedTarget;x!==null&&(d.contains(x)||on(i.value,{select:!0}))}function g(_){d.contains(i.value)||on(d)}document.addEventListener("focusin",p),document.addEventListener("focusout",h);const y=new MutationObserver(g);d&&y.observe(d,{childList:!0,subtree:!0}),u(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",h),y.disconnect()})}),tt(async u=>{const d=s.value;if(await Ge(),!d)return;a.add(l);const p=st();if(!d.contains(p)){const g=new CustomEvent(rs,ga);d.addEventListener(rs,y=>r("mountAutoFocus",y)),d.dispatchEvent(g),g.defaultPrevented||(Wh(Uh(Ku(d)),{select:!0}),st()===p&&on(d))}u(()=>{d.removeEventListener(rs,_=>r("mountAutoFocus",_));const g=new CustomEvent(os,ga),y=_=>{r("unmountAutoFocus",_)};d.addEventListener(os,y),d.dispatchEvent(g),setTimeout(()=>{g.defaultPrevented||on(p??document.body,{select:!0}),d.removeEventListener(os,y),a.remove(l)},0)})});function c(u){if(!n.loop&&!n.trapped||l.paused)return;const d=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,p=st();if(d&&p){const h=u.currentTarget,[g,y]=Kh(h);g&&y?!u.shiftKey&&p===y?(u.preventDefault(),n.loop&&on(g,{select:!0})):u.shiftKey&&p===g&&(u.preventDefault(),n.loop&&on(y,{select:!0})):p===h&&u.preventDefault()}}return(u,d)=>(O(),N(f(Pe),{ref_key:"currentRef",ref:o,tabindex:"-1","as-child":u.asChild,as:u.as,onKeydown:c},{default:b(()=>[q(u.$slots,"default")]),_:3},8,["as-child","as"]))}}),Gu=Xh;const Jh="menu.itemSelect",Ms=["Enter"," "],Qh=["ArrowDown","PageUp","Home"],Yu=["ArrowUp","PageDown","End"],Zh=[...Qh,...Yu];[...Ms],[...Ms];function Xu(e){return e?"open":"closed"}function em(e){const t=st();for(const n of e)if(n===t||(n.focus(),st()!==t))return}function tm(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s].x,l=t[s].y,c=t[i].x,u=t[i].y;l>r!=u>r&&n<(c-a)*(r-l)/(u-l)+a&&(o=!o)}return o}function nm(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return tm(n,t)}function Ts(e){return e.pointerType==="mouse"}var rm=D({__name:"DialogContentImpl",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=tn(),{forwardRef:s,currentElement:i}=he();return o.titleId||=Ar(void 0,"reka-dialog-title"),o.descriptionId||=Ar(void 0,"reka-dialog-description"),ht(()=>{o.contentElement=i,st()!==document.body&&(o.triggerElement.value=st())}),(a,l)=>(O(),N(f(Gu),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:l[5]||(l[5]=c=>r("openAutoFocus",c)),onUnmountAutoFocus:l[6]||(l[6]=c=>r("closeAutoFocus",c))},{default:b(()=>[E(f(ui),oe({id:f(o).contentId,ref:f(s),as:a.as,"as-child":a.asChild,"disable-outside-pointer-events":a.disableOutsidePointerEvents,role:"dialog","aria-describedby":f(o).descriptionId,"aria-labelledby":f(o).titleId,"data-state":f(Xu)(f(o).open.value)},a.$attrs,{onDismiss:l[0]||(l[0]=c=>f(o).onOpenChange(!1)),onEscapeKeyDown:l[1]||(l[1]=c=>r("escapeKeyDown",c)),onFocusOutside:l[2]||(l[2]=c=>r("focusOutside",c)),onInteractOutside:l[3]||(l[3]=c=>r("interactOutside",c)),onPointerDownOutside:l[4]||(l[4]=c=>r("pointerDownOutside",c))}),{default:b(()=>[q(a.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),Ju=rm,om=D({__name:"DialogContentModal",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=tn(),s=qr(r),{forwardRef:i,currentElement:a}=he();return Uu(a),(l,c)=>(O(),N(Ju,oe({...n,...f(s)},{ref:f(i),"trap-focus":f(o).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:c[0]||(c[0]=u=>{u.defaultPrevented||(u.preventDefault(),f(o).triggerElement.value?.focus())}),onPointerDownOutside:c[1]||(c[1]=u=>{const d=u.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0;(d.button===2||p)&&u.preventDefault()}),onFocusOutside:c[2]||(c[2]=u=>{u.preventDefault()})}),{default:b(()=>[q(l.$slots,"default")]),_:3},16,["trap-focus"]))}}),sm=om,im=D({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,o=qr(t);he();const s=tn(),i=G(!1),a=G(!1);return(l,c)=>(O(),N(Ju,oe({...n,...f(o)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:c[0]||(c[0]=u=>{u.defaultPrevented||(i.value||f(s).triggerElement.value?.focus(),u.preventDefault()),i.value=!1,a.value=!1}),onInteractOutside:c[1]||(c[1]=u=>{u.defaultPrevented||(i.value=!0,u.detail.originalEvent.type==="pointerdown"&&(a.value=!0));const d=u.target;f(s).triggerElement.value?.contains(d)&&u.preventDefault(),u.detail.originalEvent.type==="focusin"&&a.value&&u.preventDefault()})}),{default:b(()=>[q(l.$slots,"default")]),_:3},16))}}),am=im,lm=D({__name:"DialogContent",props:{forceMount:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=tn(),s=qr(r),{forwardRef:i}=he();return(a,l)=>(O(),N(f(Do),{present:a.forceMount||f(o).open.value},{default:b(()=>[f(o).modal.value?(O(),N(sm,oe({key:0,ref:f(i)},{...n,...f(s),...a.$attrs}),{default:b(()=>[q(a.$slots,"default")]),_:3},16)):(O(),N(am,oe({key:1,ref:f(i)},{...n,...f(s),...a.$attrs}),{default:b(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),um=lm,cm=D({__name:"DialogDescription",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"p"}},setup(e){const t=e;he();const n=tn();return(r,o)=>(O(),N(f(Pe),oe(t,{id:f(n).descriptionId}),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["id"]))}}),dm=cm,fm=D({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=tn();return zu(!0),he(),(n,r)=>(O(),N(f(Pe),{as:n.as,"as-child":n.asChild,"data-state":f(t).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:b(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),pm=fm,hm=D({__name:"DialogOverlay",props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=tn(),{forwardRef:n}=he();return(r,o)=>f(t)?.modal.value?(O(),N(f(Do),{key:0,present:r.forceMount||f(t).open.value},{default:b(()=>[E(pm,oe(r.$attrs,{ref:f(n),as:r.as,"as-child":r.asChild}),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):Rr("v-if",!0)}}),mm=hm,gm=D({__name:"Teleport",props:{to:{type:null,required:!1,default:"body"},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=ih();return(n,r)=>f(t)||n.forceMount?(O(),N(qd,{key:0,to:n.to,disabled:n.disabled,defer:n.defer},[q(n.$slots,"default")],8,["to","disabled","defer"])):Rr("v-if",!0)}}),ci=gm,vm=D({__name:"DialogPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(ci),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),ym=vm,bm=D({__name:"DialogTitle",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"h2"}},setup(e){const t=e,n=tn();return he(),(r,o)=>(O(),N(f(Pe),oe(t,{id:f(n).titleId}),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["id"]))}}),_m=bm;const[Qu,wm]=gt("AvatarRoot");var xm=D({__name:"AvatarRoot",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){return he(),wm({imageLoadingStatus:G("idle")}),(t,n)=>(O(),N(f(Pe),{"as-child":t.asChild,as:t.as},{default:b(()=>[q(t.$slots,"default")]),_:3},8,["as-child","as"]))}}),Cm=xm,Sm=D({__name:"AvatarFallback",props:{delayMs:{type:Number,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){const t=e,n=Qu();he();const r=G(t.delayMs===void 0);return tt(o=>{if(t.delayMs&&Et){const s=window.setTimeout(()=>{r.value=!0},t.delayMs);o(()=>{window.clearTimeout(s)})}}),(o,s)=>r.value&&f(n).imageLoadingStatus.value!=="loaded"?(O(),N(f(Pe),{key:0,"as-child":o.asChild,as:o.as},{default:b(()=>[q(o.$slots,"default")]),_:3},8,["as-child","as"])):Rr("v-if",!0)}}),Em=Sm;function ya(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Am(e,{referrerPolicy:t,crossOrigin:n}={}){const r=G(!1),o=G(null),s=U(()=>r.value?(!o.value&&Et&&(o.value=new window.Image),o.value):null),i=G(ya(s.value,e.value)),a=l=>()=>{r.value&&(i.value=l)};return ht(()=>{r.value=!0,tt(l=>{const c=s.value;if(!c)return;i.value=ya(c,e.value);const u=a("loaded"),d=a("error");c.addEventListener("load",u),c.addEventListener("error",d),t?.value&&(c.referrerPolicy=t.value),typeof n?.value=="string"&&(c.crossOrigin=n.value),l(()=>{c.removeEventListener("load",u),c.removeEventListener("error",d)})})}),Sn(()=>{r.value=!1}),i}var km=D({__name:"AvatarImage",props:{src:{type:String,required:!0},referrerPolicy:{type:null,required:!1},crossOrigin:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"img"}},emits:["loadingStatusChange"],setup(e,{emit:t}){const n=e,r=t,{src:o,referrerPolicy:s,crossOrigin:i}=qt(n);he();const a=Qu(),l=Am(o,{referrerPolicy:s,crossOrigin:i});return $e(l,c=>{r("loadingStatusChange",c),c!=="idle"&&(a.imageLoadingStatus.value=c)},{immediate:!0}),(c,u)=>Ll((O(),N(f(Pe),{role:"img","as-child":c.asChild,as:c.as,src:f(o),"referrer-policy":f(s)},{default:b(()=>[q(c.$slots,"default")]),_:3},8,["as-child","as","src","referrer-policy"])),[[zf,f(l)==="loaded"]])}}),Om=km;const ba="data-reka-collection-item";function Zu(e={}){const{key:t="",isProvider:n=!1}=e,r=`${t}CollectionProvider`;let o;if(n){const u=G(new Map);o={collectionRef:G(),itemMap:u},Vn(r,o)}else o=ot(r);const s=(u=!1)=>{const d=o.collectionRef.value;if(!d)return[];const p=Array.from(d.querySelectorAll(`[${ba}]`)),g=Array.from(o.itemMap.value.values()).sort((y,_)=>p.indexOf(y.ref)-p.indexOf(_.ref));return u?g:g.filter(y=>y.ref.dataset.disabled!=="")},i=D({name:"CollectionSlot",setup(u,{slots:d}){const{primitiveElement:p,currentElement:h}=ha();return $e(h,()=>{o.collectionRef.value=h.value}),()=>_t(Ps,{ref:p},d)}}),a=D({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(u,{slots:d,attrs:p}){const{primitiveElement:h,currentElement:g}=ha();return tt(y=>{if(g.value){const _=Xs(g.value);o.itemMap.value.set(_,{ref:g.value,value:u.value}),y(()=>o.itemMap.value.delete(_))}}),()=>_t(Ps,{...p,[ba]:"",ref:h},d)}}),l=U(()=>Array.from(o.itemMap.value.values())),c=U(()=>o.itemMap.value.size);return{getItems:s,reactiveItems:l,itemMapSize:c,CollectionSlot:i,CollectionItem:a}}const Pm="rovingFocusGroup.onEntryFocus",Mm={bubbles:!1,cancelable:!0};function Tm(e,t=!1){const n=st();for(const r of e)if(r===n||(r.focus({preventScroll:t}),st()!==n))return}const[hw,Rm]=gt("RovingFocusGroup");var Dm=D({__name:"RovingFocusGroup",props:{orientation:{type:String,required:!1,default:void 0},dir:{type:String,required:!1},loop:{type:Boolean,required:!1,default:!1},currentTabStopId:{type:[String,null],required:!1},defaultCurrentTabStopId:{type:String,required:!1},preventScrollOnEntryFocus:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["entryFocus","update:currentTabStopId"],setup(e,{expose:t,emit:n}){const r=e,o=n,{loop:s,orientation:i,dir:a}=qt(r),l=li(a),c=$r(r,"currentTabStopId",o,{defaultValue:r.defaultCurrentTabStopId,passive:r.currentTabStopId===void 0}),u=G(!1),d=G(!1),p=G(0),{getItems:h,CollectionSlot:g}=Zu({isProvider:!0});function y(x){const A=!d.value;if(x.currentTarget&&x.target===x.currentTarget&&A&&!u.value){const S=new CustomEvent(Pm,Mm);if(x.currentTarget.dispatchEvent(S),o("entryFocus",S),!S.defaultPrevented){const C=h().map($=>$.ref).filter($=>$.dataset.disabled!==""),P=C.find($=>$.getAttribute("data-active")===""),H=C.find($=>$.id===c.value),F=[P,H,...C].filter(Boolean);Tm(F,r.preventScrollOnEntryFocus)}}d.value=!1}function _(){setTimeout(()=>{d.value=!1},1)}return t({getItems:h}),Rm({loop:s,dir:l,orientation:i,currentTabStopId:c,onItemFocus:x=>{c.value=x},onItemShiftTab:()=>{u.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(x,A)=>(O(),N(f(g),null,{default:b(()=>[E(f(Pe),{tabindex:u.value||p.value===0?-1:0,"data-orientation":f(i),as:x.as,"as-child":x.asChild,dir:f(l),style:{outline:"none"},onMousedown:A[0]||(A[0]=S=>d.value=!0),onMouseup:_,onFocus:y,onBlur:A[1]||(A[1]=S=>u.value=!1)},{default:b(()=>[q(x.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}}),Im=Dm,$m=D({__name:"VisuallyHidden",props:{feature:{type:String,required:!1,default:"focusable"},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){return(t,n)=>(O(),N(f(Pe),{as:t.as,"as-child":t.asChild,"aria-hidden":t.feature==="focusable"?"true":void 0,"data-hidden":t.feature==="fully-hidden"?"":void 0,tabindex:t.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:b(()=>[q(t.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}}),qm=$m;const[ec,Bm]=gt("PopperRoot");var Fm=D({inheritAttrs:!1,__name:"PopperRoot",setup(e){const t=G();return Bm({anchor:t,onAnchorChange:n=>t.value=n}),(n,r)=>q(n.$slots,"default")}}),tc=Fm,Lm=D({__name:"PopperAnchor",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,{forwardRef:n,currentElement:r}=he(),o=ec();return du(()=>{o.onAnchorChange(t.reference??r.value)}),(s,i)=>(O(),N(f(Pe),{ref:f(n),as:s.as,"as-child":s.asChild},{default:b(()=>[q(s.$slots,"default")]),_:3},8,["as","as-child"]))}}),nc=Lm;const Nm={key:0,d:"M0 0L6 6L12 0"},jm={key:1,d:"M0 0L4.58579 4.58579C5.36683 5.36683 6.63316 5.36684 7.41421 4.58579L12 0"};var zm=D({__name:"Arrow",props:{width:{type:Number,required:!1,default:10},height:{type:Number,required:!1,default:5},rounded:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const t=e;return he(),(n,r)=>(O(),N(f(Pe),oe(t,{width:n.width,height:n.height,viewBox:n.asChild?void 0:"0 0 12 6",preserveAspectRatio:n.asChild?void 0:"none"}),{default:b(()=>[q(n.$slots,"default",{},()=>[n.rounded?(O(),pe("path",jm)):(O(),pe("path",Nm))])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}}),Vm=zm;function Hm(e){return e!==null}function Um(e){return{name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=Rs(n),d={start:"0%",center:"50%",end:"100%"}[u],p=(o.arrow?.x??0)+a/2,h=(o.arrow?.y??0)+l/2;let g="",y="";return c==="bottom"?(g=i?d:`${p}px`,y=`${-l}px`):c==="top"?(g=i?d:`${p}px`,y=`${r.floating.height+l}px`):c==="right"?(g=`${-l}px`,y=i?d:`${h}px`):c==="left"&&(g=`${r.floating.width+l}px`,y=i?d:`${h}px`),{data:{x:g,y}}}}}function Rs(e){const[t,n="center"]=e.split("-");return[t,n]}const Wm=["top","right","bottom","left"],fn=Math.min,dt=Math.max,po=Math.round,Gr=Math.floor,$t=e=>({x:e,y:e}),Km={left:"right",right:"left",bottom:"top",top:"bottom"},Gm={start:"end",end:"start"};function Ds(e,t,n){return dt(e,fn(t,n))}function Jt(e,t){return typeof e=="function"?e(t):e}function Qt(e){return e.split("-")[0]}function Qn(e){return e.split("-")[1]}function di(e){return e==="x"?"y":"x"}function fi(e){return e==="y"?"height":"width"}const Ym=new Set(["top","bottom"]);function Dt(e){return Ym.has(Qt(e))?"y":"x"}function pi(e){return di(Dt(e))}function Xm(e,t,n){n===void 0&&(n=!1);const r=Qn(e),o=pi(e),s=fi(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=ho(i)),[i,ho(i)]}function Jm(e){const t=ho(e);return[Is(e),t,Is(t)]}function Is(e){return e.replace(/start|end/g,t=>Gm[t])}const _a=["left","right"],wa=["right","left"],Qm=["top","bottom"],Zm=["bottom","top"];function eg(e,t,n){switch(e){case"top":case"bottom":return n?t?wa:_a:t?_a:wa;case"left":case"right":return t?Qm:Zm;default:return[]}}function tg(e,t,n,r){const o=Qn(e);let s=eg(Qt(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Is)))),s}function ho(e){return e.replace(/left|right|bottom|top/g,t=>Km[t])}function ng(e){return{top:0,right:0,bottom:0,left:0,...e}}function rc(e){return typeof e!="number"?ng(e):{top:e,right:e,bottom:e,left:e}}function mo(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function xa(e,t,n){let{reference:r,floating:o}=e;const s=Dt(t),i=pi(t),a=fi(i),l=Qt(t),c=s==="y",u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,p=r[a]/2-o[a]/2;let h;switch(l){case"top":h={x:u,y:r.y-o.height};break;case"bottom":h={x:u,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-o.width,y:d};break;default:h={x:r.x,y:r.y}}switch(Qn(t)){case"start":h[i]-=p*(n&&c?-1:1);break;case"end":h[i]+=p*(n&&c?-1:1);break}return h}const rg=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=xa(c,r,l),p=r,h={},g=0;for(let y=0;y<a.length;y++){const{name:_,fn:x}=a[y],{x:A,y:S,data:C,reset:P}=await x({x:u,y:d,initialPlacement:r,placement:p,strategy:o,middlewareData:h,rects:c,platform:i,elements:{reference:e,floating:t}});u=A??u,d=S??d,h={...h,[_]:{...h[_],...C}},P&&g<=50&&(g++,typeof P=="object"&&(P.placement&&(p=P.placement),P.rects&&(c=P.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):P.rects),{x:u,y:d}=xa(c,p,l)),y=-1)}return{x:u,y:d,placement:p,strategy:o,middlewareData:h}};async function kr(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=Jt(t,e),g=rc(h),_=a[p?d==="floating"?"reference":"floating":d],x=mo(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(_)))==null||n?_:_.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),A=d==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,S=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),C=await(s.isElement==null?void 0:s.isElement(S))?await(s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},P=mo(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:A,offsetParent:S,strategy:l}):A);return{top:(x.top-P.top+g.top)/C.y,bottom:(P.bottom-x.bottom+g.bottom)/C.y,left:(x.left-P.left+g.left)/C.x,right:(P.right-x.right+g.right)/C.x}}const og=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:u=0}=Jt(e,t)||{};if(c==null)return{};const d=rc(u),p={x:n,y:r},h=pi(o),g=fi(h),y=await i.getDimensions(c),_=h==="y",x=_?"top":"left",A=_?"bottom":"right",S=_?"clientHeight":"clientWidth",C=s.reference[g]+s.reference[h]-p[h]-s.floating[g],P=p[h]-s.reference[h],H=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let F=H?H[S]:0;(!F||!await(i.isElement==null?void 0:i.isElement(H)))&&(F=a.floating[S]||s.floating[g]);const $=C/2-P/2,T=F/2-y[g]/2-1,Z=fn(d[x],T),ae=fn(d[A],T),fe=Z,Ce=F-y[g]-ae,be=F/2-y[g]/2+$,Ee=Ds(fe,be,Ce),ue=!l.arrow&&Qn(o)!=null&&be!==Ee&&s.reference[g]/2-(be<fe?Z:ae)-y[g]/2<0,re=ue?be<fe?be-fe:be-Ce:0;return{[h]:p[h]+re,data:{[h]:Ee,centerOffset:be-Ee-re,...ue&&{alignmentOffset:re}},reset:ue}}}),sg=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,..._}=Jt(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=Qt(o),A=Dt(a),S=Qt(a)===a,C=await(l.isRTL==null?void 0:l.isRTL(c.floating)),P=p||(S||!y?[ho(a)]:Jm(a)),H=g!=="none";!p&&H&&P.push(...tg(a,y,g,C));const F=[a,...P],$=await kr(t,_),T=[];let Z=((r=s.flip)==null?void 0:r.overflows)||[];if(u&&T.push($[x]),d){const be=Xm(o,i,C);T.push($[be[0]],$[be[1]])}if(Z=[...Z,{placement:o,overflows:T}],!T.every(be=>be<=0)){var ae,fe;const be=(((ae=s.flip)==null?void 0:ae.index)||0)+1,Ee=F[be];if(Ee&&(!(d==="alignment"?A!==Dt(Ee):!1)||Z.every(B=>Dt(B.placement)===A?B.overflows[0]>0:!0)))return{data:{index:be,overflows:Z},reset:{placement:Ee}};let ue=(fe=Z.filter(re=>re.overflows[0]<=0).sort((re,B)=>re.overflows[1]-B.overflows[1])[0])==null?void 0:fe.placement;if(!ue)switch(h){case"bestFit":{var Ce;const re=(Ce=Z.filter(B=>{if(H){const qe=Dt(B.placement);return qe===A||qe==="y"}return!0}).map(B=>[B.placement,B.overflows.filter(qe=>qe>0).reduce((qe,lt)=>qe+lt,0)]).sort((B,qe)=>B[1]-qe[1])[0])==null?void 0:Ce[0];re&&(ue=re);break}case"initialPlacement":ue=a;break}if(o!==ue)return{reset:{placement:ue}}}return{}}}};function Ca(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Sa(e){return Wm.some(t=>e[t]>=0)}const ig=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Jt(e,t);switch(r){case"referenceHidden":{const s=await kr(t,{...o,elementContext:"reference"}),i=Ca(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:Sa(i)}}}case"escaped":{const s=await kr(t,{...o,altBoundary:!0}),i=Ca(s,n.floating);return{data:{escapedOffsets:i,escaped:Sa(i)}}}default:return{}}}}},oc=new Set(["left","top"]);async function ag(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=Qt(n),a=Qn(n),l=Dt(n)==="y",c=oc.has(i)?-1:1,u=s&&l?-1:1,d=Jt(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:g}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof g=="number"&&(h=a==="end"?g*-1:g),l?{x:h*u,y:p*c}:{x:p*c,y:h*u}}const lg=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await ag(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},ug=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:_=>{let{x,y:A}=_;return{x,y:A}}},...l}=Jt(e,t),c={x:n,y:r},u=await kr(t,l),d=Dt(Qt(o)),p=di(d);let h=c[p],g=c[d];if(s){const _=p==="y"?"top":"left",x=p==="y"?"bottom":"right",A=h+u[_],S=h-u[x];h=Ds(A,h,S)}if(i){const _=d==="y"?"top":"left",x=d==="y"?"bottom":"right",A=g+u[_],S=g-u[x];g=Ds(A,g,S)}const y=a.fn({...t,[p]:h,[d]:g});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[p]:s,[d]:i}}}}}},cg=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=Jt(e,t),u={x:n,y:r},d=Dt(o),p=di(d);let h=u[p],g=u[d];const y=Jt(a,t),_=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(l){const S=p==="y"?"height":"width",C=s.reference[p]-s.floating[S]+_.mainAxis,P=s.reference[p]+s.reference[S]-_.mainAxis;h<C?h=C:h>P&&(h=P)}if(c){var x,A;const S=p==="y"?"width":"height",C=oc.has(Qt(o)),P=s.reference[d]-s.floating[S]+(C&&((x=i.offset)==null?void 0:x[d])||0)+(C?0:_.crossAxis),H=s.reference[d]+s.reference[S]+(C?0:((A=i.offset)==null?void 0:A[d])||0)-(C?_.crossAxis:0);g<P?g=P:g>H&&(g=H)}return{[p]:h,[d]:g}}}},dg=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...c}=Jt(e,t),u=await kr(t,c),d=Qt(o),p=Qn(o),h=Dt(o)==="y",{width:g,height:y}=s.floating;let _,x;d==="top"||d==="bottom"?(_=d,x=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=d,_=p==="end"?"top":"bottom");const A=y-u.top-u.bottom,S=g-u.left-u.right,C=fn(y-u[_],A),P=fn(g-u[x],S),H=!t.middlewareData.shift;let F=C,$=P;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&($=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(F=A),H&&!p){const Z=dt(u.left,0),ae=dt(u.right,0),fe=dt(u.top,0),Ce=dt(u.bottom,0);h?$=g-2*(Z!==0||ae!==0?Z+ae:dt(u.left,u.right)):F=y-2*(fe!==0||Ce!==0?fe+Ce:dt(u.top,u.bottom))}await l({...t,availableWidth:$,availableHeight:F});const T=await i.getDimensions(a.floating);return g!==T.width||y!==T.height?{reset:{rects:!0}}:{}}}};function Io(){return typeof window<"u"}function En(e){return hi(e)?(e.nodeName||"").toLowerCase():"#document"}function pt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Lt(e){var t;return(t=(hi(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function hi(e){return Io()?e instanceof Node||e instanceof pt(e).Node:!1}function xt(e){return Io()?e instanceof Element||e instanceof pt(e).Element:!1}function Ft(e){return Io()?e instanceof HTMLElement||e instanceof pt(e).HTMLElement:!1}function Ea(e){return!Io()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof pt(e).ShadowRoot}const fg=new Set(["inline","contents"]);function Fr(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ct(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!fg.has(o)}const pg=new Set(["table","td","th"]);function hg(e){return pg.has(En(e))}const mg=[":popover-open",":modal"];function $o(e){return mg.some(t=>{try{return e.matches(t)}catch{return!1}})}const gg=["transform","translate","scale","rotate","perspective"],vg=["transform","translate","scale","rotate","perspective","filter"],yg=["paint","layout","strict","content"];function mi(e){const t=gi(),n=xt(e)?Ct(e):e;return gg.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||vg.some(r=>(n.willChange||"").includes(r))||yg.some(r=>(n.contain||"").includes(r))}function bg(e){let t=pn(e);for(;Ft(t)&&!Wn(t);){if(mi(t))return t;if($o(t))return null;t=pn(t)}return null}function gi(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const _g=new Set(["html","body","#document"]);function Wn(e){return _g.has(En(e))}function Ct(e){return pt(e).getComputedStyle(e)}function qo(e){return xt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function pn(e){if(En(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ea(e)&&e.host||Lt(e);return Ea(t)?t.host:t}function sc(e){const t=pn(e);return Wn(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ft(t)&&Fr(t)?t:sc(t)}function Or(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=sc(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=pt(o);if(s){const a=$s(i);return t.concat(i,i.visualViewport||[],Fr(o)?o:[],a&&n?Or(a):[])}return t.concat(o,Or(o,[],n))}function $s(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ic(e){const t=Ct(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ft(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=po(n)!==s||po(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function vi(e){return xt(e)?e:e.contextElement}function Hn(e){const t=vi(e);if(!Ft(t))return $t(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=ic(t);let i=(s?po(n.width):n.width)/r,a=(s?po(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const wg=$t(0);function ac(e){const t=pt(e);return!gi()||!t.visualViewport?wg:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function xg(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==pt(e)?!1:t}function Cn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=vi(e);let i=$t(1);t&&(r?xt(r)&&(i=Hn(r)):i=Hn(e));const a=xg(s,n,r)?ac(s):$t(0);let l=(o.left+a.x)/i.x,c=(o.top+a.y)/i.y,u=o.width/i.x,d=o.height/i.y;if(s){const p=pt(s),h=r&&xt(r)?pt(r):r;let g=p,y=$s(g);for(;y&&r&&h!==g;){const _=Hn(y),x=y.getBoundingClientRect(),A=Ct(y),S=x.left+(y.clientLeft+parseFloat(A.paddingLeft))*_.x,C=x.top+(y.clientTop+parseFloat(A.paddingTop))*_.y;l*=_.x,c*=_.y,u*=_.x,d*=_.y,l+=S,c+=C,g=pt(y),y=$s(g)}}return mo({width:u,height:d,x:l,y:c})}function yi(e,t){const n=qo(e).scrollLeft;return t?t.left+n:Cn(Lt(e)).left+n}function lc(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:yi(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function Cg(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Lt(r),a=t?$o(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},c=$t(1);const u=$t(0),d=Ft(r);if((d||!d&&!s)&&((En(r)!=="body"||Fr(i))&&(l=qo(r)),Ft(r))){const h=Cn(r);c=Hn(r),u.x=h.x+r.clientLeft,u.y=h.y+r.clientTop}const p=i&&!d&&!s?lc(i,l,!0):$t(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-l.scrollTop*c.y+u.y+p.y}}function Sg(e){return Array.from(e.getClientRects())}function Eg(e){const t=Lt(e),n=qo(e),r=e.ownerDocument.body,o=dt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=dt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+yi(e);const a=-n.scrollTop;return Ct(r).direction==="rtl"&&(i+=dt(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function Ag(e,t){const n=pt(e),r=Lt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const c=gi();(!c||c&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}const kg=new Set(["absolute","fixed"]);function Og(e,t){const n=Cn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Ft(e)?Hn(e):$t(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,c=r*s.y;return{width:i,height:a,x:l,y:c}}function Aa(e,t,n){let r;if(t==="viewport")r=Ag(e,n);else if(t==="document")r=Eg(Lt(e));else if(xt(t))r=Og(t,n);else{const o=ac(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return mo(r)}function uc(e,t){const n=pn(e);return n===t||!xt(n)||Wn(n)?!1:Ct(n).position==="fixed"||uc(n,t)}function Pg(e,t){const n=t.get(e);if(n)return n;let r=Or(e,[],!1).filter(a=>xt(a)&&En(a)!=="body"),o=null;const s=Ct(e).position==="fixed";let i=s?pn(e):e;for(;xt(i)&&!Wn(i);){const a=Ct(i),l=mi(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&kg.has(o.position)||Fr(i)&&!l&&uc(e,i))?r=r.filter(u=>u!==i):o=a,i=pn(i)}return t.set(e,r),r}function Mg(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?$o(t)?[]:Pg(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((c,u)=>{const d=Aa(t,u,o);return c.top=dt(d.top,c.top),c.right=fn(d.right,c.right),c.bottom=fn(d.bottom,c.bottom),c.left=dt(d.left,c.left),c},Aa(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Tg(e){const{width:t,height:n}=ic(e);return{width:t,height:n}}function Rg(e,t,n){const r=Ft(t),o=Lt(t),s=n==="fixed",i=Cn(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=$t(0);function c(){l.x=yi(o)}if(r||!r&&!s)if((En(t)!=="body"||Fr(o))&&(a=qo(t)),r){const h=Cn(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else o&&c();s&&!r&&o&&c();const u=o&&!r&&!s?lc(o,a):$t(0),d=i.left+a.scrollLeft-l.x-u.x,p=i.top+a.scrollTop-l.y-u.y;return{x:d,y:p,width:i.width,height:i.height}}function ss(e){return Ct(e).position==="static"}function ka(e,t){if(!Ft(e)||Ct(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Lt(e)===n&&(n=n.ownerDocument.body),n}function cc(e,t){const n=pt(e);if($o(e))return n;if(!Ft(e)){let o=pn(e);for(;o&&!Wn(o);){if(xt(o)&&!ss(o))return o;o=pn(o)}return n}let r=ka(e,t);for(;r&&hg(r)&&ss(r);)r=ka(r,t);return r&&Wn(r)&&ss(r)&&!mi(r)?n:r||bg(e)||n}const Dg=async function(e){const t=this.getOffsetParent||cc,n=this.getDimensions,r=await n(e.floating);return{reference:Rg(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ig(e){return Ct(e).direction==="rtl"}const $g={convertOffsetParentRelativeRectToViewportRelativeRect:Cg,getDocumentElement:Lt,getClippingRect:Mg,getOffsetParent:cc,getElementRects:Dg,getClientRects:Sg,getDimensions:Tg,getScale:Hn,isElement:xt,isRTL:Ig};function dc(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function qg(e,t){let n=null,r;const o=Lt(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const c=e.getBoundingClientRect(),{left:u,top:d,width:p,height:h}=c;if(a||t(),!p||!h)return;const g=Gr(d),y=Gr(o.clientWidth-(u+p)),_=Gr(o.clientHeight-(d+h)),x=Gr(u),S={rootMargin:-g+"px "+-y+"px "+-_+"px "+-x+"px",threshold:dt(0,fn(1,l))||1};let C=!0;function P(H){const F=H[0].intersectionRatio;if(F!==l){if(!C)return i();F?i(!1,F):r=setTimeout(()=>{i(!1,1e-7)},1e3)}F===1&&!dc(c,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(P,{...S,root:o.ownerDocument})}catch{n=new IntersectionObserver(P,S)}n.observe(e)}return i(!0),s}function Bg(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=vi(e),u=o||s?[...c?Or(c):[],...Or(t)]:[];u.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const d=c&&a?qg(c,n):null;let p=-1,h=null;i&&(h=new ResizeObserver(x=>{let[A]=x;A&&A.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var S;(S=h)==null||S.observe(t)})),n()}),c&&!l&&h.observe(c),h.observe(t));let g,y=l?Cn(e):null;l&&_();function _(){const x=Cn(e);y&&!dc(y,x)&&n(),y=x,g=requestAnimationFrame(_)}return n(),()=>{var x;u.forEach(A=>{o&&A.removeEventListener("scroll",n),s&&A.removeEventListener("resize",n)}),d?.(),(x=h)==null||x.disconnect(),h=null,l&&cancelAnimationFrame(g)}}const Fg=lg,Lg=ug,Oa=sg,Ng=dg,jg=ig,zg=og,Vg=cg,Hg=(e,t,n)=>{const r=new Map,o={platform:$g,...n},s={...o.platform,_c:r};return rg(e,t,{...o,platform:s})};function Ug(e){return e!=null&&typeof e=="object"&&"$el"in e}function qs(e){if(Ug(e)){const t=e.$el;return hi(t)&&En(t)==="#comment"?null:t}return e}function qn(e){return typeof e=="function"?e():f(e)}function Wg(e){return{name:"arrow",options:e,fn(t){const n=qs(qn(e.element));return n==null?{}:zg({element:n,padding:e.padding}).fn(t)}}}function fc(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Pa(e,t){const n=fc(e);return Math.round(t*n)/n}function Kg(e,t,n){n===void 0&&(n={});const r=n.whileElementsMounted,o=U(()=>{var F;return(F=qn(n.open))!=null?F:!0}),s=U(()=>qn(n.middleware)),i=U(()=>{var F;return(F=qn(n.placement))!=null?F:"bottom"}),a=U(()=>{var F;return(F=qn(n.strategy))!=null?F:"absolute"}),l=U(()=>{var F;return(F=qn(n.transform))!=null?F:!0}),c=U(()=>qs(e.value)),u=U(()=>qs(t.value)),d=G(0),p=G(0),h=G(a.value),g=G(i.value),y=Gt({}),_=G(!1),x=U(()=>{const F={position:h.value,left:"0",top:"0"};if(!u.value)return F;const $=Pa(u.value,d.value),T=Pa(u.value,p.value);return l.value?{...F,transform:"translate("+$+"px, "+T+"px)",...fc(u.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:$+"px",top:T+"px"}});let A;function S(){if(c.value==null||u.value==null)return;const F=o.value;Hg(c.value,u.value,{middleware:s.value,placement:i.value,strategy:a.value}).then($=>{d.value=$.x,p.value=$.y,h.value=$.strategy,g.value=$.placement,y.value=$.middlewareData,_.value=F!==!1})}function C(){typeof A=="function"&&(A(),A=void 0)}function P(){if(C(),r===void 0){S();return}if(c.value!=null&&u.value!=null){A=r(c.value,u.value,S);return}}function H(){o.value||(_.value=!1)}return $e([s,i,a,o],S,{flush:"sync"}),$e([c,u],P,{flush:"sync"}),$e(o,H,{flush:"sync"}),wo()&&Us(C),{x:kn(d),y:kn(p),strategy:kn(h),placement:kn(g),middlewareData:kn(y),isPositioned:kn(_),floatingStyles:x,update:S}}const pc={side:"bottom",sideOffset:0,sideFlip:!0,align:"center",alignOffset:0,alignFlip:!0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[Gg,Yg]=gt("PopperContent");var Xg=D({inheritAttrs:!1,__name:"PopperContent",props:Jl({side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...pc}),emits:["placed"],setup(e,{emit:t}){const n=e,r=t,o=ec(),{forwardRef:s,currentElement:i}=he(),a=G(),l=G(),{width:c,height:u}=Ah(l),d=U(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),p=U(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),h=U(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),g=U(()=>({padding:p.value,boundary:h.value.filter(Hm),altBoundary:h.value.length>0})),y=U(()=>({mainAxis:n.sideFlip,crossAxis:n.alignFlip})),_=Xp(()=>[Fg({mainAxis:n.sideOffset+u.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&Oa({...g.value,...y.value}),n.avoidCollisions&&Lg({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?Vg():void 0,...g.value}),!n.prioritizePosition&&n.avoidCollisions&&Oa({...g.value,...y.value}),Ng({...g.value,apply:({elements:fe,rects:Ce,availableWidth:be,availableHeight:Ee})=>{const{width:ue,height:re}=Ce.reference,B=fe.floating.style;B.setProperty("--reka-popper-available-width",`${be}px`),B.setProperty("--reka-popper-available-height",`${Ee}px`),B.setProperty("--reka-popper-anchor-width",`${ue}px`),B.setProperty("--reka-popper-anchor-height",`${re}px`)}}),l.value&&Wg({element:l.value,padding:n.arrowPadding}),Um({arrowWidth:c.value,arrowHeight:u.value}),n.hideWhenDetached&&jg({strategy:"referenceHidden",...g.value})]),x=U(()=>n.reference??o.anchor.value),{floatingStyles:A,placement:S,isPositioned:C,middlewareData:P}=Kg(x,a,{strategy:n.positionStrategy,placement:d,whileElementsMounted:(...fe)=>Bg(...fe,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy==="always"}),middleware:_}),H=U(()=>Rs(S.value)[0]),F=U(()=>Rs(S.value)[1]);du(()=>{C.value&&r("placed")});const $=U(()=>P.value.arrow?.centerOffset!==0),T=G("");tt(()=>{i.value&&(T.value=window.getComputedStyle(i.value).zIndex)});const Z=U(()=>P.value.arrow?.x??0),ae=U(()=>P.value.arrow?.y??0);return Yg({placedSide:H,onArrowChange:fe=>l.value=fe,arrowX:Z,arrowY:ae,shouldHideArrow:$}),(fe,Ce)=>(O(),pe("div",{ref_key:"floatingRef",ref:a,"data-reka-popper-content-wrapper":"",style:mn({...f(A),transform:f(C)?f(A).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:T.value,"--reka-popper-transform-origin":[f(P).transformOrigin?.x,f(P).transformOrigin?.y].join(" "),...f(P).hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}})},[E(f(Pe),oe({ref:f(s)},fe.$attrs,{"as-child":n.asChild,as:fe.as,"data-side":H.value,"data-align":F.value,style:{animation:f(C)?void 0:"none"}}),{default:b(()=>[q(fe.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4))}}),hc=Xg;const Jg={top:"bottom",right:"left",bottom:"top",left:"right"};var Qg=D({inheritAttrs:!1,__name:"PopperArrow",props:{width:{type:Number,required:!1},height:{type:Number,required:!1},rounded:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const{forwardRef:t}=he(),n=Gg(),r=U(()=>Jg[n.placedSide.value]);return(o,s)=>(O(),pe("span",{ref:i=>{f(n).onArrowChange(i)},style:mn({position:"absolute",left:f(n).arrowX?.value?`${f(n).arrowX?.value}px`:void 0,top:f(n).arrowY?.value?`${f(n).arrowY?.value}px`:void 0,[r.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f(n).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f(n).placedSide.value],visibility:f(n).shouldHideArrow.value?"hidden":void 0})},[E(Vm,oe(o.$attrs,{ref:f(t),style:{display:"block"},as:o.as,"as-child":o.asChild,rounded:o.rounded,width:o.width,height:o.height}),{default:b(()=>[q(o.$slots,"default")]),_:3},16,["as","as-child","rounded","width","height"])],4))}}),Zg=Qg,ev=D({__name:"MenuAnchor",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(nc),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),tv=ev;function nv(){const e=G(!1);return ht(()=>{Un("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),Un(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const rv=Lu(nv),[Bo,ov]=gt(["MenuRoot","MenuSub"],"MenuContext"),[bi,sv]=gt("MenuRoot");var iv=D({__name:"MenuRoot",props:{open:{type:Boolean,required:!1,default:!1},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,{modal:o,dir:s}=qt(n),i=li(s),a=$r(n,"open",r),l=G(),c=rv();return ov({open:a,onOpenChange:u=>{a.value=u},content:l,onContentChange:u=>{l.value=u}}),sv({onClose:()=>{a.value=!1},isUsingKeyboardRef:c,dir:i,modal:o}),(u,d)=>(O(),N(f(tc),null,{default:b(()=>[q(u.$slots,"default")]),_:3}))}}),av=iv;const[mc,lv]=gt("MenuContent");var uv=D({__name:"MenuContentImpl",props:Jl({loop:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},disableOutsideScroll:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...pc}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:t}){const n=e,r=t,o=Bo(),s=bi(),{trapFocus:i,disableOutsidePointerEvents:a,loop:l}=qt(n);hh(),zu(a.value);const c=G(""),u=G(0),d=G(0),p=G(null),h=G("right"),g=G(0),y=G(null),_=G(),{forwardRef:x,currentElement:A}=he(),{handleTypeaheadSearch:S}=Oh();$e(A,T=>{o.onContentChange(T)}),Sn(()=>{window.clearTimeout(u.value)});function C(T){return h.value===p.value?.side&&nm(T,p.value?.area)}async function P(T){r("openAutoFocus",T),!T.defaultPrevented&&(T.preventDefault(),A.value?.focus({preventScroll:!0}))}function H(T){if(T.defaultPrevented)return;const ae=T.target.closest("[data-reka-menu-content]")===T.currentTarget,fe=T.ctrlKey||T.altKey||T.metaKey,Ce=T.key.length===1,be=Yp(T,st(),A.value,{loop:l.value,arrowKeyOptions:"vertical",dir:s?.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(be)return be?.focus();if(T.code==="Space")return;const Ee=_.value?.getItems()??[];if(ae&&(T.key==="Tab"&&T.preventDefault(),!fe&&Ce&&S(T.key,Ee)),T.target!==A.value||!Zh.includes(T.key))return;T.preventDefault();const ue=[...Ee.map(re=>re.ref)];Yu.includes(T.key)&&ue.reverse(),em(ue)}function F(T){T?.currentTarget?.contains?.(T.target)||(window.clearTimeout(u.value),c.value="")}function $(T){if(!Ts(T))return;const Z=T.target,ae=g.value!==T.clientX;if(T?.currentTarget?.contains(Z)&&ae){const fe=T.clientX>g.value?"right":"left";h.value=fe,g.value=T.clientX}}return lv({onItemEnter:T=>!!C(T),onItemLeave:T=>{C(T)||(A.value?.focus(),y.value=null)},onTriggerLeave:T=>!!C(T),searchRef:c,pointerGraceTimerRef:d,onPointerGraceIntentChange:T=>{p.value=T}}),(T,Z)=>(O(),N(f(Gu),{"as-child":"",trapped:f(i),onMountAutoFocus:P,onUnmountAutoFocus:Z[7]||(Z[7]=ae=>r("closeAutoFocus",ae))},{default:b(()=>[E(f(ui),{"as-child":"","disable-outside-pointer-events":f(a),onEscapeKeyDown:Z[2]||(Z[2]=ae=>r("escapeKeyDown",ae)),onPointerDownOutside:Z[3]||(Z[3]=ae=>r("pointerDownOutside",ae)),onFocusOutside:Z[4]||(Z[4]=ae=>r("focusOutside",ae)),onInteractOutside:Z[5]||(Z[5]=ae=>r("interactOutside",ae)),onDismiss:Z[6]||(Z[6]=ae=>r("dismiss"))},{default:b(()=>[E(f(Im),{ref_key:"rovingFocusGroupRef",ref:_,"current-tab-stop-id":y.value,"onUpdate:currentTabStopId":Z[0]||(Z[0]=ae=>y.value=ae),"as-child":"",orientation:"vertical",dir:f(s).dir.value,loop:f(l),onEntryFocus:Z[1]||(Z[1]=ae=>{r("entryFocus",ae),f(s).isUsingKeyboardRef.value||ae.preventDefault()})},{default:b(()=>[E(f(hc),{ref:f(x),role:"menu",as:T.as,"as-child":T.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":f(Xu)(f(o).open.value),dir:f(s).dir.value,side:T.side,"side-offset":T.sideOffset,align:T.align,"align-offset":T.alignOffset,"avoid-collisions":T.avoidCollisions,"collision-boundary":T.collisionBoundary,"collision-padding":T.collisionPadding,"arrow-padding":T.arrowPadding,"prioritize-position":T.prioritizePosition,"position-strategy":T.positionStrategy,"update-position-strategy":T.updatePositionStrategy,sticky:T.sticky,"hide-when-detached":T.hideWhenDetached,reference:T.reference,onKeydown:H,onBlur:F,onPointermove:$},{default:b(()=>[q(T.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),gc=uv,cv=D({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,n=mc(),{forwardRef:r}=he(),{CollectionItem:o}=Zu(),s=G(!1);async function i(l){l.defaultPrevented||Ts(l)&&(t.disabled?n.onItemLeave(l):n.onItemEnter(l)||l.currentTarget?.focus({preventScroll:!0}))}async function a(l){await Ge(),!l.defaultPrevented&&Ts(l)&&n.onItemLeave(l)}return(l,c)=>(O(),N(f(o),{value:{textValue:l.textValue}},{default:b(()=>[E(f(Pe),oe({ref:f(r),role:"menuitem",tabindex:"-1"},l.$attrs,{as:l.as,"as-child":l.asChild,"aria-disabled":l.disabled||void 0,"data-disabled":l.disabled?"":void 0,"data-highlighted":s.value?"":void 0,onPointermove:i,onPointerleave:a,onFocus:c[0]||(c[0]=async u=>{await Ge(),!(u.defaultPrevented||l.disabled)&&(s.value=!0)}),onBlur:c[1]||(c[1]=async u=>{await Ge(),!u.defaultPrevented&&(s.value=!1)})}),{default:b(()=>[q(l.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),dv=cv,fv=D({__name:"MenuItem",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["select"],setup(e,{emit:t}){const n=e,r=t,{forwardRef:o,currentElement:s}=he(),i=bi(),a=mc(),l=G(!1);async function c(){const u=s.value;if(!n.disabled&&u){const d=new CustomEvent(Jh,{bubbles:!0,cancelable:!0});r("select",d),await Ge(),d.defaultPrevented?l.value=!1:i.onClose()}}return(u,d)=>(O(),N(dv,oe(n,{ref:f(o),onClick:c,onPointerdown:d[0]||(d[0]=()=>{l.value=!0}),onPointerup:d[1]||(d[1]=async p=>{await Ge(),!p.defaultPrevented&&(l.value||p.currentTarget?.click())}),onKeydown:d[2]||(d[2]=async p=>{const h=f(a).searchRef.value!=="";u.disabled||h&&p.key===" "||f(Ms).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:b(()=>[q(u.$slots,"default")]),_:3},16))}}),pv=fv,hv=D({__name:"MenuRootContentModal",props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=At(n,r),s=Bo(),{forwardRef:i,currentElement:a}=he();return Uu(a),(l,c)=>(O(),N(gc,oe(f(o),{ref:f(i),"trap-focus":f(s).open.value,"disable-outside-pointer-events":f(s).open.value,"disable-outside-scroll":!0,onDismiss:c[0]||(c[0]=u=>f(s).onOpenChange(!1)),onFocusOutside:c[1]||(c[1]=xu(u=>r("focusOutside",u),["prevent"]))}),{default:b(()=>[q(l.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),mv=hv,gv=D({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const o=At(e,t),s=Bo();return(i,a)=>(O(),N(gc,oe(f(o),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:a[0]||(a[0]=l=>f(s).onOpenChange(!1))}),{default:b(()=>[q(i.$slots,"default")]),_:3},16))}}),vv=gv,yv=D({__name:"MenuContent",props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const o=At(e,t),s=Bo(),i=bi();return(a,l)=>(O(),N(f(Do),{present:a.forceMount||f(s).open.value},{default:b(()=>[f(i).modal.value?(O(),N(mv,et(oe({key:0},{...a.$attrs,...f(o)})),{default:b(()=>[q(a.$slots,"default")]),_:3},16)):(O(),N(vv,et(oe({key:1},{...a.$attrs,...f(o)})),{default:b(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),bv=yv,_v=D({__name:"MenuLabel",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"div"}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),wv=_v,xv=D({__name:"MenuPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(ci),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Cv=xv,Sv=D({__name:"MenuSeparator",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),oe(t,{role:"separator","aria-orientation":"horizontal"}),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Ev=Sv;const[vc,Av]=gt("DropdownMenuRoot");var kv=D({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean,required:!1},open:{type:Boolean,required:!1,default:void 0},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t;he();const o=$r(n,"open",r,{defaultValue:n.defaultOpen,passive:n.open===void 0}),s=G(),{modal:i,dir:a}=qt(n),l=li(a);return Av({open:o,onOpenChange:c=>{o.value=c},onOpenToggle:()=>{o.value=!o.value},triggerId:"",triggerElement:s,contentId:"",modal:i,dir:l}),(c,u)=>(O(),N(f(av),{open:f(o),"onUpdate:open":u[0]||(u[0]=d=>Re(o)?o.value=d:null),dir:f(l),modal:f(i)},{default:b(()=>[q(c.$slots,"default",{open:f(o)})]),_:3},8,["open","dir","modal"]))}}),Ov=kv,Pv=D({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:t}){const o=At(e,t);he();const s=vc(),i=G(!1);function a(l){l.defaultPrevented||(i.value||setTimeout(()=>{s.triggerElement.value?.focus()},0),i.value=!1,l.preventDefault())}return s.contentId||=Ar(void 0,"reka-dropdown-menu-content"),(l,c)=>(O(),N(f(bv),oe(f(o),{id:f(s).contentId,"aria-labelledby":f(s)?.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:a,onInteractOutside:c[0]||(c[0]=u=>{if(u.defaultPrevented)return;const d=u.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0,h=d.button===2||p;(!f(s).modal.value||h)&&(i.value=!0),f(s).triggerElement.value?.contains(u.target)&&u.preventDefault()})}),{default:b(()=>[q(l.$slots,"default")]),_:3},16,["id","aria-labelledby"]))}}),Mv=Pv,Tv=D({__name:"DropdownMenuItem",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["select"],setup(e,{emit:t}){const n=e,o=qr(t);return he(),(s,i)=>(O(),N(f(pv),et(mt({...n,...f(o)})),{default:b(()=>[q(s.$slots,"default")]),_:3},16))}}),Rv=Tv,Dv=D({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return he(),(n,r)=>(O(),N(f(wv),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Iv=Dv,$v=D({__name:"DropdownMenuPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(Cv),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),qv=$v,Bv=D({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return he(),(n,r)=>(O(),N(f(Ev),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Fv=Bv,Lv=D({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e,n=vc(),{forwardRef:r,currentElement:o}=he();return ht(()=>{n.triggerElement=o}),n.triggerId||=Ar(void 0,"reka-dropdown-menu-trigger"),(s,i)=>(O(),N(f(tv),{"as-child":""},{default:b(()=>[E(f(Pe),{id:f(n).triggerId,ref:f(r),type:s.as==="button"?"button":void 0,"as-child":t.asChild,as:s.as,"aria-haspopup":"menu","aria-expanded":f(n).open.value,"aria-controls":f(n).open.value?f(n).contentId:void 0,"data-disabled":s.disabled?"":void 0,disabled:s.disabled,"data-state":f(n).open.value?"open":"closed",onClick:i[0]||(i[0]=async a=>{!s.disabled&&a.button===0&&a.ctrlKey===!1&&(f(n)?.onOpenToggle(),await Ge(),f(n).open.value&&a.preventDefault())}),onKeydown:i[1]||(i[1]=Cu(a=>{s.disabled||(["Enter"," "].includes(a.key)&&f(n).onOpenToggle(),a.key==="ArrowDown"&&f(n).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())},["enter","space","arrow-down"]))},{default:b(()=>[q(s.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}}),Nv=Lv,jv=D({__name:"BaseSeparator",props:{orientation:{type:String,required:!1,default:"horizontal"},decorative:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,n=["horizontal","vertical"];function r(a){return n.includes(a)}const o=U(()=>r(t.orientation)?t.orientation:"horizontal"),s=U(()=>o.value==="vertical"?t.orientation:void 0),i=U(()=>t.decorative?{role:"none"}:{"aria-orientation":s.value,role:"separator"});return(a,l)=>(O(),N(f(Pe),oe({as:a.as,"as-child":a.asChild,"data-orientation":o.value},i.value),{default:b(()=>[q(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),zv=jv,Vv=D({__name:"Separator",props:{orientation:{type:String,required:!1,default:"horizontal"},decorative:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(zv,et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Hv=Vv,Uv=D({__name:"TooltipArrow",props:{width:{type:Number,required:!1,default:10},height:{type:Number,required:!1,default:5},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const t=e;return he(),(n,r)=>(O(),N(f(Zg),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),Wv=Uv;const[_i,Kv]=gt("TooltipProvider");var Gv=D({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{type:Number,required:!1,default:700},skipDelayDuration:{type:Number,required:!1,default:300},disableHoverableContent:{type:Boolean,required:!1,default:!1},disableClosingTrigger:{type:Boolean,required:!1},disabled:{type:Boolean,required:!1},ignoreNonKeyboardFocus:{type:Boolean,required:!1,default:!1}},setup(e){const t=e,{delayDuration:n,skipDelayDuration:r,disableHoverableContent:o,disableClosingTrigger:s,ignoreNonKeyboardFocus:i,disabled:a}=qt(t);he();const l=G(!0),c=G(!1),{start:u,stop:d}=ju(()=>{l.value=!0},r,{immediate:!1});return Kv({isOpenDelayed:l,delayDuration:n,onOpen(){d(),l.value=!1},onClose(){u()},isPointerInTransitRef:c,disableHoverableContent:o,disableClosingTrigger:s,disabled:a,ignoreNonKeyboardFocus:i}),(p,h)=>q(p.$slots,"default")}}),Yv=Gv;const yc="tooltip.open",[Fo,Xv]=gt("TooltipRoot");var Jv=D({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,required:!1,default:!1},open:{type:Boolean,required:!1,default:void 0},delayDuration:{type:Number,required:!1,default:void 0},disableHoverableContent:{type:Boolean,required:!1,default:void 0},disableClosingTrigger:{type:Boolean,required:!1,default:void 0},disabled:{type:Boolean,required:!1,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,required:!1,default:void 0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t;he();const o=_i(),s=U(()=>n.disableHoverableContent??o.disableHoverableContent.value),i=U(()=>n.disableClosingTrigger??o.disableClosingTrigger.value),a=U(()=>n.disabled??o.disabled.value),l=U(()=>n.delayDuration??o.delayDuration.value),c=U(()=>n.ignoreNonKeyboardFocus??o.ignoreNonKeyboardFocus.value),u=$r(n,"open",r,{defaultValue:n.defaultOpen,passive:n.open===void 0});$e(u,S=>{o.onClose&&(S?(o.onOpen(),document.dispatchEvent(new CustomEvent(yc))):o.onClose())});const d=G(!1),p=G(),h=U(()=>u.value?d.value?"delayed-open":"instant-open":"closed"),{start:g,stop:y}=ju(()=>{d.value=!0,u.value=!0},l,{immediate:!1});function _(){y(),d.value=!1,u.value=!0}function x(){y(),u.value=!1}function A(){g()}return Xv({contentId:"",open:u,stateAttribute:h,trigger:p,onTriggerChange(S){p.value=S},onTriggerEnter(){o.isOpenDelayed.value?A():_()},onTriggerLeave(){s.value?x():y()},onOpen:_,onClose:x,disableHoverableContent:s,disableClosingTrigger:i,disabled:a,ignoreNonKeyboardFocus:c}),(S,C)=>(O(),N(f(tc),null,{default:b(()=>[q(S.$slots,"default",{open:f(u)})]),_:3}))}}),Qv=Jv,Zv=D({__name:"TooltipContentImpl",props:{ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1,default:"top"},sideOffset:{type:Number,required:!1,default:0},align:{type:null,required:!1,default:"center"},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1,default:!0},collisionBoundary:{type:null,required:!1,default:()=>[]},collisionPadding:{type:[Number,Object],required:!1,default:0},arrowPadding:{type:Number,required:!1,default:0},sticky:{type:String,required:!1,default:"partial"},hideWhenDetached:{type:Boolean,required:!1,default:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,o=Fo(),{forwardRef:s}=he(),i=Xd(),a=U(()=>i.default?.({})),l=U(()=>{if(n.ariaLabel)return n.ariaLabel;let u="";function d(p){typeof p.children=="string"&&p.type!==wt?u+=p.children:Array.isArray(p.children)&&p.children.forEach(h=>d(h))}return a.value?.forEach(p=>d(p)),u}),c=U(()=>{const{ariaLabel:u,...d}=n;return d});return ht(()=>{Un(window,"scroll",u=>{u.target?.contains(o.trigger.value)&&o.onClose()}),Un(window,yc,o.onClose)}),(u,d)=>(O(),N(f(ui),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:d[0]||(d[0]=p=>r("escapeKeyDown",p)),onPointerDownOutside:d[1]||(d[1]=p=>{f(o).disableClosingTrigger.value&&f(o).trigger.value?.contains(p.target)&&p.preventDefault(),r("pointerDownOutside",p)}),onFocusOutside:d[2]||(d[2]=xu(()=>{},["prevent"])),onDismiss:d[3]||(d[3]=p=>f(o).onClose())},{default:b(()=>[E(f(hc),oe({ref:f(s),"data-state":f(o).stateAttribute.value},{...u.$attrs,...c.value},{style:{"--reka-tooltip-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-tooltip-content-available-width":"var(--reka-popper-available-width)","--reka-tooltip-content-available-height":"var(--reka-popper-available-height)","--reka-tooltip-trigger-width":"var(--reka-popper-anchor-width)","--reka-tooltip-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:b(()=>[q(u.$slots,"default"),E(f(qm),{id:f(o).contentId,role:"tooltip"},{default:b(()=>[Me(Ue(l.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),bc=Zv,ey=D({__name:"TooltipContentHoverable",props:{ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},setup(e){const n=Br(e),{forwardRef:r,currentElement:o}=he(),{trigger:s,onClose:i}=Fo(),a=_i(),{isPointerInTransit:l,onPointerExit:c}=mh(s,o);return a.isPointerInTransitRef=l,c(()=>{i()}),(u,d)=>(O(),N(bc,oe({ref:f(r)},f(n)),{default:b(()=>[q(u.$slots,"default")]),_:3},16))}}),ty=ey,ny=D({__name:"TooltipContent",props:{forceMount:{type:Boolean,required:!1},ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1,default:"top"},sideOffset:{type:Number,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,o=Fo(),s=At(n,r),{forwardRef:i}=he();return(a,l)=>(O(),N(f(Do),{present:a.forceMount||f(o).open.value},{default:b(()=>[(O(),N(xr(f(o).disableHoverableContent.value?bc:ty),oe({ref:f(i)},f(s)),{default:b(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),ry=ny,oy=D({__name:"TooltipPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(O(),N(f(ci),et(mt(t)),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),sy=oy,iy=D({__name:"TooltipTrigger",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e,n=Fo(),r=_i();n.contentId||=Ar(void 0,"reka-tooltip-content");const{forwardRef:o,currentElement:s}=he(),i=G(!1),a=G(!1),l=U(()=>n.disabled.value?{}:{click:y,focus:h,pointermove:d,pointerleave:p,pointerdown:u,blur:g});ht(()=>{n.onTriggerChange(s.value)});function c(){setTimeout(()=>{i.value=!1},1)}function u(){n.open&&!n.disableClosingTrigger.value&&n.onClose(),i.value=!0,document.addEventListener("pointerup",c,{once:!0})}function d(_){_.pointerType!=="touch"&&!a.value&&!r.isPointerInTransitRef.value&&(n.onTriggerEnter(),a.value=!0)}function p(){n.onTriggerLeave(),a.value=!1}function h(_){i.value||n.ignoreNonKeyboardFocus.value&&!_.target.matches?.(":focus-visible")||n.onOpen()}function g(){n.onClose()}function y(){n.disableClosingTrigger.value||n.onClose()}return(_,x)=>(O(),N(f(nc),{"as-child":"",reference:_.reference},{default:b(()=>[E(f(Pe),oe({ref:f(o),"aria-describedby":f(n).open.value?f(n).contentId:void 0,"data-state":f(n).stateAttribute.value,as:_.as,"as-child":t.asChild,"data-grace-area-trigger":""},Gd(l.value)),{default:b(()=>[q(_.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3},8,["reference"]))}}),ay=iy;const ly=D({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const o=At(e,t);return(s,i)=>(O(),N(f($h),oe({"data-slot":"sheet"},f(o)),{default:b(()=>[q(s.$slots,"default")]),_:3},16))}});function uy(e){return wo()?(Us(e),!0):!1}const is=new WeakMap,cy=(...e)=>{var t;const n=e[0],r=(t=at())==null?void 0:t.proxy;if(r==null&&!tu())throw new Error("injectLocal must be called in setup");return r&&is.has(r)&&n in is.get(r)?is.get(r)[n]:ot(...e)};function dy(e){if(!Re(e))return cn(e);const t=new Proxy({},{get(n,r,o){return f(Reflect.get(e.value,r,o))},set(n,r,o){return Re(e.value[r])&&!Re(o)?e.value[r].value=o:e.value[r]=o,!0},deleteProperty(n,r){return Reflect.deleteProperty(e.value,r)},has(n,r){return Reflect.has(e.value,r)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return cn(t)}function fy(e){return dy(U(e))}function vt(e,...t){const n=t.flat(),r=n[0];return fy(()=>Object.fromEntries(typeof r=="function"?Object.entries(qt(e)).filter(([o,s])=>!r(Be(s),o)):Object.entries(qt(e)).filter(o=>!n.includes(o[0]))))}const py=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const hy=e=>typeof e<"u",my=Object.prototype.toString,gy=e=>my.call(e)==="[object Object]";function Ma(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function as(e){return Array.isArray(e)?e:[e]}function vy(e,t,n){return $e(e,t,{...n,immediate:!0})}const _c=py?window:void 0;function yy(e){var t;const n=Be(e);return(t=n?.$el)!=null?t:n}function wc(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,l,c,u)=>(a.addEventListener(l,c,u),()=>a.removeEventListener(l,c,u)),o=U(()=>{const a=as(Be(e[0])).filter(l=>l!=null);return a.every(l=>typeof l!="string")?a:void 0}),s=vy(()=>{var a,l;return[(l=(a=o.value)==null?void 0:a.map(c=>yy(c)))!=null?l:[_c].filter(c=>c!=null),as(Be(o.value?e[1]:e[0])),as(f(o.value?e[2]:e[1])),Be(o.value?e[3]:e[2])]},([a,l,c,u])=>{if(n(),!a?.length||!l?.length||!c?.length)return;const d=gy(u)?{...u}:u;t.push(...a.flatMap(p=>l.flatMap(h=>c.map(g=>r(p,h,g,d)))))},{flush:"post"}),i=()=>{s(),n()};return uy(n),i}function by(){const e=Gt(!1),t=at();return t&&ht(()=>{e.value=!0},t),e}function _y(e){const t=by();return U(()=>(t.value,!!e()))}const wy=Symbol("vueuse-ssr-width");function xy(){const e=tu()?cy(wy,null):null;return typeof e=="number"?e:void 0}function Cy(e,t={}){const{window:n=_c,ssrWidth:r=xy()}=t,o=_y(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),s=Gt(typeof r=="number"),i=Gt(),a=Gt(!1),l=c=>{a.value=c.matches};return tt(()=>{if(s.value){s.value=!o.value;const c=Be(e).split(",");a.value=c.some(u=>{const d=u.includes("not all"),p=u.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),h=u.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let g=!!(p||h);return p&&g&&(g=r>=Ma(p[1])),h&&g&&(g=r<=Ma(h[1])),d?!g:g});return}o.value&&(i.value=n.matchMedia(Be(e)),a.value=i.value.matches)}),wc(i,"change",l,{passive:!0}),U(()=>a.value)}function Sy(e){return JSON.parse(JSON.stringify(e))}function xc(e,t,n,r={}){var o,s,i;const{clone:a=!1,passive:l=!1,eventName:c,deep:u=!1,defaultValue:d,shouldEmit:p}=r,h=at(),g=n||h?.emit||((o=h?.$emit)==null?void 0:o.bind(h))||((i=(s=h?.proxy)==null?void 0:s.$emit)==null?void 0:i.bind(h?.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const _=S=>a?typeof a=="function"?a(S):Sy(S):S,x=()=>hy(e[t])?_(e[t]):d,A=S=>{p?p(S)&&g(y,S):g(y,S)};if(l){const S=x(),C=G(S);let P=!1;return $e(()=>e[t],H=>{P||(P=!0,C.value=_(H),Ge(()=>P=!1))}),$e(C,H=>{!P&&(H!==e[t]||u)&&A(H)},{deep:u}),C}else return U({get(){return x()},set(S){A(S)}})}/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ta=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ey=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),Ay=e=>{const t=Ey(e);return t.charAt(0).toUpperCase()+t.slice(1)},ky=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),Ra=e=>e==="";/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var or={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=({name:e,iconNode:t,absoluteStrokeWidth:n,"absolute-stroke-width":r,strokeWidth:o,"stroke-width":s,size:i=or.width,color:a=or.stroke,...l},{slots:c})=>_t("svg",{...or,...l,width:i,height:i,stroke:a,"stroke-width":Ra(n)||Ra(r)||n===!0||r===!0?Number(o||s||or["stroke-width"])*24/Number(i):o||s||or["stroke-width"],class:ky("lucide",l.class,...e?[`lucide-${Ta(Ay(e))}-icon`,`lucide-${Ta(e)}`]:["lucide-icon"])},[...t.map(u=>_t(...u)),...c.default?[c.default()]:[]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=(e,t)=>(n,{slots:r,attrs:o})=>_t(Oy,{...o,...n,iconNode:t,name:e},r);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=Te("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=Te("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=Te("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=Te("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=Te("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cc=Te("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iy=Te("clipboard-check",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $y=Te("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sc=Te("factory",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M16 16h.01",key:"1f9h7w"}],["path",{d:"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z",key:"1iv0i2"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=Te("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const By=Te("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=Te("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=Te("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=Te("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=Te("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zy=Te("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ec=Te("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ac=Te("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vy=Te("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kc=Te("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hy=Te("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=Te("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wy=Te("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ky=Te("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Gy=D({__name:"SheetOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(mm),oe({"data-slot":"sheet-overlay",class:f(ce)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",t.class)},f(n)),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),Yy=D({inheritAttrs:!1,__name:"SheetContent",props:{class:{},side:{default:"right"},forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=vt(n,"class","side"),s=At(o,r);return(i,a)=>(O(),N(f(ym),null,{default:b(()=>[E(Gy),E(f(um),oe({"data-slot":"sheet-content",class:f(ce)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",i.side==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",i.side==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",i.side==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",i.side==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",n.class)},{...f(s),...i.$attrs}),{default:b(()=>[q(i.$slots,"default"),E(f(Bh),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none"},{default:b(()=>[E(f(Ky),{class:"size-4"}),a[0]||(a[0]=ee("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),Xy=D({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(dm),oe({"data-slot":"sheet-description",class:f(ce)("text-muted-foreground text-sm",t.class)},f(n)),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),Jy=D({__name:"SheetHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sheet-header",class:ye(f(ce)("flex flex-col gap-1.5 p-4",t.class))},[q(n.$slots,"default")],2))}}),Qy=D({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(_m),oe({"data-slot":"sheet-title",class:f(ce)("text-foreground font-semibold",t.class)},f(n)),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),Zy="sidebar_state",eb=3600*24*7,tb="16rem",nb="18rem",rb="3rem",ob="b",[wi,sb]=gt("Sidebar"),ib={class:"flex h-full w-full flex-col"},ab=["data-state","data-collapsible","data-variant","data-side"],lb={"data-sidebar":"sidebar",class:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm"},ub=D({inheritAttrs:!1,__name:"Sidebar",props:{side:{default:"left"},variant:{default:"sidebar"},collapsible:{default:"offcanvas"},class:{}},setup(e){const t=e,{isMobile:n,state:r,openMobile:o,setOpenMobile:s}=wi();return(i,a)=>i.collapsible==="none"?(O(),pe("div",oe({key:0,"data-slot":"sidebar",class:f(ce)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",t.class)},i.$attrs),[q(i.$slots,"default")],16)):f(n)?(O(),N(f(ly),oe({key:1,open:f(o)},i.$attrs,{"onUpdate:open":f(s)}),{default:b(()=>[E(f(Yy),{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",side:i.side,class:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:mn({"--sidebar-width":f(nb)})},{default:b(()=>[E(Jy,{class:"sr-only"},{default:b(()=>[E(Qy,null,{default:b(()=>a[0]||(a[0]=[Me("Sidebar",-1)])),_:1,__:[0]}),E(Xy,null,{default:b(()=>a[1]||(a[1]=[Me("Displays the mobile sidebar.",-1)])),_:1,__:[1]})]),_:1}),ee("div",ib,[q(i.$slots,"default")])]),_:3},8,["side","style"])]),_:3},16,["open","onUpdate:open"])):(O(),pe("div",{key:2,class:"group peer text-sidebar-foreground hidden md:block","data-slot":"sidebar","data-state":f(r),"data-collapsible":f(r)==="collapsed"?i.collapsible:"","data-variant":i.variant,"data-side":i.side},[ee("div",{class:ye(f(ce)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",i.variant==="floating"||i.variant==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)"))},null,2),ee("div",oe({class:f(ce)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",i.side==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",i.variant==="floating"||i.variant==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",t.class)},i.$attrs),[ee("div",lb,[q(i.$slots,"default")])],16)],8,ab))}}),cb=D({__name:"SidebarContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sidebar-content","data-sidebar":"content",class:ye(f(ce)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t.class))},[q(n.$slots,"default")],2))}}),db=D({__name:"SidebarFooter",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",class:ye(f(ce)("flex flex-col gap-2 p-2",t.class))},[q(n.$slots,"default")],2))}}),Da=D({__name:"SidebarGroup",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sidebar-group","data-sidebar":"group",class:ye(f(ce)("relative flex w-full min-w-0 flex-col p-2",t.class))},[q(n.$slots,"default")],2))}}),Ia=D({__name:"SidebarGroupContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",class:ye(f(ce)("w-full text-sm",t.class))},[q(n.$slots,"default")],2))}}),$a=D({__name:"SidebarGroupLabel",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),{"data-slot":"sidebar-group-label","data-sidebar":"group-label",as:n.as,"as-child":n.asChild,class:ye(f(ce)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t.class))},{default:b(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),fb=D({__name:"SidebarHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"sidebar-header","data-sidebar":"header",class:ye(f(ce)("flex flex-col gap-2 p-2",t.class))},[q(n.$slots,"default")],2))}}),pb=D({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,o=xc(n,"modelValue",t,{passive:!0,defaultValue:n.defaultValue});return(s,i)=>Ll((O(),pe("input",{"onUpdate:modelValue":i[0]||(i[0]=a=>Re(o)?o.value=a:null),"data-slot":"input",class:ye(f(ce)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n.class))},null,2)),[[rp,f(o)]])}}),hb=D({__name:"SidebarInset",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("main",{"data-slot":"sidebar-inset",class:ye(f(ce)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t.class))},[q(n.$slots,"default")],2))}}),Yr=D({__name:"SidebarMenu",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",class:ye(f(ce)("flex w-full min-w-0 flex-col gap-1",t.class))},[q(n.$slots,"default")],2))}}),mb=D({__name:"Tooltip",props:{defaultOpen:{type:Boolean},open:{type:Boolean},delayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const o=At(e,t);return(s,i)=>(O(),N(f(Qv),oe({"data-slot":"tooltip"},f(o)),{default:b(()=>[q(s.$slots,"default")]),_:3},16))}}),gb=D({inheritAttrs:!1,__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},class:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,o=vt(n,"class"),s=At(o,r);return(i,a)=>(O(),N(f(sy),null,{default:b(()=>[E(f(ry),oe({"data-slot":"tooltip-content"},{...f(s),...i.$attrs},{class:f(ce)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",n.class)}),{default:b(()=>[q(i.$slots,"default"),E(f(Wv),{class:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]),_:3},16,["class"])]),_:3}))}}),vb=D({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,r)=>(O(),N(f(ay),oe({"data-slot":"tooltip-trigger"},t),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),qa=D({__name:"SidebarMenuButtonChild",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),oe({"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n.size,"data-active":n.isActive,class:f(ce)(f(xb)({variant:n.variant,size:n.size}),t.class),as:n.as,"as-child":n.asChild},n.$attrs),{default:b(()=>[q(n.$slots,"default")]),_:3},16,["data-size","data-active","class","as","as-child"]))}}),Xr=D({inheritAttrs:!1,__name:"SidebarMenuButton",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{default:"button"},tooltip:{}},setup(e){const t=e,{isMobile:n,state:r}=wi(),o=vt(t,"tooltip");return(s,i)=>s.tooltip?(O(),N(f(mb),{key:1},{default:b(()=>[E(f(vb),{"as-child":""},{default:b(()=>[E(qa,et(mt({...f(o),...s.$attrs})),{default:b(()=>[q(s.$slots,"default")]),_:3},16)]),_:3}),E(f(gb),{side:"right",align:"center",hidden:f(r)!=="collapsed"||f(n)},{default:b(()=>[typeof s.tooltip=="string"?(O(),pe(ze,{key:0},[Me(Ue(s.tooltip),1)],64)):(O(),N(xr(s.tooltip),{key:1}))]),_:1},8,["hidden"])]),_:3})):(O(),N(qa,et(oe({key:0},{...f(o),...s.$attrs})),{default:b(()=>[q(s.$slots,"default")]),_:3},16))}}),Jr=D({__name:"SidebarMenuItem",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",class:ye(f(ce)("group/menu-item relative",t.class))},[q(n.$slots,"default")],2))}}),yb=D({__name:"SidebarProvider",props:{defaultOpen:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},class:{}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,o=Cy("(max-width: 768px)"),s=G(!1),i=xc(n,"open",r,{defaultValue:n.defaultOpen??!1,passive:n.open===void 0});function a(d){i.value=d,document.cookie=`${Zy}=${i.value}; path=/; max-age=${eb}`}function l(d){s.value=d}function c(){return o.value?l(!s.value):a(!i.value)}wc("keydown",d=>{d.key===ob&&(d.metaKey||d.ctrlKey)&&(d.preventDefault(),c())});const u=U(()=>i.value?"expanded":"collapsed");return sb({state:u,open:i,setOpen:a,isMobile:o,openMobile:s,setOpenMobile:l,toggleSidebar:c}),(d,p)=>(O(),N(f(Yv),{"delay-duration":0},{default:b(()=>[ee("div",oe({"data-slot":"sidebar-wrapper",style:{"--sidebar-width":f(tb),"--sidebar-width-icon":f(rb)},class:f(ce)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n.class)},d.$attrs),[q(d.$slots,"default")],16)]),_:3}))}}),bb=D({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(Hv),oe({"data-slot":"separator-root"},f(n),{class:f(ce)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t.class)}),null,16,["class"]))}}),lr=D({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),{"data-slot":"button",as:n.as,"as-child":n.asChild,class:ye(f(ce)(f(_b)({variant:n.variant,size:n.size}),t.class))},{default:b(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),_b=oi("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),wb=D({__name:"SidebarTrigger",props:{class:{}},setup(e){const t=e,{toggleSidebar:n}=wi();return(r,o)=>(O(),N(f(lr),{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",class:ye(f(ce)("h-7 w-7",t.class)),onClick:f(n)},{default:b(()=>[E(f(jy)),o[0]||(o[0]=ee("span",{class:"sr-only"},"Toggle Sidebar",-1))]),_:1,__:[0]},8,["class","onClick"]))}}),xb=oi("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Bn=typeof document<"u";function Oc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Cb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Oc(e.default)}const _e=Object.assign;function ls(e,t){const n={};for(const r in t){const o=t[r];n[r]=St(o)?o.map(e):e(o)}return n}const vr=()=>{},St=Array.isArray,Pc=/#/g,Sb=/&/g,Eb=/\//g,Ab=/=/g,kb=/\?/g,Mc=/\+/g,Ob=/%5B/g,Pb=/%5D/g,Tc=/%5E/g,Mb=/%60/g,Rc=/%7B/g,Tb=/%7C/g,Dc=/%7D/g,Rb=/%20/g;function xi(e){return encodeURI(""+e).replace(Tb,"|").replace(Ob,"[").replace(Pb,"]")}function Db(e){return xi(e).replace(Rc,"{").replace(Dc,"}").replace(Tc,"^")}function Bs(e){return xi(e).replace(Mc,"%2B").replace(Rb,"+").replace(Pc,"%23").replace(Sb,"%26").replace(Mb,"`").replace(Rc,"{").replace(Dc,"}").replace(Tc,"^")}function Ib(e){return Bs(e).replace(Ab,"%3D")}function $b(e){return xi(e).replace(Pc,"%23").replace(kb,"%3F")}function qb(e){return e==null?"":$b(e).replace(Eb,"%2F")}function Pr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Bb=/\/$/,Fb=e=>e.replace(Bb,"");function us(e,t,n="/"){let r,o={},s="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),s=t.slice(l+1,a>-1?a:t.length),o=e(s)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=zb(r??t,n),{fullPath:r+(s&&"?")+s+i,path:r,query:o,hash:Pr(i)}}function Lb(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ba(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Nb(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&Kn(t.matched[r],n.matched[o])&&Ic(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Kn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ic(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!jb(e[n],t[n]))return!1;return!0}function jb(e,t){return St(e)?Fa(e,t):St(t)?Fa(t,e):e===t}function Fa(e,t){return St(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function zb(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let s=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+r.slice(i).join("/")}const rn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mr;(function(e){e.pop="pop",e.push="push"})(Mr||(Mr={}));var yr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(yr||(yr={}));function Vb(e){if(!e)if(Bn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Fb(e)}const Hb=/^[^#]+#/;function Ub(e,t){return e.replace(Hb,"#")+t}function Wb(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Lo=()=>({left:window.scrollX,top:window.scrollY});function Kb(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Wb(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function La(e,t){return(history.state?history.state.position-t:-1)+e}const Fs=new Map;function Gb(e,t){Fs.set(e,t)}function Yb(e){const t=Fs.get(e);return Fs.delete(e),t}let Xb=()=>location.protocol+"//"+location.host;function $c(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let a=o.includes(e.slice(s))?e.slice(s).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),Ba(l,"")}return Ba(n,e)+r+o}function Jb(e,t,n,r){let o=[],s=[],i=null;const a=({state:p})=>{const h=$c(e,location),g=n.value,y=t.value;let _=0;if(p){if(n.value=h,t.value=p,i&&i===g){i=null;return}_=y?p.position-y.position:0}else r(h);o.forEach(x=>{x(n.value,g,{delta:_,type:Mr.pop,direction:_?_>0?yr.forward:yr.back:yr.unknown})})};function l(){i=n.value}function c(p){o.push(p);const h=()=>{const g=o.indexOf(p);g>-1&&o.splice(g,1)};return s.push(h),h}function u(){const{history:p}=window;p.state&&p.replaceState(_e({},p.state,{scroll:Lo()}),"")}function d(){for(const p of s)p();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:d}}function Na(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?Lo():null}}function Qb(e){const{history:t,location:n}=window,r={value:$c(e,n)},o={value:t.state};o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(l,c,u){const d=e.indexOf("#"),p=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+l:Xb()+e+l;try{t[u?"replaceState":"pushState"](c,"",p),o.value=c}catch(h){console.error(h),n[u?"replace":"assign"](p)}}function i(l,c){const u=_e({},t.state,Na(o.value.back,l,o.value.forward,!0),c,{position:o.value.position});s(l,u,!0),r.value=l}function a(l,c){const u=_e({},o.value,t.state,{forward:l,scroll:Lo()});s(u.current,u,!0);const d=_e({},Na(r.value,l,null),{position:u.position+1},c);s(l,d,!1),r.value=l}return{location:r,state:o,push:a,replace:i}}function Zb(e){e=Vb(e);const t=Qb(e),n=Jb(e,t.state,t.location,t.replace);function r(s,i=!0){i||n.pauseListeners(),history.go(s)}const o=_e({location:"",base:e,go:r,createHref:Ub.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function e_(e){return typeof e=="string"||e&&typeof e=="object"}function qc(e){return typeof e=="string"||typeof e=="symbol"}const Bc=Symbol("");var ja;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ja||(ja={}));function Gn(e,t){return _e(new Error,{type:e,[Bc]:!0},t)}function Ht(e,t){return e instanceof Error&&Bc in e&&(t==null||!!(e.type&t))}const za="[^/]+?",t_={sensitive:!1,strict:!1,start:!0,end:!0},n_=/[.+*?^${}()[\]/\\]/g;function r_(e,t){const n=_e({},t_,t),r=[];let o=n.start?"^":"";const s=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(o+="/");for(let d=0;d<c.length;d++){const p=c[d];let h=40+(n.sensitive?.25:0);if(p.type===0)d||(o+="/"),o+=p.value.replace(n_,"\\$&"),h+=40;else if(p.type===1){const{value:g,repeatable:y,optional:_,regexp:x}=p;s.push({name:g,repeatable:y,optional:_});const A=x||za;if(A!==za){h+=10;try{new RegExp(`(${A})`)}catch(C){throw new Error(`Invalid custom RegExp for param "${g}" (${A}): `+C.message)}}let S=y?`((?:${A})(?:/(?:${A}))*)`:`(${A})`;d||(S=_&&c.length<2?`(?:/${S})`:"/"+S),_&&(S+="?"),o+=S,h+=20,_&&(h+=-8),y&&(h+=-20),A===".*"&&(h+=-50)}u.push(h)}r.push(u)}if(n.strict&&n.end){const c=r.length-1;r[c][r[c].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function a(c){const u=c.match(i),d={};if(!u)return null;for(let p=1;p<u.length;p++){const h=u[p]||"",g=s[p-1];d[g.name]=h&&g.repeatable?h.split("/"):h}return d}function l(c){let u="",d=!1;for(const p of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const h of p)if(h.type===0)u+=h.value;else if(h.type===1){const{value:g,repeatable:y,optional:_}=h,x=g in c?c[g]:"";if(St(x)&&!y)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const A=St(x)?x.join("/"):x;if(!A)if(_)p.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${g}"`);u+=A}}return u||"/"}return{re:i,score:r,keys:s,parse:a,stringify:l}}function o_(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Fc(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const s=o_(r[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-r.length)===1){if(Va(r))return 1;if(Va(o))return-1}return o.length-r.length}function Va(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const s_={type:0,value:""},i_=/[a-zA-Z0-9_]/;function a_(e){if(!e)return[[]];if(e==="/")return[[s_]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${c}": ${h}`)}let n=0,r=n;const o=[];let s;function i(){s&&o.push(s),s=[]}let a=0,l,c="",u="";function d(){c&&(n===0?s.push({type:0,value:c}):n===1||n===2||n===3?(s.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function p(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(c&&d(),i()):l===":"?(d(),n=1):p();break;case 4:p(),n=r;break;case 1:l==="("?n=2:i_.test(l)?p():(d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),d(),i(),o}function l_(e,t,n){const r=r_(a_(e.path),n),o=_e(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function u_(e,t){const n=[],r=new Map;t=Ka({strict:!1,end:!0,sensitive:!1},t);function o(d){return r.get(d)}function s(d,p,h){const g=!h,y=Ua(d);y.aliasOf=h&&h.record;const _=Ka(t,d),x=[y];if("alias"in d){const C=typeof d.alias=="string"?[d.alias]:d.alias;for(const P of C)x.push(Ua(_e({},y,{components:h?h.record.components:y.components,path:P,aliasOf:h?h.record:y})))}let A,S;for(const C of x){const{path:P}=C;if(p&&P[0]!=="/"){const H=p.record.path,F=H[H.length-1]==="/"?"":"/";C.path=p.record.path+(P&&F+P)}if(A=l_(C,p,_),h?h.alias.push(A):(S=S||A,S!==A&&S.alias.push(A),g&&d.name&&!Wa(A)&&i(d.name)),Lc(A)&&l(A),y.children){const H=y.children;for(let F=0;F<H.length;F++)s(H[F],A,h&&h.children[F])}h=h||A}return S?()=>{i(S)}:vr}function i(d){if(qc(d)){const p=r.get(d);p&&(r.delete(d),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(d);p>-1&&(n.splice(p,1),d.record.name&&r.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function a(){return n}function l(d){const p=f_(d,n);n.splice(p,0,d),d.record.name&&!Wa(d)&&r.set(d.record.name,d)}function c(d,p){let h,g={},y,_;if("name"in d&&d.name){if(h=r.get(d.name),!h)throw Gn(1,{location:d});_=h.record.name,g=_e(Ha(p.params,h.keys.filter(S=>!S.optional).concat(h.parent?h.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),d.params&&Ha(d.params,h.keys.map(S=>S.name))),y=h.stringify(g)}else if(d.path!=null)y=d.path,h=n.find(S=>S.re.test(y)),h&&(g=h.parse(y),_=h.record.name);else{if(h=p.name?r.get(p.name):n.find(S=>S.re.test(p.path)),!h)throw Gn(1,{location:d,currentLocation:p});_=h.record.name,g=_e({},p.params,d.params),y=h.stringify(g)}const x=[];let A=h;for(;A;)x.unshift(A.record),A=A.parent;return{name:_,path:y,params:g,matched:x,meta:d_(x)}}e.forEach(d=>s(d));function u(){n.length=0,r.clear()}return{addRoute:s,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:a,getRecordMatcher:o}}function Ha(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ua(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:c_(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function c_(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Wa(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function d_(e){return e.reduce((t,n)=>_e(t,n.meta),{})}function Ka(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function f_(e,t){let n=0,r=t.length;for(;n!==r;){const s=n+r>>1;Fc(e,t[s])<0?r=s:n=s+1}const o=p_(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function p_(e){let t=e;for(;t=t.parent;)if(Lc(t)&&Fc(e,t)===0)return t}function Lc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function h_(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const s=r[o].replace(Mc," "),i=s.indexOf("="),a=Pr(i<0?s:s.slice(0,i)),l=i<0?null:Pr(s.slice(i+1));if(a in t){let c=t[a];St(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function Ga(e){let t="";for(let n in e){const r=e[n];if(n=Ib(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(St(r)?r.map(s=>s&&Bs(s)):[r&&Bs(r)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function m_(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=St(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const g_=Symbol(""),Ya=Symbol(""),No=Symbol(""),Ci=Symbol(""),Ls=Symbol("");function sr(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ln(e,t,n,r,o,s=i=>i()){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((a,l)=>{const c=p=>{p===!1?l(Gn(4,{from:n,to:t})):p instanceof Error?l(p):e_(p)?l(Gn(2,{from:t,to:p})):(i&&r.enterCallbacks[o]===i&&typeof p=="function"&&i.push(p),a())},u=s(()=>e.call(r&&r.instances[o],t,n,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(p=>l(p))})}function cs(e,t,n,r,o=s=>s()){const s=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Oc(l)){const u=(l.__vccOpts||l)[t];u&&s.push(ln(u,n,r,i,a,o))}else{let c=l();s.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const d=Cb(u)?u.default:u;i.mods[a]=u,i.components[a]=d;const h=(d.__vccOpts||d)[t];return h&&ln(h,n,r,i,a,o)()}))}}return s}function Xa(e){const t=ot(No),n=ot(Ci),r=U(()=>{const l=f(e.to);return t.resolve(l)}),o=U(()=>{const{matched:l}=r.value,{length:c}=l,u=l[c-1],d=n.matched;if(!u||!d.length)return-1;const p=d.findIndex(Kn.bind(null,u));if(p>-1)return p;const h=Ja(l[c-2]);return c>1&&Ja(u)===h&&d[d.length-1].path!==h?d.findIndex(Kn.bind(null,l[c-2])):p}),s=U(()=>o.value>-1&&w_(n.params,r.value.params)),i=U(()=>o.value>-1&&o.value===n.matched.length-1&&Ic(n.params,r.value.params));function a(l={}){if(__(l)){const c=t[f(e.replace)?"replace":"push"](f(e.to)).catch(vr);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:r,href:U(()=>r.value.href),isActive:s,isExactActive:i,navigate:a}}function v_(e){return e.length===1?e[0]:e}const y_=D({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Xa,setup(e,{slots:t}){const n=cn(Xa(e)),{options:r}=ot(No),o=U(()=>({[Qa(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Qa(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&v_(t.default(n));return e.custom?s:_t("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),b_=y_;function __(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function w_(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!St(o)||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function Ja(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Qa=(e,t,n)=>e??t??n,x_=D({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ot(Ls),o=U(()=>e.route||r.value),s=ot(Ya,0),i=U(()=>{let c=f(s);const{matched:u}=o.value;let d;for(;(d=u[c])&&!d.components;)c++;return c}),a=U(()=>o.value.matched[i.value]);Vn(Ya,U(()=>i.value+1)),Vn(g_,a),Vn(Ls,o);const l=G();return $e(()=>[l.value,a.value,e.name],([c,u,d],[p,h,g])=>{u&&(u.instances[d]=c,h&&h!==u&&c&&c===p&&(u.leaveGuards.size||(u.leaveGuards=h.leaveGuards),u.updateGuards.size||(u.updateGuards=h.updateGuards))),c&&u&&(!h||!Kn(u,h)||!p)&&(u.enterCallbacks[d]||[]).forEach(y=>y(c))},{flush:"post"}),()=>{const c=o.value,u=e.name,d=a.value,p=d&&d.components[u];if(!p)return Za(n.default,{Component:p,route:c});const h=d.props[u],g=h?h===!0?c.params:typeof h=="function"?h(c):h:null,_=_t(p,_e({},g,t,{onVnodeUnmounted:x=>{x.component.isUnmounted&&(d.instances[u]=null)},ref:l}));return Za(n.default,{Component:_,route:c})||_}}});function Za(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const C_=x_;function S_(e){const t=u_(e.routes,e),n=e.parseQuery||h_,r=e.stringifyQuery||Ga,o=e.history,s=sr(),i=sr(),a=sr(),l=Gt(rn);let c=rn;Bn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ls.bind(null,k=>""+k),d=ls.bind(null,qb),p=ls.bind(null,Pr);function h(k,Y){let V,Q;return qc(k)?(V=t.getRecordMatcher(k),Q=Y):Q=k,t.addRoute(Q,V)}function g(k){const Y=t.getRecordMatcher(k);Y&&t.removeRoute(Y)}function y(){return t.getRoutes().map(k=>k.record)}function _(k){return!!t.getRecordMatcher(k)}function x(k,Y){if(Y=_e({},Y||l.value),typeof k=="string"){const w=us(n,k,Y.path),M=t.resolve({path:w.path},Y),I=o.createHref(w.fullPath);return _e(w,M,{params:p(M.params),hash:Pr(w.hash),redirectedFrom:void 0,href:I})}let V;if(k.path!=null)V=_e({},k,{path:us(n,k.path,Y.path).path});else{const w=_e({},k.params);for(const M in w)w[M]==null&&delete w[M];V=_e({},k,{params:d(w)}),Y.params=d(Y.params)}const Q=t.resolve(V,Y),ge=k.hash||"";Q.params=u(p(Q.params));const m=Lb(r,_e({},k,{hash:Db(ge),path:Q.path})),v=o.createHref(m);return _e({fullPath:m,hash:ge,query:r===Ga?m_(k.query):k.query||{}},Q,{redirectedFrom:void 0,href:v})}function A(k){return typeof k=="string"?us(n,k,l.value.path):_e({},k)}function S(k,Y){if(c!==k)return Gn(8,{from:Y,to:k})}function C(k){return F(k)}function P(k){return C(_e(A(k),{replace:!0}))}function H(k){const Y=k.matched[k.matched.length-1];if(Y&&Y.redirect){const{redirect:V}=Y;let Q=typeof V=="function"?V(k):V;return typeof Q=="string"&&(Q=Q.includes("?")||Q.includes("#")?Q=A(Q):{path:Q},Q.params={}),_e({query:k.query,hash:k.hash,params:Q.path!=null?{}:k.params},Q)}}function F(k,Y){const V=c=x(k),Q=l.value,ge=k.state,m=k.force,v=k.replace===!0,w=H(V);if(w)return F(_e(A(w),{state:typeof w=="object"?_e({},ge,w.state):ge,force:m,replace:v}),Y||V);const M=V;M.redirectedFrom=Y;let I;return!m&&Nb(r,Q,V)&&(I=Gn(16,{to:M,from:Q}),ut(Q,Q,!0,!1)),(I?Promise.resolve(I):Z(M,Q)).catch(R=>Ht(R)?Ht(R,2)?R:lt(R):B(R,M,Q)).then(R=>{if(R){if(Ht(R,2))return F(_e({replace:v},A(R.to),{state:typeof R.to=="object"?_e({},ge,R.to.state):ge,force:m}),Y||M)}else R=fe(M,Q,!0,v,ge);return ae(M,Q,R),R})}function $(k,Y){const V=S(k,Y);return V?Promise.reject(V):Promise.resolve()}function T(k){const Y=Fe.values().next().value;return Y&&typeof Y.runWithContext=="function"?Y.runWithContext(k):k()}function Z(k,Y){let V;const[Q,ge,m]=E_(k,Y);V=cs(Q.reverse(),"beforeRouteLeave",k,Y);for(const w of Q)w.leaveGuards.forEach(M=>{V.push(ln(M,k,Y))});const v=$.bind(null,k,Y);return V.push(v),Xe(V).then(()=>{V=[];for(const w of s.list())V.push(ln(w,k,Y));return V.push(v),Xe(V)}).then(()=>{V=cs(ge,"beforeRouteUpdate",k,Y);for(const w of ge)w.updateGuards.forEach(M=>{V.push(ln(M,k,Y))});return V.push(v),Xe(V)}).then(()=>{V=[];for(const w of m)if(w.beforeEnter)if(St(w.beforeEnter))for(const M of w.beforeEnter)V.push(ln(M,k,Y));else V.push(ln(w.beforeEnter,k,Y));return V.push(v),Xe(V)}).then(()=>(k.matched.forEach(w=>w.enterCallbacks={}),V=cs(m,"beforeRouteEnter",k,Y,T),V.push(v),Xe(V))).then(()=>{V=[];for(const w of i.list())V.push(ln(w,k,Y));return V.push(v),Xe(V)}).catch(w=>Ht(w,8)?w:Promise.reject(w))}function ae(k,Y,V){a.list().forEach(Q=>T(()=>Q(k,Y,V)))}function fe(k,Y,V,Q,ge){const m=S(k,Y);if(m)return m;const v=Y===rn,w=Bn?history.state:{};V&&(Q||v?o.replace(k.fullPath,_e({scroll:v&&w&&w.scroll},ge)):o.push(k.fullPath,ge)),l.value=k,ut(k,Y,V,v),lt()}let Ce;function be(){Ce||(Ce=o.listen((k,Y,V)=>{if(!Nt.listening)return;const Q=x(k),ge=H(Q);if(ge){F(_e(ge,{replace:!0,force:!0}),Q).catch(vr);return}c=Q;const m=l.value;Bn&&Gb(La(m.fullPath,V.delta),Lo()),Z(Q,m).catch(v=>Ht(v,12)?v:Ht(v,2)?(F(_e(A(v.to),{force:!0}),Q).then(w=>{Ht(w,20)&&!V.delta&&V.type===Mr.pop&&o.go(-1,!1)}).catch(vr),Promise.reject()):(V.delta&&o.go(-V.delta,!1),B(v,Q,m))).then(v=>{v=v||fe(Q,m,!1),v&&(V.delta&&!Ht(v,8)?o.go(-V.delta,!1):V.type===Mr.pop&&Ht(v,20)&&o.go(-1,!1)),ae(Q,m,v)}).catch(vr)}))}let Ee=sr(),ue=sr(),re;function B(k,Y,V){lt(k);const Q=ue.list();return Q.length?Q.forEach(ge=>ge(k,Y,V)):console.error(k),Promise.reject(k)}function qe(){return re&&l.value!==rn?Promise.resolve():new Promise((k,Y)=>{Ee.add([k,Y])})}function lt(k){return re||(re=!k,be(),Ee.list().forEach(([Y,V])=>k?V(k):Y()),Ee.reset()),k}function ut(k,Y,V,Q){const{scrollBehavior:ge}=e;if(!Bn||!ge)return Promise.resolve();const m=!V&&Yb(La(k.fullPath,0))||(Q||!V)&&history.state&&history.state.scroll||null;return Ge().then(()=>ge(k,Y,m)).then(v=>v&&Kb(v)).catch(v=>B(v,k,Y))}const Ne=k=>o.go(k);let Ie;const Fe=new Set,Nt={currentRoute:l,listening:!0,addRoute:h,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:y,resolve:x,options:e,push:C,replace:P,go:Ne,back:()=>Ne(-1),forward:()=>Ne(1),beforeEach:s.add,beforeResolve:i.add,afterEach:a.add,onError:ue.add,isReady:qe,install(k){const Y=this;k.component("RouterLink",b_),k.component("RouterView",C_),k.config.globalProperties.$router=Y,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>f(l)}),Bn&&!Ie&&l.value===rn&&(Ie=!0,C(o.location).catch(ge=>{}));const V={};for(const ge in rn)Object.defineProperty(V,ge,{get:()=>l.value[ge],enumerable:!0});k.provide(No,Y),k.provide(Ci,Ml(V)),k.provide(Ls,l);const Q=k.unmount;Fe.add(k),k.unmount=function(){Fe.delete(k),Fe.size<1&&(c=rn,Ce&&Ce(),Ce=null,l.value=rn,Ie=!1,re=!1),Q()}}};function Xe(k){return k.reduce((Y,V)=>Y.then(()=>T(V)),Promise.resolve())}return Nt}function E_(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const a=t.matched[i];a&&(e.matched.find(c=>Kn(c,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(c=>Kn(c,l))||o.push(l))}return[n,r,o]}function Nc(){return ot(No)}function A_(e){return ot(Ci)}const jc=D({__name:"Avatar",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),N(f(Cm),{"data-slot":"avatar",class:ye(f(ce)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t.class))},{default:b(()=>[q(n.$slots,"default")]),_:3},8,["class"]))}}),zc=D({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(Em),oe({"data-slot":"avatar-fallback"},f(n),{class:f(ce)("bg-muted flex size-full items-center justify-center rounded-full",t.class)}),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),Vc=D({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,r)=>(O(),N(f(Om),oe({"data-slot":"avatar-image"},t,{class:"aspect-square size-full"}),{default:b(()=>[q(n.$slots,"default")]),_:3},16))}}),k_={class:"flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground"},O_={class:"grid flex-1 text-left text-sm leading-tight"},P_={class:"truncate font-semibold"},M_={class:"truncate text-xs"},T_=D({__name:"AppSidebar",setup(e){Nc();const t=G(null),n=[{title:"仪表盘",url:"/dashboard",icon:By,roles:["admin","enterprise_manager","sales_engineer","production_scheduler","quality_engineer","warehouse_manager","procurement_manager","finance_manager","production_supervisor"]},{title:"客户关系",url:"/crm",icon:Wy,roles:["admin","enterprise_manager","sales_engineer","finance_manager"]},{title:"库存管理",url:"/inventory",icon:Ny,roles:["admin","enterprise_manager","warehouse_manager","procurement_manager","production_scheduler"]},{title:"生产执行",url:"/mes",icon:Sc,roles:["admin","enterprise_manager","production_scheduler","production_supervisor","cutting_operator","tempering_operator","laminating_operator"]},{title:"采购管理",url:"/procurement",icon:Ac,roles:["admin","enterprise_manager","procurement_manager","warehouse_manager"]},{title:"质量管理",url:"/quality",icon:Iy,roles:["admin","enterprise_manager","quality_engineer","production_supervisor"]}],r=[{title:"用户管理",url:"/admin/users",icon:Hy,roles:["admin"]},{title:"元数据管理",url:"/admin/metadata",icon:$y,roles:["admin"]},{title:"系统设置",url:"/admin/settings",icon:Ec,roles:["admin"]}],o=U(()=>t.value?n.filter(a=>a.roles.some(l=>t.value?.roles.includes(l))):[]),s=U(()=>t.value?.roles.includes("admin")||!1),i=async()=>{try{const a=localStorage.getItem("currentUser");if(a)t.value=JSON.parse(a);else{const u=(await(await fetch("/mock/common/users.json")).json()).data.find(d=>d.username==="admin");u&&(t.value=u,localStorage.setItem("currentUser",JSON.stringify(u)))}}catch(a){console.error("Failed to load current user:",a)}};return ht(()=>{i()}),(a,l)=>{const c=Zs("router-link");return O(),N(f(ub),{variant:"inset"},{default:b(()=>[E(f(fb),null,{default:b(()=>[E(f(Yr),null,{default:b(()=>[E(f(Jr),null,{default:b(()=>[E(f(Xr),{size:"lg","as-child":""},{default:b(()=>[E(c,{to:"/",class:"flex items-center gap-2"},{default:b(()=>[ee("div",k_,[E(f(My),{class:"size-4"})]),l[0]||(l[0]=ee("div",{class:"grid flex-1 text-left text-sm leading-tight"},[ee("span",{class:"truncate font-semibold"},"玻璃ERP系统"),ee("span",{class:"truncate text-xs"},"Glass Manufacturing")],-1))]),_:1,__:[0]})]),_:1})]),_:1})]),_:1})]),_:1}),E(f(cb),null,{default:b(()=>[E(f(Da),null,{default:b(()=>[E(f($a),null,{default:b(()=>l[1]||(l[1]=[Me("主要功能",-1)])),_:1,__:[1]}),E(f(Ia),null,{default:b(()=>[E(f(Yr),null,{default:b(()=>[(O(!0),pe(ze,null,vs(o.value,u=>(O(),N(f(Jr),{key:u.title},{default:b(()=>[E(f(Xr),{"as-child":"",tooltip:u.title},{default:b(()=>[E(c,{to:u.url,class:"flex items-center gap-2"},{default:b(()=>[(O(),N(xr(u.icon),{class:"size-4"})),ee("span",null,Ue(u.title),1)]),_:2},1032,["to"])]),_:2},1032,["tooltip"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1}),s.value?(O(),N(f(Da),{key:0},{default:b(()=>[E(f($a),null,{default:b(()=>l[2]||(l[2]=[Me("系统管理",-1)])),_:1,__:[2]}),E(f(Ia),null,{default:b(()=>[E(f(Yr),null,{default:b(()=>[(O(),pe(ze,null,vs(r,u=>E(f(Jr),{key:u.title},{default:b(()=>[E(f(Xr),{"as-child":"",tooltip:u.title},{default:b(()=>[E(c,{to:u.url,class:"flex items-center gap-2"},{default:b(()=>[(O(),N(xr(u.icon),{class:"size-4"})),ee("span",null,Ue(u.title),1)]),_:2},1032,["to"])]),_:2},1032,["tooltip"])]),_:2},1024)),64))]),_:1})]),_:1})]),_:1})):Rr("",!0)]),_:1}),E(f(db),null,{default:b(()=>[E(f(Yr),null,{default:b(()=>[E(f(Jr),null,{default:b(()=>[E(f(Xr),{size:"lg",class:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"},{default:b(()=>[E(f(jc),{class:"h-8 w-8 rounded-lg"},{default:b(()=>[E(f(Vc),{src:t.value?.avatar||"",alt:t.value?.name||""},null,8,["src","alt"]),E(f(zc),{class:"rounded-lg"},{default:b(()=>[Me(Ue(t.value?.name?.charAt(0)||"U"),1)]),_:1})]),_:1}),ee("div",O_,[ee("span",P_,Ue(t.value?.name||"未登录"),1),ee("span",M_,Ue(t.value?.department||""),1)]),E(f(Ry),{class:"ml-auto size-4"})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}}),R_=D({__name:"Breadcrumb",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:ye(t.class)},[q(n.$slots,"default")],2))}}),el=D({__name:"BreadcrumbItem",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("li",{"data-slot":"breadcrumb-item",class:ye(f(ce)("inline-flex items-center gap-1.5",t.class))},[q(n.$slots,"default")],2))}}),D_=D({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(e){const t=e;return(n,r)=>(O(),N(f(Pe),{"data-slot":"breadcrumb-link",as:n.as,"as-child":n.asChild,class:ye(f(ce)("hover:text-foreground transition-colors",t.class))},{default:b(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),I_=D({__name:"BreadcrumbList",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("ol",{"data-slot":"breadcrumb-list",class:ye(f(ce)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t.class))},[q(n.$slots,"default")],2))}}),$_=D({__name:"BreadcrumbPage",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:ye(f(ce)("text-foreground font-normal",t.class))},[q(n.$slots,"default")],2))}}),q_=D({__name:"BreadcrumbSeparator",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:ye(f(ce)("[&>svg]:size-3.5",t.class))},[q(n.$slots,"default",{},()=>[E(f(Ty))])],2))}}),B_=D({__name:"Badge",props:{asChild:{type:Boolean},as:{},variant:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(Pe),oe({"data-slot":"badge",class:f(ce)(f(F_)({variant:r.variant}),t.class)},f(n)),{default:b(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),F_=oi("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}}),tl=D({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const o=At(e,t);return(s,i)=>(O(),N(f(Ov),oe({"data-slot":"dropdown-menu"},f(o)),{default:b(()=>[q(s.$slots,"default")]),_:3},16))}}),nl=D({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},sideFlip:{type:Boolean},align:{},alignOffset:{},alignFlip:{type:Boolean},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,o=vt(n,"class"),s=At(o,r);return(i,a)=>(O(),N(f(qv),null,{default:b(()=>[E(f(Mv),oe({"data-slot":"dropdown-menu-content"},f(s),{class:f(ce)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--reka-dropdown-menu-content-available-height) min-w-[8rem] origin-(--reka-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",n.class)}),{default:b(()=>[q(i.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),Qr=D({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean},variant:{default:"default"}},setup(e){const t=e,n=vt(t,"inset","variant","class"),r=Br(n);return(o,s)=>(O(),N(f(Rv),oe({"data-slot":"dropdown-menu-item","data-inset":o.inset?"":void 0,"data-variant":o.variant},f(r),{class:f(ce)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t.class)}),{default:b(()=>[q(o.$slots,"default")]),_:3},16,["data-inset","data-variant","class"]))}}),rl=D({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(e){const t=e,n=vt(t,"class","inset"),r=Br(n);return(o,s)=>(O(),N(f(Iv),oe({"data-slot":"dropdown-menu-label","data-inset":o.inset?"":void 0},f(r),{class:f(ce)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t.class)}),{default:b(()=>[q(o.$slots,"default")]),_:3},16,["data-inset","class"]))}}),Zr=D({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,o)=>(O(),N(f(Fv),oe({"data-slot":"dropdown-menu-separator"},f(n),{class:f(ce)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),ol=D({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const n=Br(e);return(r,o)=>(O(),N(f(Nv),oe({"data-slot":"dropdown-menu-trigger"},f(n)),{default:b(()=>[q(r.$slots,"default")]),_:3},16))}}),L_={class:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12"},N_={class:"flex items-center gap-2 px-4"},j_={class:"ml-auto flex items-center gap-2 px-4"},z_={class:"relative hidden md:block"},V_={class:"max-h-80 overflow-y-auto"},H_={key:0,class:"p-4 text-center text-sm text-muted-foreground"},U_={key:1},W_=["onClick"],K_={class:"flex-shrink-0"},G_={class:"flex-1 space-y-1"},Y_={class:"text-sm font-medium"},X_={class:"text-xs text-muted-foreground"},J_={class:"text-xs text-muted-foreground"},Q_={class:"flex flex-col space-y-1"},Z_={class:"text-sm font-medium leading-none"},ew={class:"text-xs leading-none text-muted-foreground"},tw=D({__name:"AppHeader",setup(e){const t=A_(),n=Nc(),r=G(null),o=G(""),s=G([]),i={"/":"首页","/dashboard":"仪表盘","/crm":"客户关系","/inventory":"库存管理","/mes":"生产执行","/procurement":"采购管理","/quality":"质量管理","/admin/users":"用户管理","/admin/metadata":"元数据管理","/admin/settings":"系统设置"},a=U(()=>i[t.path]||"未知页面"),l=U(()=>s.value.filter(C=>!C.read).length),c=C=>{switch(C){case"warning":return kc;case"success":return Cc;case"error":return Dy;default:return qy}},u=C=>{switch(C){case"warning":return"text-yellow-500";case"success":return"text-green-500";case"error":return"text-red-500";default:return"text-blue-500"}},d=C=>{const P=new Date(C),F=new Date().getTime()-P.getTime(),$=Math.floor(F/(1e3*60)),T=Math.floor(F/(1e3*60*60)),Z=Math.floor(F/(1e3*60*60*24));return $<1?"刚刚":$<60?`${$}分钟前`:T<24?`${T}小时前`:Z<7?`${Z}天前`:P.toLocaleDateString()},p=()=>{o.value.trim()&&n.push(`/search?q=${encodeURIComponent(o.value)}`)},h=C=>{const P=s.value.find(H=>H.id===C);P&&(P.read=!0)},g=()=>{s.value.forEach(C=>C.read=!0)},y=()=>{localStorage.removeItem("currentUser"),r.value=null,n.push("/login")},_=()=>{const C=document.documentElement;C.classList.contains("dark")?(C.classList.remove("dark"),localStorage.setItem("theme","light")):(C.classList.add("dark"),localStorage.setItem("theme","dark"))},x=async()=>{try{const C=localStorage.getItem("currentUser");if(C)r.value=JSON.parse(C);else{const F=(await(await fetch("/mock/common/users.json")).json()).data.find($=>$.username==="admin");F&&(r.value=F,localStorage.setItem("currentUser",JSON.stringify(F)))}}catch(C){console.error("Failed to load current user:",C)}},A=()=>{s.value=[{id:"1",type:"warning",title:"库存预警",message:"6mm透明浮法玻璃库存不足，当前库存：15片",read:!1,createdAt:new Date(Date.now()-1800*1e3).toISOString()},{id:"2",type:"info",title:"新订单",message:'客户"建筑公司A"提交了新的玻璃加工订单',read:!1,createdAt:new Date(Date.now()-7200*1e3).toISOString()},{id:"3",type:"success",title:"生产完成",message:"订单#2024001的钢化玻璃生产已完成",read:!0,createdAt:new Date(Date.now()-14400*1e3).toISOString()},{id:"4",type:"error",title:"设备故障",message:"钢化炉#2出现温度异常，请及时检修",read:!1,createdAt:new Date(Date.now()-360*60*1e3).toISOString()}]},S=()=>{const C=localStorage.getItem("theme"),P=window.matchMedia("(prefers-color-scheme: dark)").matches;(C==="dark"||!C&&P)&&document.documentElement.classList.add("dark")};return ht(()=>{x(),A(),S()}),(C,P)=>{const H=Zs("router-link");return O(),pe("header",L_,[ee("div",N_,[E(f(wb),{class:"-ml-1"}),E(f(bb),{orientation:"vertical",class:"mr-2 h-4"}),E(f(R_),null,{default:b(()=>[E(f(I_),null,{default:b(()=>[E(f(el),{class:"hidden md:block"},{default:b(()=>[E(f(D_),{href:"/"},{default:b(()=>P[1]||(P[1]=[Me(" 首页 ",-1)])),_:1,__:[1]})]),_:1}),E(f(q_),{class:"hidden md:block"}),E(f(el),null,{default:b(()=>[E(f($_),null,{default:b(()=>[Me(Ue(a.value),1)]),_:1})]),_:1})]),_:1})]),_:1})]),ee("div",j_,[ee("div",z_,[E(f(zy),{class:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),E(f(pb),{type:"search",placeholder:"搜索功能...",class:"w-[200px] pl-8 lg:w-[300px]",modelValue:o.value,"onUpdate:modelValue":P[0]||(P[0]=F=>o.value=F),onKeyup:Cu(p,["enter"])},null,8,["modelValue"])]),E(f(tl),null,{default:b(()=>[E(f(ol),{"as-child":""},{default:b(()=>[E(f(lr),{variant:"outline",size:"icon",class:"relative"},{default:b(()=>[E(f(Py),{class:"h-4 w-4"}),l.value>0?(O(),N(f(B_),{key:0,class:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs",variant:"destructive"},{default:b(()=>[Me(Ue(l.value>9?"9+":l.value),1)]),_:1})):Rr("",!0)]),_:1})]),_:1}),E(f(nl),{align:"end",class:"w-80"},{default:b(()=>[E(f(rl),{class:"flex items-center justify-between"},{default:b(()=>[P[3]||(P[3]=Me(" 通知中心 ",-1)),E(f(lr),{variant:"ghost",size:"sm",onClick:g},{default:b(()=>P[2]||(P[2]=[Me(" 全部已读 ",-1)])),_:1,__:[2]})]),_:1,__:[3]}),E(f(Zr)),ee("div",V_,[s.value.length===0?(O(),pe("div",H_," 暂无通知 ")):(O(),pe("div",U_,[(O(!0),pe(ze,null,vs(s.value.slice(0,5),F=>(O(),pe("div",{key:F.id,class:ye(["flex items-start gap-3 p-3 hover:bg-accent cursor-pointer",{"bg-accent/50":!F.read}]),onClick:$=>h(F.id)},[ee("div",K_,[(O(),N(xr(c(F.type)),{class:ye(["h-4 w-4",u(F.type)])},null,8,["class"]))]),ee("div",G_,[ee("p",Y_,Ue(F.title),1),ee("p",X_,Ue(F.message),1),ee("p",J_,Ue(d(F.createdAt)),1)])],10,W_))),128))]))]),E(f(Zr)),E(f(Qr),{"as-child":""},{default:b(()=>[E(H,{to:"/notifications",class:"w-full text-center"},{default:b(()=>P[4]||(P[4]=[Me(" 查看全部通知 ",-1)])),_:1,__:[4]})]),_:1})]),_:1})]),_:1}),E(f(tl),null,{default:b(()=>[E(f(ol),{"as-child":""},{default:b(()=>[E(f(lr),{variant:"outline",size:"icon"},{default:b(()=>[E(f(jc),{class:"h-8 w-8"},{default:b(()=>[E(f(Vc),{src:r.value?.avatar||"",alt:r.value?.name||""},null,8,["src","alt"]),E(f(zc),null,{default:b(()=>[Me(Ue(r.value?.name?.charAt(0)||"U"),1)]),_:1})]),_:1})]),_:1})]),_:1}),E(f(nl),{align:"end",class:"w-56"},{default:b(()=>[E(f(rl),{class:"font-normal"},{default:b(()=>[ee("div",Q_,[ee("p",Z_,Ue(r.value?.name||"未登录"),1),ee("p",ew,Ue(r.value?.email||""),1)])]),_:1}),E(f(Zr)),E(f(Qr),{"as-child":""},{default:b(()=>[E(H,{to:"/profile",class:"flex items-center"},{default:b(()=>[E(f(Uy),{class:"mr-2 h-4 w-4"}),P[5]||(P[5]=Me(" 个人资料 ",-1))]),_:1,__:[5]})]),_:1}),E(f(Qr),{"as-child":""},{default:b(()=>[E(H,{to:"/settings",class:"flex items-center"},{default:b(()=>[E(f(Ec),{class:"mr-2 h-4 w-4"}),P[6]||(P[6]=Me(" 设置 ",-1))]),_:1,__:[6]})]),_:1}),E(f(Zr)),E(f(Qr),{onClick:y,class:"text-red-600"},{default:b(()=>[E(f(Fy),{class:"mr-2 h-4 w-4"}),P[7]||(P[7]=Me(" 退出登录 ",-1))]),_:1,__:[7]})]),_:1})]),_:1}),E(f(lr),{variant:"outline",size:"icon",onClick:_},{default:b(()=>[E(f(Vy),{class:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),E(f(Ly),{class:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"})]),_:1})])])}}}),nw={class:"min-h-screen bg-background"},rw={class:"flex-1 space-y-4 p-4 md:p-6 lg:p-8"},ow=D({__name:"AppLayout",setup(e){const t=G(window.innerWidth),n=U(()=>t.value<768),r=()=>{t.value=window.innerWidth};return ht(()=>{window.addEventListener("resize",r)}),Sn(()=>{window.removeEventListener("resize",r)}),(o,s)=>{const i=Zs("router-view");return O(),pe("div",nw,[E(f(yb),{"default-open":!n.value},{default:b(()=>[E(T_),E(f(hb),null,{default:b(()=>[E(tw),ee("main",rw,[E(i)])]),_:1})]),_:1},8,["default-open"])])}}}),sw=D({__name:"App",setup(e){return(t,n)=>(O(),N(ow))}}),iw="modulepreload",aw=function(e){return"/"+e},sl={},ir=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){let l=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=i?.nonce||i?.getAttribute("nonce");o=l(n.map(c=>{if(c=aw(c),c in sl)return;sl[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const p=document.createElement("link");if(p.rel=u?"stylesheet":iw,u||(p.as="script"),p.crossOrigin="",p.href=c,a&&p.setAttribute("nonce",a),document.head.appendChild(p),u)return new Promise((h,g)=>{p.addEventListener("load",h),p.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${c}`)))})}))}function s(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return o.then(i=>{for(const a of i||[])a.status==="rejected"&&s(a.reason);return t().catch(s)})},Mn=D({__name:"Card",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"card",class:ye(f(ce)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t.class))},[q(n.$slots,"default")],2))}}),Tn=D({__name:"CardContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"card-content",class:ye(f(ce)("px-6",t.class))},[q(n.$slots,"default")],2))}}),Rn=D({__name:"CardHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("div",{"data-slot":"card-header",class:ye(f(ce)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t.class))},[q(n.$slots,"default")],2))}}),Dn=D({__name:"CardTitle",props:{class:{}},setup(e){const t=e;return(n,r)=>(O(),pe("h3",{"data-slot":"card-title",class:ye(f(ce)("leading-none font-semibold",t.class))},[q(n.$slots,"default")],2))}}),lw={class:"space-y-6"},uw={class:"grid gap-4 md:grid-cols-2 lg:grid-cols-4"},cw={class:"grid gap-4 md:grid-cols-2 lg:grid-cols-7"},dw=D({__name:"DashboardView",setup(e){return(t,n)=>(O(),pe("div",lw,[n[12]||(n[12]=ee("div",{class:"flex items-center justify-between"},[ee("h1",{class:"text-3xl font-bold tracking-tight"},"仪表盘")],-1)),ee("div",uw,[E(f(Mn),null,{default:b(()=>[E(f(Rn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:b(()=>[E(f(Dn),{class:"text-sm font-medium"},{default:b(()=>n[0]||(n[0]=[Me("今日订单",-1)])),_:1,__:[0]}),E(f(Ac),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f(Tn),null,{default:b(()=>n[1]||(n[1]=[ee("div",{class:"text-2xl font-bold"},"12",-1),ee("p",{class:"text-xs text-muted-foreground"}," +20.1% 较昨日 ",-1)])),_:1,__:[1]})]),_:1}),E(f(Mn),null,{default:b(()=>[E(f(Rn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:b(()=>[E(f(Dn),{class:"text-sm font-medium"},{default:b(()=>n[2]||(n[2]=[Me("生产进度",-1)])),_:1,__:[2]}),E(f(Sc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f(Tn),null,{default:b(()=>n[3]||(n[3]=[ee("div",{class:"text-2xl font-bold"},"85%",-1),ee("p",{class:"text-xs text-muted-foreground"}," +5% 较昨日 ",-1)])),_:1,__:[3]})]),_:1}),E(f(Mn),null,{default:b(()=>[E(f(Rn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:b(()=>[E(f(Dn),{class:"text-sm font-medium"},{default:b(()=>n[4]||(n[4]=[Me("库存预警",-1)])),_:1,__:[4]}),E(f(kc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f(Tn),null,{default:b(()=>n[5]||(n[5]=[ee("div",{class:"text-2xl font-bold"},"3",-1),ee("p",{class:"text-xs text-muted-foreground"}," 需要补货的物料 ",-1)])),_:1,__:[5]})]),_:1}),E(f(Mn),null,{default:b(()=>[E(f(Rn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:b(()=>[E(f(Dn),{class:"text-sm font-medium"},{default:b(()=>n[6]||(n[6]=[Me("质量合格率",-1)])),_:1,__:[6]}),E(f(Cc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f(Tn),null,{default:b(()=>n[7]||(n[7]=[ee("div",{class:"text-2xl font-bold"},"98.5%",-1),ee("p",{class:"text-xs text-muted-foreground"}," +0.2% 较昨日 ",-1)])),_:1,__:[7]})]),_:1})]),ee("div",cw,[E(f(Mn),{class:"col-span-4"},{default:b(()=>[E(f(Rn),null,{default:b(()=>[E(f(Dn),null,{default:b(()=>n[8]||(n[8]=[Me("生产概览",-1)])),_:1,__:[8]})]),_:1}),E(f(Tn),{class:"pl-2"},{default:b(()=>n[9]||(n[9]=[ee("div",{class:"h-[200px] flex items-center justify-center text-muted-foreground"}," 生产数据图表区域 ",-1)])),_:1,__:[9]})]),_:1}),E(f(Mn),{class:"col-span-3"},{default:b(()=>[E(f(Rn),null,{default:b(()=>[E(f(Dn),null,{default:b(()=>n[10]||(n[10]=[Me("最近活动",-1)])),_:1,__:[10]})]),_:1}),E(f(Tn),null,{default:b(()=>n[11]||(n[11]=[ee("div",{class:"space-y-4"},[ee("div",{class:"flex items-center"},[ee("div",{class:"ml-4 space-y-1"},[ee("p",{class:"text-sm font-medium leading-none"}," 订单 #2024001 已完成生产 "),ee("p",{class:"text-sm text-muted-foreground"}," 2小时前 ")])]),ee("div",{class:"flex items-center"},[ee("div",{class:"ml-4 space-y-1"},[ee("p",{class:"text-sm font-medium leading-none"}," 新客户注册：建筑公司B "),ee("p",{class:"text-sm text-muted-foreground"}," 4小时前 ")])]),ee("div",{class:"flex items-center"},[ee("div",{class:"ml-4 space-y-1"},[ee("p",{class:"text-sm font-medium leading-none"}," 库存补货：6mm透明玻璃 "),ee("p",{class:"text-sm text-muted-foreground"}," 6小时前 ")])])],-1)])),_:1,__:[11]})]),_:1})])]))}}),fw=S_({history:Zb("/"),routes:[{path:"/",redirect:"/dashboard"},{path:"/dashboard",name:"dashboard",component:dw},{path:"/crm",name:"crm",component:()=>ir(()=>import("./CrmView-1fx4fd9T.js"),__vite__mapDeps([0,1]))},{path:"/inventory",name:"inventory",component:()=>ir(()=>import("./InventoryView-NbnKMQZj.js"),__vite__mapDeps([2,1]))},{path:"/mes",name:"mes",component:()=>ir(()=>import("./MesView-Dx3BE192.js"),__vite__mapDeps([3,1]))},{path:"/procurement",name:"procurement",component:()=>ir(()=>import("./ProcurementView-Dhc6W5X0.js"),__vite__mapDeps([4,1]))},{path:"/quality",name:"quality",component:()=>ir(()=>import("./QualityView-B-Okd0Mt.js"),__vite__mapDeps([5,1]))}]}),Si=up(sw);Si.use(pp());Si.use(fw);Si.mount("#app");export{lr as _,ee as a,E as b,pe as c,D as d,Mn as e,Me as f,Rn as g,Dn as h,Tn as i,Te as j,ce as k,ye as n,O as o,q as r,f as u,b as w};
