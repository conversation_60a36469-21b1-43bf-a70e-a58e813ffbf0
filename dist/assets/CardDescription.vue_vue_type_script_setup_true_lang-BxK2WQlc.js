import{j as t,d as o,c as r,n,u as c,k as l,r as p,o as d}from"./index-xR3mVGqH.js";/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=t("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),f=o({__name:"CardDescription",props:{class:{}},setup(s){const e=s;return(a,u)=>(d(),r("p",{"data-slot":"card-description",class:n(c(l)("text-muted-foreground text-sm",e.class))},[p(a.$slots,"default")],2))}});export{m as P,f as _};
