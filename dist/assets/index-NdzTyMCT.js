const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/CrmView-DZbhcTa2.js","assets/CardDescription.vue_vue_type_script_setup_true_lang-Cwfa4Smo.js","assets/InventoryView---4uNayw.js","assets/MesView-2a1sUtv7.js","assets/ProcurementView-crMoVyzN.js","assets/QualityView-BP4ZT7mq.js"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ei(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Pe={},zn=[],$t=()=>{},ud=()=>!1,ws=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ti=e=>e.startsWith("onUpdate:"),Xe=Object.assign,ni=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},cd=Object.prototype.hasOwnProperty,Oe=(e,t)=>cd.call(e,t),ce=Array.isArray,Vn=e=>xs(e)==="[object Map]",xl=e=>xs(e)==="[object Set]",fe=e=>typeof e=="function",Le=e=>typeof e=="string",en=e=>typeof e=="symbol",De=e=>e!==null&&typeof e=="object",Cl=e=>(De(e)||fe(e))&&fe(e.then)&&fe(e.catch),Sl=Object.prototype.toString,xs=e=>Sl.call(e),dd=e=>xs(e).slice(8,-1),El=e=>xs(e)==="[object Object]",ri=e=>Le(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,fr=ei(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Cs=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},fd=/-(\w)/g,lt=Cs(e=>e.replace(fd,(t,n)=>n?n.toUpperCase():"")),pd=/\B([A-Z])/g,gn=Cs(e=>e.replace(pd,"-$1").toLowerCase()),Ss=Cs(e=>e.charAt(0).toUpperCase()+e.slice(1)),pr=Cs(e=>e?`on${Ss(e)}`:""),dn=(e,t)=>!Object.is(e,t),is=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Co=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},So=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ji;const Es=()=>ji||(ji=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function vn(e){if(ce(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Le(r)?vd(r):vn(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(Le(e)||De(e))return e}const hd=/;(?![^(]*\))/g,md=/:([^]+)/,gd=/\/\*[^]*?\*\//g;function vd(e){const t={};return e.replace(gd,"").split(hd).forEach(n=>{if(n){const r=n.split(md);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Ee(e){let t="";if(Le(e))t=e;else if(ce(e))for(let n=0;n<e.length;n++){const r=Ee(e[n]);r&&(t+=r+" ")}else if(De(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function rt(e){if(!e)return null;let{class:t,style:n}=e;return t&&!Le(t)&&(e.class=Ee(t)),n&&(e.style=vn(n)),e}const yd="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",bd=ei(yd);function Al(e){return!!e||e===""}const Ol=e=>!!(e&&e.__v_isRef===!0),Ve=e=>Le(e)?e:e==null?"":ce(e)||De(e)&&(e.toString===Sl||!fe(e.toString))?Ol(e)?Ve(e.value):JSON.stringify(e,kl,2):String(e),kl=(e,t)=>Ol(t)?kl(e,t.value):Vn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[Js(r,o)+" =>"]=s,n),{})}:xl(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Js(n))}:en(t)?Js(t):De(t)&&!ce(t)&&!El(t)?String(t):t,Js=(e,t="")=>{var n;return en(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ge;class Pl{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ge,!t&&Ge&&(this.index=(Ge.scopes||(Ge.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ge;try{return Ge=this,t()}finally{Ge=n}}}on(){++this._on===1&&(this.prevScope=Ge,Ge=this)}off(){this._on>0&&--this._on===0&&(Ge=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function As(e){return new Pl(e)}function $r(){return Ge}function Os(e,t=!1){Ge&&Ge.cleanups.push(e)}let Me;const Xs=new WeakSet;class Rl{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ge&&Ge.active&&Ge.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xs.has(this)&&(Xs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ml(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,zi(this),Dl(this);const t=Me,n=bt;Me=this,bt=!0;try{return this.fn()}finally{Il(this),Me=t,bt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ii(t);this.deps=this.depsTail=void 0,zi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Eo(this)&&this.run()}get dirty(){return Eo(this)}}let Tl=0,hr,mr;function Ml(e,t=!1){if(e.flags|=8,t){e.next=mr,mr=e;return}e.next=hr,hr=e}function si(){Tl++}function oi(){if(--Tl>0)return;if(mr){let t=mr;for(mr=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;hr;){let t=hr;for(hr=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Dl(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Il(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),ii(r),_d(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function Eo(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&($l(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function $l(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Cr)||(e.globalVersion=Cr,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Eo(e))))return;e.flags|=2;const t=e.dep,n=Me,r=bt;Me=e,bt=!0;try{Dl(e);const s=e.fn(e._value);(t.version===0||dn(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{Me=n,bt=r,Il(e),e.flags&=-3}}function ii(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)ii(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function _d(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let bt=!0;const Bl=[];function Yt(){Bl.push(bt),bt=!1}function Jt(){const e=Bl.pop();bt=e===void 0?!0:e}function zi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Me;Me=void 0;try{t()}finally{Me=n}}}let Cr=0;class wd{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ks{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Me||!bt||Me===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Me)n=this.activeLink=new wd(Me,this),Me.deps?(n.prevDep=Me.depsTail,Me.depsTail.nextDep=n,Me.depsTail=n):Me.deps=Me.depsTail=n,ql(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Me.depsTail,n.nextDep=void 0,Me.depsTail.nextDep=n,Me.depsTail=n,Me.deps===n&&(Me.deps=r)}return n}trigger(t){this.version++,Cr++,this.notify(t)}notify(t){si();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{oi()}}}function ql(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)ql(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const cs=new WeakMap,Cn=Symbol(""),Ao=Symbol(""),Sr=Symbol("");function Ye(e,t,n){if(bt&&Me){let r=cs.get(e);r||cs.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new ks),s.map=r,s.key=n),s.track()}}function Wt(e,t,n,r,s,o){const i=cs.get(e);if(!i){Cr++;return}const a=l=>{l&&l.trigger()};if(si(),t==="clear")i.forEach(a);else{const l=ce(e),c=l&&ri(n);if(l&&n==="length"){const u=Number(r);i.forEach((d,p)=>{(p==="length"||p===Sr||!en(p)&&p>=u)&&a(d)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),c&&a(i.get(Sr)),t){case"add":l?c&&a(i.get("length")):(a(i.get(Cn)),Vn(e)&&a(i.get(Ao)));break;case"delete":l||(a(i.get(Cn)),Vn(e)&&a(i.get(Ao)));break;case"set":Vn(e)&&a(i.get(Cn));break}}oi()}function xd(e,t){const n=cs.get(e);return n&&n.get(t)}function Pn(e){const t=Ce(e);return t===e?t:(Ye(t,"iterate",Sr),yt(e)?t:t.map(He))}function Ps(e){return Ye(e=Ce(e),"iterate",Sr),e}const Cd={__proto__:null,[Symbol.iterator](){return Qs(this,Symbol.iterator,He)},concat(...e){return Pn(this).concat(...e.map(t=>ce(t)?Pn(t):t))},entries(){return Qs(this,"entries",e=>(e[1]=He(e[1]),e))},every(e,t){return jt(this,"every",e,t,void 0,arguments)},filter(e,t){return jt(this,"filter",e,t,n=>n.map(He),arguments)},find(e,t){return jt(this,"find",e,t,He,arguments)},findIndex(e,t){return jt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return jt(this,"findLast",e,t,He,arguments)},findLastIndex(e,t){return jt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return jt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Zs(this,"includes",e)},indexOf(...e){return Zs(this,"indexOf",e)},join(e){return Pn(this).join(e)},lastIndexOf(...e){return Zs(this,"lastIndexOf",e)},map(e,t){return jt(this,"map",e,t,void 0,arguments)},pop(){return sr(this,"pop")},push(...e){return sr(this,"push",e)},reduce(e,...t){return Vi(this,"reduce",e,t)},reduceRight(e,...t){return Vi(this,"reduceRight",e,t)},shift(){return sr(this,"shift")},some(e,t){return jt(this,"some",e,t,void 0,arguments)},splice(...e){return sr(this,"splice",e)},toReversed(){return Pn(this).toReversed()},toSorted(e){return Pn(this).toSorted(e)},toSpliced(...e){return Pn(this).toSpliced(...e)},unshift(...e){return sr(this,"unshift",e)},values(){return Qs(this,"values",He)}};function Qs(e,t,n){const r=Ps(e),s=r[t]();return r!==e&&!yt(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Sd=Array.prototype;function jt(e,t,n,r,s,o){const i=Ps(e),a=i!==e&&!yt(e),l=i[t];if(l!==Sd[t]){const d=l.apply(e,o);return a?He(d):d}let c=n;i!==e&&(a?c=function(d,p){return n.call(this,He(d),p,e)}:n.length>2&&(c=function(d,p){return n.call(this,d,p,e)}));const u=l.call(i,c,r);return a&&s?s(u):u}function Vi(e,t,n,r){const s=Ps(e);let o=n;return s!==e&&(yt(e)?n.length>3&&(o=function(i,a,l){return n.call(this,i,a,l,e)}):o=function(i,a,l){return n.call(this,i,He(a),l,e)}),s[t](o,...r)}function Zs(e,t,n){const r=Ce(e);Ye(r,"iterate",Sr);const s=r[t](...n);return(s===-1||s===!1)&&ai(n[0])?(n[0]=Ce(n[0]),r[t](...n)):s}function sr(e,t,n=[]){Yt(),si();const r=Ce(e)[t].apply(e,n);return oi(),Jt(),r}const Ed=ei("__proto__,__v_isRef,__isVue"),Ll=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(en));function Ad(e){en(e)||(e=String(e));const t=Ce(this);return Ye(t,"has",e),t.hasOwnProperty(e)}class Nl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?Ul:Hl:o?Vl:zl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=ce(t);if(!s){let l;if(i&&(l=Cd[n]))return l;if(n==="hasOwnProperty")return Ad}const a=Reflect.get(t,n,Re(t)?t:r);return(en(n)?Ll.has(n):Ed(n))||(s||Ye(t,"get",n),o)?a:Re(a)?i&&ri(n)?a:a.value:De(a)?s?Ts(a):Xt(a):a}}class Fl extends Nl{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const l=pn(o);if(!yt(r)&&!pn(r)&&(o=Ce(o),r=Ce(r)),!ce(t)&&Re(o)&&!Re(r))return l?!1:(o.value=r,!0)}const i=ce(t)&&ri(n)?Number(n)<t.length:Oe(t,n),a=Reflect.set(t,n,r,Re(t)?t:s);return t===Ce(s)&&(i?dn(r,o)&&Wt(t,"set",n,r):Wt(t,"add",n,r)),a}deleteProperty(t,n){const r=Oe(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&Wt(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!en(n)||!Ll.has(n))&&Ye(t,"has",n),r}ownKeys(t){return Ye(t,"iterate",ce(t)?"length":Cn),Reflect.ownKeys(t)}}class jl extends Nl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Od=new Fl,kd=new jl,Pd=new Fl(!0),Rd=new jl(!0),Oo=e=>e,Hr=e=>Reflect.getPrototypeOf(e);function Td(e,t,n){return function(...r){const s=this.__v_raw,o=Ce(s),i=Vn(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,c=s[e](...r),u=n?Oo:t?ds:He;return!t&&Ye(o,"iterate",l?Ao:Cn),{next(){const{value:d,done:p}=c.next();return p?{value:d,done:p}:{value:a?[u(d[0]),u(d[1])]:u(d),done:p}},[Symbol.iterator](){return this}}}}function Ur(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Md(e,t){const n={get(s){const o=this.__v_raw,i=Ce(o),a=Ce(s);e||(dn(s,a)&&Ye(i,"get",s),Ye(i,"get",a));const{has:l}=Hr(i),c=t?Oo:e?ds:He;if(l.call(i,s))return c(o.get(s));if(l.call(i,a))return c(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&Ye(Ce(s),"iterate",Cn),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=Ce(o),a=Ce(s);return e||(dn(s,a)&&Ye(i,"has",s),Ye(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,l=Ce(a),c=t?Oo:e?ds:He;return!e&&Ye(l,"iterate",Cn),a.forEach((u,d)=>s.call(o,c(u),c(d),i))}};return Xe(n,e?{add:Ur("add"),set:Ur("set"),delete:Ur("delete"),clear:Ur("clear")}:{add(s){!t&&!yt(s)&&!pn(s)&&(s=Ce(s));const o=Ce(this);return Hr(o).has.call(o,s)||(o.add(s),Wt(o,"add",s,s)),this},set(s,o){!t&&!yt(o)&&!pn(o)&&(o=Ce(o));const i=Ce(this),{has:a,get:l}=Hr(i);let c=a.call(i,s);c||(s=Ce(s),c=a.call(i,s));const u=l.call(i,s);return i.set(s,o),c?dn(o,u)&&Wt(i,"set",s,o):Wt(i,"add",s,o),this},delete(s){const o=Ce(this),{has:i,get:a}=Hr(o);let l=i.call(o,s);l||(s=Ce(s),l=i.call(o,s)),a&&a.call(o,s);const c=o.delete(s);return l&&Wt(o,"delete",s,void 0),c},clear(){const s=Ce(this),o=s.size!==0,i=s.clear();return o&&Wt(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Td(s,e,t)}),n}function Rs(e,t){const n=Md(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Oe(n,s)&&s in r?n:r,s,o)}const Dd={get:Rs(!1,!1)},Id={get:Rs(!1,!0)},$d={get:Rs(!0,!1)},Bd={get:Rs(!0,!0)},zl=new WeakMap,Vl=new WeakMap,Hl=new WeakMap,Ul=new WeakMap;function qd(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ld(e){return e.__v_skip||!Object.isExtensible(e)?0:qd(dd(e))}function Xt(e){return pn(e)?e:Ms(e,!1,Od,Dd,zl)}function Wl(e){return Ms(e,!1,Pd,Id,Vl)}function Ts(e){return Ms(e,!0,kd,$d,Hl)}function Rn(e){return Ms(e,!0,Rd,Bd,Ul)}function Ms(e,t,n,r,s){if(!De(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Ld(e);if(o===0)return e;const i=s.get(e);if(i)return i;const a=new Proxy(e,o===2?r:n);return s.set(e,a),a}function fn(e){return pn(e)?fn(e.__v_raw):!!(e&&e.__v_isReactive)}function pn(e){return!!(e&&e.__v_isReadonly)}function yt(e){return!!(e&&e.__v_isShallow)}function ai(e){return e?!!e.__v_raw:!1}function Ce(e){const t=e&&e.__v_raw;return t?Ce(t):e}function Ds(e){return!Oe(e,"__v_skip")&&Object.isExtensible(e)&&Co(e,"__v_skip",!0),e}const He=e=>De(e)?Xt(e):e,ds=e=>De(e)?Ts(e):e;function Re(e){return e?e.__v_isRef===!0:!1}function L(e){return Kl(e,!1)}function Gt(e){return Kl(e,!0)}function Kl(e,t){return Re(e)?e:new Nd(e,t)}class Nd{constructor(t,n){this.dep=new ks,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Ce(t),this._value=n?t:He(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||yt(t)||pn(t);t=r?t:Ce(t),dn(t,n)&&(this._rawValue=t,this._value=r?t:He(t),this.dep.trigger())}}function f(e){return Re(e)?e.value:e}function Fe(e){return fe(e)?e():f(e)}const Fd={get:(e,t,n)=>t==="__v_raw"?e:f(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Re(s)&&!Re(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function Gl(e){return fn(e)?e:new Proxy(e,Fd)}class jd{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new ks,{get:r,set:s}=t(n.track.bind(n),n.trigger.bind(n));this._get=r,this._set=s}get value(){return this._value=this._get()}set value(t){this._set(t)}}function zd(e){return new jd(e)}function wt(e){const t=ce(e)?new Array(e.length):{};for(const n in e)t[n]=Yl(e,n);return t}class Vd{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return xd(Ce(this._object),this._key)}}class Hd{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Ud(e,t,n){return Re(e)?e:fe(e)?new Hd(e):De(e)&&arguments.length>1?Yl(e,t,n):L(e)}function Yl(e,t,n){const r=e[t];return Re(r)?r:new Vd(e,t,n)}class Wd{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ks(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Cr-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Me!==this)return Ml(this,!0),!0}get value(){const t=this.dep.track();return $l(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Kd(e,t,n=!1){let r,s;return fe(e)?r=e:(r=e.get,s=e.set),new Wd(r,s,n)}const Wr={},fs=new WeakMap;let xn;function Gd(e,t=!1,n=xn){if(n){let r=fs.get(n);r||fs.set(n,r=[]),r.push(e)}}function Yd(e,t,n=Pe){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:a,call:l}=n,c=x=>s?x:yt(x)||s===!1||s===0?Kt(x,1):Kt(x);let u,d,p,h,m=!1,y=!1;if(Re(e)?(d=()=>e.value,m=yt(e)):fn(e)?(d=()=>c(e),m=!0):ce(e)?(y=!0,m=e.some(x=>fn(x)||yt(x)),d=()=>e.map(x=>{if(Re(x))return x.value;if(fn(x))return c(x);if(fe(x))return l?l(x,2):x()})):fe(e)?t?d=l?()=>l(e,2):e:d=()=>{if(p){Yt();try{p()}finally{Jt()}}const x=xn;xn=u;try{return l?l(e,3,[h]):e(h)}finally{xn=x}}:d=$t,t&&s){const x=d,R=s===!0?1/0:s;d=()=>Kt(x(),R)}const _=$r(),C=()=>{u.stop(),_&&_.active&&ni(_.effects,u)};if(o&&t){const x=t;t=(...R)=>{x(...R),C()}}let O=y?new Array(e.length).fill(Wr):Wr;const b=x=>{if(!(!(u.flags&1)||!u.dirty&&!x))if(t){const R=u.run();if(s||m||(y?R.some((N,F)=>dn(N,O[F])):dn(R,O))){p&&p();const N=xn;xn=u;try{const F=[R,O===Wr?void 0:y&&O[0]===Wr?[]:O,h];O=R,l?l(t,3,F):t(...F)}finally{xn=N}}}else u.run()};return a&&a(b),u=new Rl(d),u.scheduler=i?()=>i(b,!1):b,h=x=>Gd(x,!1,u),p=u.onStop=()=>{const x=fs.get(u);if(x){if(l)l(x,4);else for(const R of x)R();fs.delete(u)}},t?r?b(!0):O=u.run():i?i(b.bind(null,!0),!0):u.run(),C.pause=u.pause.bind(u),C.resume=u.resume.bind(u),C.stop=C,C}function Kt(e,t=1/0,n){if(t<=0||!De(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Re(e))Kt(e.value,t,n);else if(ce(e))for(let r=0;r<e.length;r++)Kt(e[r],t,n);else if(xl(e)||Vn(e))e.forEach(r=>{Kt(r,t,n)});else if(El(e)){for(const r in e)Kt(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Kt(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Br(e,t,n,r){try{return r?e(...r):e()}catch(s){Is(s,t,n)}}function qt(e,t,n,r){if(fe(e)){const s=Br(e,t,n,r);return s&&Cl(s)&&s.catch(o=>{Is(o,t,n)}),s}if(ce(e)){const s=[];for(let o=0;o<e.length;o++)s.push(qt(e[o],t,n,r));return s}}function Is(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Pe;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,l,c)===!1)return}a=a.parent}if(o){Yt(),Br(o,null,10,[e,l,c]),Jt();return}}Jd(e,n,s,r,i)}function Jd(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const tt=[];let Mt=-1;const Hn=[];let ln=null,Ln=0;const Jl=Promise.resolve();let ps=null;function Ke(e){const t=ps||Jl;return e?t.then(this?e.bind(this):e):t}function Xd(e){let t=Mt+1,n=tt.length;for(;t<n;){const r=t+n>>>1,s=tt[r],o=Er(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function li(e){if(!(e.flags&1)){const t=Er(e),n=tt[tt.length-1];!n||!(e.flags&2)&&t>=Er(n)?tt.push(e):tt.splice(Xd(t),0,e),e.flags|=1,Xl()}}function Xl(){ps||(ps=Jl.then(Zl))}function Qd(e){ce(e)?Hn.push(...e):ln&&e.id===-1?ln.splice(Ln+1,0,e):e.flags&1||(Hn.push(e),e.flags|=1),Xl()}function Hi(e,t,n=Mt+1){for(;n<tt.length;n++){const r=tt[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;tt.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Ql(e){if(Hn.length){const t=[...new Set(Hn)].sort((n,r)=>Er(n)-Er(r));if(Hn.length=0,ln){ln.push(...t);return}for(ln=t,Ln=0;Ln<ln.length;Ln++){const n=ln[Ln];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ln=null,Ln=0}}const Er=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Zl(e){try{for(Mt=0;Mt<tt.length;Mt++){const t=tt[Mt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Br(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Mt<tt.length;Mt++){const t=tt[Mt];t&&(t.flags&=-2)}Mt=-1,tt.length=0,Ql(),ps=null,(tt.length||Hn.length)&&Zl()}}let We=null,eu=null;function hs(e){const t=We;return We=e,eu=e&&e.type.__scopeId||null,t}function w(e,t=We,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&na(-1);const o=hs(t);let i;try{i=e(...s)}finally{hs(o),r._d&&na(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function tu(e,t){if(We===null)return e;const n=Ns(We),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,a,l=Pe]=t[s];o&&(fe(o)&&(o={mounted:o,updated:o}),o.deep&&Kt(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function yn(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let l=a.dir[r];l&&(Yt(),qt(l,n,8,[e.el,a,e,t]),Jt())}}const nu=Symbol("_vte"),Zd=e=>e.__isTeleport,gr=e=>e&&(e.disabled||e.disabled===""),Ui=e=>e&&(e.defer||e.defer===""),Wi=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ki=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,ko=(e,t)=>{const n=e&&e.to;return Le(n)?t?t(n):null:n},ru={name:"Teleport",__isTeleport:!0,process(e,t,n,r,s,o,i,a,l,c){const{mc:u,pc:d,pbc:p,o:{insert:h,querySelector:m,createText:y,createComment:_}}=c,C=gr(t.props);let{shapeFlag:O,children:b,dynamicChildren:x}=t;if(e==null){const R=t.el=y(""),N=t.anchor=y("");h(R,n,r),h(N,n,r);const F=(A,Y)=>{O&16&&(s&&s.isCE&&(s.ce._teleportTarget=A),u(b,A,Y,s,o,i,a,l))},k=()=>{const A=t.target=ko(t.props,m),Y=su(A,t,y,h);A&&(i!=="svg"&&Wi(A)?i="svg":i!=="mathml"&&Ki(A)&&(i="mathml"),C||(F(A,Y),as(t,!1)))};C&&(F(n,N),as(t,!0)),Ui(t.props)?(t.el.__isMounted=!1,et(()=>{k(),delete t.el.__isMounted},o)):k()}else{if(Ui(t.props)&&e.el.__isMounted===!1){et(()=>{ru.process(e,t,n,r,s,o,i,a,l,c)},o);return}t.el=e.el,t.targetStart=e.targetStart;const R=t.anchor=e.anchor,N=t.target=e.target,F=t.targetAnchor=e.targetAnchor,k=gr(e.props),A=k?n:N,Y=k?R:F;if(i==="svg"||Wi(N)?i="svg":(i==="mathml"||Ki(N))&&(i="mathml"),x?(p(e.dynamicChildren,x,A,s,o,i,a),hi(e,t,!0)):l||d(e,t,A,Y,s,o,i,a,!1),C)k?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Kr(t,n,R,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const se=t.target=ko(t.props,m);se&&Kr(t,se,null,c,0)}else k&&Kr(t,N,F,c,1);as(t,C)}},remove(e,t,n,{um:r,o:{remove:s}},o){const{shapeFlag:i,children:a,anchor:l,targetStart:c,targetAnchor:u,target:d,props:p}=e;if(d&&(s(c),s(u)),o&&s(l),i&16){const h=o||!gr(p);for(let m=0;m<a.length;m++){const y=a[m];r(y,t,n,h,!!y.dynamicChildren)}}},move:Kr,hydrate:ef};function Kr(e,t,n,{o:{insert:r},m:s},o=2){o===0&&r(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:l,children:c,props:u}=e,d=o===2;if(d&&r(i,t,n),(!d||gr(u))&&l&16)for(let p=0;p<c.length;p++)s(c[p],t,n,2);d&&r(a,t,n)}function ef(e,t,n,r,s,o,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:c,createText:u}},d){const p=t.target=ko(t.props,l);if(p){const h=gr(t.props),m=p._lpa||p.firstChild;if(t.shapeFlag&16)if(h)t.anchor=d(i(e),t,a(e),n,r,s,o),t.targetStart=m,t.targetAnchor=m&&i(m);else{t.anchor=i(e);let y=m;for(;y;){if(y&&y.nodeType===8){if(y.data==="teleport start anchor")t.targetStart=y;else if(y.data==="teleport anchor"){t.targetAnchor=y,p._lpa=t.targetAnchor&&i(t.targetAnchor);break}}y=i(y)}t.targetAnchor||su(p,t,u,c),d(m&&i(m),t,p,n,r,s,o)}as(t,h)}return t.anchor&&i(t.anchor)}const tf=ru;function as(e,t){const n=e.ctx;if(n&&n.ut){let r,s;for(t?(r=e.el,s=e.anchor):(r=e.targetStart,s=e.targetAnchor);r&&r!==s;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function su(e,t,n,r){const s=t.targetStart=n(""),o=t.targetAnchor=n("");return s[nu]=o,e&&(r(s,e),r(o,e)),o}function ui(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ui(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function I(e,t){return fe(e)?Xe({name:e.name},t,{setup:e}):e}function nf(){const e=ut();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function ou(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function vr(e,t,n,r,s=!1){if(ce(e)){e.forEach((m,y)=>vr(m,t&&(ce(t)?t[y]:t),n,r,s));return}if(Un(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&vr(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Ns(r.component):r.el,i=s?null:o,{i:a,r:l}=e,c=t&&t.r,u=a.refs===Pe?a.refs={}:a.refs,d=a.setupState,p=Ce(d),h=d===Pe?()=>!1:m=>Oe(p,m);if(c!=null&&c!==l&&(Le(c)?(u[c]=null,h(c)&&(d[c]=null)):Re(c)&&(c.value=null)),fe(l))Br(l,a,12,[i,u]);else{const m=Le(l),y=Re(l);if(m||y){const _=()=>{if(e.f){const C=m?h(l)?d[l]:u[l]:l.value;s?ce(C)&&ni(C,o):ce(C)?C.includes(o)||C.push(o):m?(u[l]=[o],h(l)&&(d[l]=u[l])):(l.value=[o],e.k&&(u[e.k]=l.value))}else m?(u[l]=i,h(l)&&(d[l]=i)):y&&(l.value=i,e.k&&(u[e.k]=i))};i?(_.id=-1,et(_,n)):_()}}}Es().requestIdleCallback;Es().cancelIdleCallback;const Un=e=>!!e.type.__asyncLoader,iu=e=>e.type.__isKeepAlive;function rf(e,t){au(e,"a",t)}function sf(e,t){au(e,"da",t)}function au(e,t,n=Je){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if($s(t,r,n),n){let s=n.parent;for(;s&&s.parent;)iu(s.parent.vnode)&&of(r,t,n,s),s=s.parent}}function of(e,t,n,r){const s=$s(t,e,r,!0);On(()=>{ni(r[t],s)},n)}function $s(e,t,n=Je,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Yt();const a=Lr(n),l=qt(t,n,e,i);return a(),Jt(),l});return r?s.unshift(o):s.push(o),o}}const tn=e=>(t,n=Je)=>{(!Pr||e==="sp")&&$s(e,(...r)=>t(...r),n)},af=tn("bm"),ht=tn("m"),lf=tn("bu"),uf=tn("u"),lu=tn("bum"),On=tn("um"),cf=tn("sp"),df=tn("rtg"),ff=tn("rtc");function pf(e,t=Je){$s("ec",e,t)}const uu="components";function ci(e,t){return du(uu,e,!0,t)||e}const cu=Symbol.for("v-ndc");function Ar(e){return Le(e)?du(uu,e,!1)||e:e||cu}function du(e,t,n=!0,r=!1){const s=We||Je;if(s){const o=s.type;{const a=Zf(o,!1);if(a&&(a===t||a===lt(t)||a===Ss(lt(t))))return o}const i=Gi(s[e]||o[e],t)||Gi(s.appContext[e],t);return!i&&r?o:i}}function Gi(e,t){return e&&(e[t]||e[lt(t)]||e[Ss(lt(t))])}function Po(e,t,n,r){let s;const o=n,i=ce(e);if(i||Le(e)){const a=i&&fn(e);let l=!1,c=!1;a&&(l=!yt(e),c=pn(e),e=Ps(e)),s=new Array(e.length);for(let u=0,d=e.length;u<d;u++)s[u]=t(l?c?ds(He(e[u])):He(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,o)}else if(De(e))if(e[Symbol.iterator])s=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);s=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];s[l]=t(e[u],u,l,o)}}else s=[];return s}function q(e,t,n={},r,s){if(We.ce||We.parent&&Un(We.parent)&&We.parent.ce)return T(),z(Ue,null,[E("slot",n,r&&r())],64);let o=e[t];o&&o._c&&(o._d=!1),T();const i=o&&fu(o(n)),a=n.key||i&&i.key,l=z(Ue,{key:(a&&!en(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||(r?r():[]),i&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function fu(e){return e.some(t=>kr(t)?!(t.type===xt||t.type===Ue&&!fu(t.children)):!0)?e:null}function hf(e,t){const n={};for(const r in e)n[pr(r)]=e[r];return n}const Ro=e=>e?Mu(e)?Ns(e):Ro(e.parent):null,yr=Xe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ro(e.parent),$root:e=>Ro(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>mu(e),$forceUpdate:e=>e.f||(e.f=()=>{li(e.update)}),$nextTick:e=>e.n||(e.n=Ke.bind(e.proxy)),$watch:e=>qf.bind(e)}),eo=(e,t)=>e!==Pe&&!e.__isScriptSetup&&Oe(e,t),mf={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const h=i[t];if(h!==void 0)switch(h){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(eo(r,t))return i[t]=1,r[t];if(s!==Pe&&Oe(s,t))return i[t]=2,s[t];if((c=e.propsOptions[0])&&Oe(c,t))return i[t]=3,o[t];if(n!==Pe&&Oe(n,t))return i[t]=4,n[t];Mo&&(i[t]=0)}}const u=yr[t];let d,p;if(u)return t==="$attrs"&&Ye(e.attrs,"get",""),u(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(n!==Pe&&Oe(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,Oe(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return eo(s,t)?(s[t]=n,!0):r!==Pe&&Oe(r,t)?(r[t]=n,!0):Oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let a;return!!n[i]||e!==Pe&&Oe(e,i)||eo(t,i)||(a=o[0])&&Oe(a,i)||Oe(r,i)||Oe(yr,i)||Oe(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function gf(){return vf().slots}function vf(e){const t=ut();return t.setupContext||(t.setupContext=Iu(t))}function To(e){return ce(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function pu(e,t){const n=To(e);for(const r in t){if(r.startsWith("__skip"))continue;let s=n[r];s?ce(s)||fe(s)?s=n[r]={type:s,default:t[r]}:s.default=t[r]:s===null&&(s=n[r]={default:t[r]}),s&&t[`__skip_${r}`]&&(s.skipFactory=!0)}return n}let Mo=!0;function yf(e){const t=mu(e),n=e.proxy,r=e.ctx;Mo=!1,t.beforeCreate&&Yi(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:a,provide:l,inject:c,created:u,beforeMount:d,mounted:p,beforeUpdate:h,updated:m,activated:y,deactivated:_,beforeDestroy:C,beforeUnmount:O,destroyed:b,unmounted:x,render:R,renderTracked:N,renderTriggered:F,errorCaptured:k,serverPrefetch:A,expose:Y,inheritAttrs:se,components:le,directives:ge,filters:ve}=t;if(c&&bf(c,r,null),i)for(const U in i){const B=i[U];fe(B)&&(r[U]=B.bind(n))}if(s){const U=s.call(n,n);De(U)&&(e.data=Xt(U))}if(Mo=!0,o)for(const U in o){const B=o[U],Ne=fe(B)?B.bind(n,n):fe(B.get)?B.get.bind(n,n):$t,X=!fe(B)&&fe(B.set)?B.set.bind(n):$t,de=H({get:Ne,set:X});Object.defineProperty(r,U,{enumerable:!0,configurable:!0,get:()=>de.value,set:ne=>de.value=ne})}if(a)for(const U in a)hu(a[U],r,n,U);if(l){const U=fe(l)?l.call(n):l;Reflect.ownKeys(U).forEach(B=>{Wn(B,U[B])})}u&&Yi(u,e,"c");function V(U,B){ce(B)?B.forEach(Ne=>U(Ne.bind(n))):B&&U(B.bind(n))}if(V(af,d),V(ht,p),V(lf,h),V(uf,m),V(rf,y),V(sf,_),V(pf,k),V(ff,N),V(df,F),V(lu,O),V(On,x),V(cf,A),ce(Y))if(Y.length){const U=e.exposed||(e.exposed={});Y.forEach(B=>{Object.defineProperty(U,B,{get:()=>n[B],set:Ne=>n[B]=Ne,enumerable:!0})})}else e.exposed||(e.exposed={});R&&e.render===$t&&(e.render=R),se!=null&&(e.inheritAttrs=se),le&&(e.components=le),ge&&(e.directives=ge),A&&ou(e)}function bf(e,t,n=$t){ce(e)&&(e=Do(e));for(const r in e){const s=e[r];let o;De(s)?"default"in s?o=nt(s.from||r,s.default,!0):o=nt(s.from||r):o=nt(s),Re(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function Yi(e,t,n){qt(ce(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function hu(e,t,n,r){let s=r.includes(".")?Ou(n,r):()=>n[r];if(Le(e)){const o=t[e];fe(o)&&qe(s,o)}else if(fe(e))qe(s,e.bind(n));else if(De(e))if(ce(e))e.forEach(o=>hu(o,t,n,r));else{const o=fe(e.handler)?e.handler.bind(n):t[e.handler];fe(o)&&qe(s,o,e)}}function mu(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!s.length&&!n&&!r?l=t:(l={},s.length&&s.forEach(c=>ms(l,c,i,!0)),ms(l,t,i)),De(t)&&o.set(t,l),l}function ms(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&ms(e,o,n,!0),s&&s.forEach(i=>ms(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=_f[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const _f={data:Ji,props:Xi,emits:Xi,methods:cr,computed:cr,beforeCreate:Ze,created:Ze,beforeMount:Ze,mounted:Ze,beforeUpdate:Ze,updated:Ze,beforeDestroy:Ze,beforeUnmount:Ze,destroyed:Ze,unmounted:Ze,activated:Ze,deactivated:Ze,errorCaptured:Ze,serverPrefetch:Ze,components:cr,directives:cr,watch:xf,provide:Ji,inject:wf};function Ji(e,t){return t?e?function(){return Xe(fe(e)?e.call(this,this):e,fe(t)?t.call(this,this):t)}:t:e}function wf(e,t){return cr(Do(e),Do(t))}function Do(e){if(ce(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ze(e,t){return e?[...new Set([].concat(e,t))]:t}function cr(e,t){return e?Xe(Object.create(null),e,t):t}function Xi(e,t){return e?ce(e)&&ce(t)?[...new Set([...e,...t])]:Xe(Object.create(null),To(e),To(t??{})):t}function xf(e,t){if(!e)return t;if(!t)return e;const n=Xe(Object.create(null),e);for(const r in t)n[r]=Ze(e[r],t[r]);return n}function gu(){return{app:null,config:{isNativeTag:ud,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Cf=0;function Sf(e,t){return function(r,s=null){fe(r)||(r=Xe({},r)),s!=null&&!De(s)&&(s=null);const o=gu(),i=new WeakSet,a=[];let l=!1;const c=o.app={_uid:Cf++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:tp,get config(){return o.config},set config(u){},use(u,...d){return i.has(u)||(u&&fe(u.install)?(i.add(u),u.install(c,...d)):fe(u)&&(i.add(u),u(c,...d))),c},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),c},component(u,d){return d?(o.components[u]=d,c):o.components[u]},directive(u,d){return d?(o.directives[u]=d,c):o.directives[u]},mount(u,d,p){if(!l){const h=c._ceVNode||E(r,s);return h.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(h,u,p),l=!0,c._container=u,u.__vue_app__=c,Ns(h.component)}},onUnmount(u){a.push(u)},unmount(){l&&(qt(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,d){return o.provides[u]=d,c},runWithContext(u){const d=Sn;Sn=c;try{return u()}finally{Sn=d}}};return c}}let Sn=null;function Wn(e,t){if(Je){let n=Je.provides;const r=Je.parent&&Je.parent.provides;r===n&&(n=Je.provides=Object.create(r)),n[e]=t}}function nt(e,t,n=!1){const r=ut();if(r||Sn){let s=Sn?Sn._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&fe(t)?t.call(r&&r.proxy):t}}function di(){return!!(ut()||Sn)}const vu={},yu=()=>Object.create(vu),bu=e=>Object.getPrototypeOf(e)===vu;function Ef(e,t,n,r=!1){const s={},o=yu();e.propsDefaults=Object.create(null),_u(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:Wl(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function Af(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=Ce(s),[l]=e.propsOptions;let c=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let p=u[d];if(qs(e.emitsOptions,p))continue;const h=t[p];if(l)if(Oe(o,p))h!==o[p]&&(o[p]=h,c=!0);else{const m=lt(p);s[m]=Io(l,a,m,h,e,!1)}else h!==o[p]&&(o[p]=h,c=!0)}}}else{_u(e,t,s,o)&&(c=!0);let u;for(const d in a)(!t||!Oe(t,d)&&((u=gn(d))===d||!Oe(t,u)))&&(l?n&&(n[d]!==void 0||n[u]!==void 0)&&(s[d]=Io(l,a,d,void 0,e,!0)):delete s[d]);if(o!==a)for(const d in o)(!t||!Oe(t,d))&&(delete o[d],c=!0)}c&&Wt(e.attrs,"set","")}function _u(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(fr(l))continue;const c=t[l];let u;s&&Oe(s,u=lt(l))?!o||!o.includes(u)?n[u]=c:(a||(a={}))[u]=c:qs(e.emitsOptions,l)||(!(l in r)||c!==r[l])&&(r[l]=c,i=!0)}if(o){const l=Ce(n),c=a||Pe;for(let u=0;u<o.length;u++){const d=o[u];n[d]=Io(s,l,d,c[d],e,!Oe(c,d))}}return i}function Io(e,t,n,r,s,o){const i=e[n];if(i!=null){const a=Oe(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&fe(l)){const{propsDefaults:c}=s;if(n in c)r=c[n];else{const u=Lr(s);r=c[n]=l.call(null,t),u()}}else r=l;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!a?r=!1:i[1]&&(r===""||r===gn(n))&&(r=!0))}return r}const Of=new WeakMap;function wu(e,t,n=!1){const r=n?Of:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},a=[];let l=!1;if(!fe(e)){const u=d=>{l=!0;const[p,h]=wu(d,t,!0);Xe(i,p),h&&a.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!l)return De(e)&&r.set(e,zn),zn;if(ce(o))for(let u=0;u<o.length;u++){const d=lt(o[u]);Qi(d)&&(i[d]=Pe)}else if(o)for(const u in o){const d=lt(u);if(Qi(d)){const p=o[u],h=i[d]=ce(p)||fe(p)?{type:p}:Xe({},p),m=h.type;let y=!1,_=!0;if(ce(m))for(let C=0;C<m.length;++C){const O=m[C],b=fe(O)&&O.name;if(b==="Boolean"){y=!0;break}else b==="String"&&(_=!1)}else y=fe(m)&&m.name==="Boolean";h[0]=y,h[1]=_,(y||Oe(h,"default"))&&a.push(d)}}const c=[i,a];return De(e)&&r.set(e,c),c}function Qi(e){return e[0]!=="$"&&!fr(e)}const fi=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",pi=e=>ce(e)?e.map(Dt):[Dt(e)],kf=(e,t,n)=>{if(t._n)return t;const r=w((...s)=>pi(t(...s)),n);return r._c=!1,r},xu=(e,t,n)=>{const r=e._ctx;for(const s in e){if(fi(s))continue;const o=e[s];if(fe(o))t[s]=kf(s,o,r);else if(o!=null){const i=pi(o);t[s]=()=>i}}},Cu=(e,t)=>{const n=pi(t);e.slots.default=()=>n},Su=(e,t,n)=>{for(const r in t)(n||!fi(r))&&(e[r]=t[r])},Pf=(e,t,n)=>{const r=e.slots=yu();if(e.vnode.shapeFlag&32){const s=t.__;s&&Co(r,"__",s,!0);const o=t._;o?(Su(r,t,n),n&&Co(r,"_",o,!0)):xu(t,r)}else t&&Cu(e,t)},Rf=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=Pe;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Su(s,t,n):(o=!t.$stable,xu(t,s)),i=t}else t&&(Cu(e,t),i={default:1});if(o)for(const a in s)!fi(a)&&i[a]==null&&delete s[a]},et=Hf;function Tf(e){return Mf(e)}function Mf(e,t){const n=Es();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:a,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:p,setScopeId:h=$t,insertStaticContent:m}=e,y=(g,v,S,M=null,$=null,D=null,J=void 0,K=null,W=!!v.dynamicChildren)=>{if(g===v)return;g&&!or(g,v)&&(M=P(g),ne(g,$,D,!0),g=null),v.patchFlag===-2&&(W=!1,v.dynamicChildren=null);const{type:j,ref:ae,shapeFlag:Q}=v;switch(j){case Ls:_(g,v,S,M);break;case xt:C(g,v,S,M);break;case no:g==null&&O(v,S,M,J);break;case Ue:le(g,v,S,M,$,D,J,K,W);break;default:Q&1?R(g,v,S,M,$,D,J,K,W):Q&6?ge(g,v,S,M,$,D,J,K,W):(Q&64||Q&128)&&j.process(g,v,S,M,$,D,J,K,W,re)}ae!=null&&$?vr(ae,g&&g.ref,D,v||g,!v):ae==null&&g&&g.ref!=null&&vr(g.ref,null,D,g,!0)},_=(g,v,S,M)=>{if(g==null)r(v.el=a(v.children),S,M);else{const $=v.el=g.el;v.children!==g.children&&c($,v.children)}},C=(g,v,S,M)=>{g==null?r(v.el=l(v.children||""),S,M):v.el=g.el},O=(g,v,S,M)=>{[g.el,g.anchor]=m(g.children,v,S,M,g.el,g.anchor)},b=({el:g,anchor:v},S,M)=>{let $;for(;g&&g!==v;)$=p(g),r(g,S,M),g=$;r(v,S,M)},x=({el:g,anchor:v})=>{let S;for(;g&&g!==v;)S=p(g),s(g),g=S;s(v)},R=(g,v,S,M,$,D,J,K,W)=>{v.type==="svg"?J="svg":v.type==="math"&&(J="mathml"),g==null?N(v,S,M,$,D,J,K,W):A(g,v,$,D,J,K,W)},N=(g,v,S,M,$,D,J,K)=>{let W,j;const{props:ae,shapeFlag:Q,transition:ie,dirs:pe}=g;if(W=g.el=i(g.type,D,ae&&ae.is,ae),Q&8?u(W,g.children):Q&16&&k(g.children,W,null,M,$,to(g,D),J,K),pe&&yn(g,null,M,"created"),F(W,g,g.scopeId,J,M),ae){for(const Te in ae)Te!=="value"&&!fr(Te)&&o(W,Te,null,ae[Te],D,M);"value"in ae&&o(W,"value",null,ae.value,D),(j=ae.onVnodeBeforeMount)&&Tt(j,M,g)}pe&&yn(g,null,M,"beforeMount");const we=Df($,ie);we&&ie.beforeEnter(W),r(W,v,S),((j=ae&&ae.onVnodeMounted)||we||pe)&&et(()=>{j&&Tt(j,M,g),we&&ie.enter(W),pe&&yn(g,null,M,"mounted")},$)},F=(g,v,S,M,$)=>{if(S&&h(g,S),M)for(let D=0;D<M.length;D++)h(g,M[D]);if($){let D=$.subTree;if(v===D||Pu(D.type)&&(D.ssContent===v||D.ssFallback===v)){const J=$.vnode;F(g,J,J.scopeId,J.slotScopeIds,$.parent)}}},k=(g,v,S,M,$,D,J,K,W=0)=>{for(let j=W;j<g.length;j++){const ae=g[j]=K?un(g[j]):Dt(g[j]);y(null,ae,v,S,M,$,D,J,K)}},A=(g,v,S,M,$,D,J)=>{const K=v.el=g.el;let{patchFlag:W,dynamicChildren:j,dirs:ae}=v;W|=g.patchFlag&16;const Q=g.props||Pe,ie=v.props||Pe;let pe;if(S&&bn(S,!1),(pe=ie.onVnodeBeforeUpdate)&&Tt(pe,S,v,g),ae&&yn(v,g,S,"beforeUpdate"),S&&bn(S,!0),(Q.innerHTML&&ie.innerHTML==null||Q.textContent&&ie.textContent==null)&&u(K,""),j?Y(g.dynamicChildren,j,K,S,M,to(v,$),D):J||B(g,v,K,null,S,M,to(v,$),D,!1),W>0){if(W&16)se(K,Q,ie,S,$);else if(W&2&&Q.class!==ie.class&&o(K,"class",null,ie.class,$),W&4&&o(K,"style",Q.style,ie.style,$),W&8){const we=v.dynamicProps;for(let Te=0;Te<we.length;Te++){const ke=we[Te],ot=Q[ke],it=ie[ke];(it!==ot||ke==="value")&&o(K,ke,ot,it,$,S)}}W&1&&g.children!==v.children&&u(K,v.children)}else!J&&j==null&&se(K,Q,ie,S,$);((pe=ie.onVnodeUpdated)||ae)&&et(()=>{pe&&Tt(pe,S,v,g),ae&&yn(v,g,S,"updated")},M)},Y=(g,v,S,M,$,D,J)=>{for(let K=0;K<v.length;K++){const W=g[K],j=v[K],ae=W.el&&(W.type===Ue||!or(W,j)||W.shapeFlag&198)?d(W.el):S;y(W,j,ae,null,M,$,D,J,!0)}},se=(g,v,S,M,$)=>{if(v!==S){if(v!==Pe)for(const D in v)!fr(D)&&!(D in S)&&o(g,D,v[D],null,$,M);for(const D in S){if(fr(D))continue;const J=S[D],K=v[D];J!==K&&D!=="value"&&o(g,D,K,J,$,M)}"value"in S&&o(g,"value",v.value,S.value,$)}},le=(g,v,S,M,$,D,J,K,W)=>{const j=v.el=g?g.el:a(""),ae=v.anchor=g?g.anchor:a("");let{patchFlag:Q,dynamicChildren:ie,slotScopeIds:pe}=v;pe&&(K=K?K.concat(pe):pe),g==null?(r(j,S,M),r(ae,S,M),k(v.children||[],S,ae,$,D,J,K,W)):Q>0&&Q&64&&ie&&g.dynamicChildren?(Y(g.dynamicChildren,ie,S,$,D,J,K),(v.key!=null||$&&v===$.subTree)&&hi(g,v,!0)):B(g,v,S,ae,$,D,J,K,W)},ge=(g,v,S,M,$,D,J,K,W)=>{v.slotScopeIds=K,g==null?v.shapeFlag&512?$.ctx.activate(v,S,M,J,W):ve(v,S,M,$,D,J,W):xe(g,v,W)},ve=(g,v,S,M,$,D,J)=>{const K=g.component=Yf(g,M,$);if(iu(g)&&(K.ctx.renderer=re),Jf(K,!1,J),K.asyncDep){if($&&$.registerDep(K,V,J),!g.el){const W=K.subTree=E(xt);C(null,W,v,S),g.placeholder=W.el}}else V(K,g,v,S,$,D,J)},xe=(g,v,S)=>{const M=v.component=g.component;if(zf(g,v,S))if(M.asyncDep&&!M.asyncResolved){U(M,v,S);return}else M.next=v,M.update();else v.el=g.el,M.vnode=v},V=(g,v,S,M,$,D,J)=>{const K=()=>{if(g.isMounted){let{next:Q,bu:ie,u:pe,parent:we,vnode:Te}=g;{const Pt=Eu(g);if(Pt){Q&&(Q.el=Te.el,U(g,Q,J)),Pt.asyncDep.then(()=>{g.isUnmounted||K()});return}}let ke=Q,ot;bn(g,!1),Q?(Q.el=Te.el,U(g,Q,J)):Q=Te,ie&&is(ie),(ot=Q.props&&Q.props.onVnodeBeforeUpdate)&&Tt(ot,we,Q,Te),bn(g,!0);const it=ea(g),kt=g.subTree;g.subTree=it,y(kt,it,d(kt.el),P(kt),g,$,D),Q.el=it.el,ke===null&&Vf(g,it.el),pe&&et(pe,$),(ot=Q.props&&Q.props.onVnodeUpdated)&&et(()=>Tt(ot,we,Q,Te),$)}else{let Q;const{el:ie,props:pe}=v,{bm:we,m:Te,parent:ke,root:ot,type:it}=g,kt=Un(v);bn(g,!1),we&&is(we),!kt&&(Q=pe&&pe.onVnodeBeforeMount)&&Tt(Q,ke,v),bn(g,!0);{ot.ce&&ot.ce._def.shadowRoot!==!1&&ot.ce._injectChildStyle(it);const Pt=g.subTree=ea(g);y(null,Pt,S,M,g,$,D),v.el=Pt.el}if(Te&&et(Te,$),!kt&&(Q=pe&&pe.onVnodeMounted)){const Pt=v;et(()=>Tt(Q,ke,Pt),$)}(v.shapeFlag&256||ke&&Un(ke.vnode)&&ke.vnode.shapeFlag&256)&&g.a&&et(g.a,$),g.isMounted=!0,v=S=M=null}};g.scope.on();const W=g.effect=new Rl(K);g.scope.off();const j=g.update=W.run.bind(W),ae=g.job=W.runIfDirty.bind(W);ae.i=g,ae.id=g.uid,W.scheduler=()=>li(ae),bn(g,!0),j()},U=(g,v,S)=>{v.component=g;const M=g.vnode.props;g.vnode=v,g.next=null,Af(g,v.props,M,S),Rf(g,v.children,S),Yt(),Hi(g),Jt()},B=(g,v,S,M,$,D,J,K,W=!1)=>{const j=g&&g.children,ae=g?g.shapeFlag:0,Q=v.children,{patchFlag:ie,shapeFlag:pe}=v;if(ie>0){if(ie&128){X(j,Q,S,M,$,D,J,K,W);return}else if(ie&256){Ne(j,Q,S,M,$,D,J,K,W);return}}pe&8?(ae&16&&Qe(j,$,D),Q!==j&&u(S,Q)):ae&16?pe&16?X(j,Q,S,M,$,D,J,K,W):Qe(j,$,D,!0):(ae&8&&u(S,""),pe&16&&k(Q,S,M,$,D,J,K,W))},Ne=(g,v,S,M,$,D,J,K,W)=>{g=g||zn,v=v||zn;const j=g.length,ae=v.length,Q=Math.min(j,ae);let ie;for(ie=0;ie<Q;ie++){const pe=v[ie]=W?un(v[ie]):Dt(v[ie]);y(g[ie],pe,S,null,$,D,J,K,W)}j>ae?Qe(g,$,D,!0,!1,Q):k(v,S,M,$,D,J,K,W,Q)},X=(g,v,S,M,$,D,J,K,W)=>{let j=0;const ae=v.length;let Q=g.length-1,ie=ae-1;for(;j<=Q&&j<=ie;){const pe=g[j],we=v[j]=W?un(v[j]):Dt(v[j]);if(or(pe,we))y(pe,we,S,null,$,D,J,K,W);else break;j++}for(;j<=Q&&j<=ie;){const pe=g[Q],we=v[ie]=W?un(v[ie]):Dt(v[ie]);if(or(pe,we))y(pe,we,S,null,$,D,J,K,W);else break;Q--,ie--}if(j>Q){if(j<=ie){const pe=ie+1,we=pe<ae?v[pe].el:M;for(;j<=ie;)y(null,v[j]=W?un(v[j]):Dt(v[j]),S,we,$,D,J,K,W),j++}}else if(j>ie)for(;j<=Q;)ne(g[j],$,D,!0),j++;else{const pe=j,we=j,Te=new Map;for(j=we;j<=ie;j++){const ct=v[j]=W?un(v[j]):Dt(v[j]);ct.key!=null&&Te.set(ct.key,j)}let ke,ot=0;const it=ie-we+1;let kt=!1,Pt=0;const rr=new Array(it);for(j=0;j<it;j++)rr[j]=0;for(j=pe;j<=Q;j++){const ct=g[j];if(ot>=it){ne(ct,$,D,!0);continue}let Rt;if(ct.key!=null)Rt=Te.get(ct.key);else for(ke=we;ke<=ie;ke++)if(rr[ke-we]===0&&or(ct,v[ke])){Rt=ke;break}Rt===void 0?ne(ct,$,D,!0):(rr[Rt-we]=j+1,Rt>=Pt?Pt=Rt:kt=!0,y(ct,v[Rt],S,null,$,D,J,K,W),ot++)}const Li=kt?If(rr):zn;for(ke=Li.length-1,j=it-1;j>=0;j--){const ct=we+j,Rt=v[ct],Ni=v[ct+1],Fi=ct+1<ae?Ni.el||Ni.placeholder:M;rr[j]===0?y(null,Rt,S,Fi,$,D,J,K,W):kt&&(ke<0||j!==Li[ke]?de(Rt,S,Fi,2):ke--)}}},de=(g,v,S,M,$=null)=>{const{el:D,type:J,transition:K,children:W,shapeFlag:j}=g;if(j&6){de(g.component.subTree,v,S,M);return}if(j&128){g.suspense.move(v,S,M);return}if(j&64){J.move(g,v,S,re);return}if(J===Ue){r(D,v,S);for(let Q=0;Q<W.length;Q++)de(W[Q],v,S,M);r(g.anchor,v,S);return}if(J===no){b(g,v,S);return}if(M!==2&&j&1&&K)if(M===0)K.beforeEnter(D),r(D,v,S),et(()=>K.enter(D),$);else{const{leave:Q,delayLeave:ie,afterLeave:pe}=K,we=()=>{g.ctx.isUnmounted?s(D):r(D,v,S)},Te=()=>{Q(D,()=>{we(),pe&&pe()})};ie?ie(D,we,Te):Te()}else r(D,v,S)},ne=(g,v,S,M=!1,$=!1)=>{const{type:D,props:J,ref:K,children:W,dynamicChildren:j,shapeFlag:ae,patchFlag:Q,dirs:ie,cacheIndex:pe}=g;if(Q===-2&&($=!1),K!=null&&(Yt(),vr(K,null,S,g,!0),Jt()),pe!=null&&(v.renderCache[pe]=void 0),ae&256){v.ctx.deactivate(g);return}const we=ae&1&&ie,Te=!Un(g);let ke;if(Te&&(ke=J&&J.onVnodeBeforeUnmount)&&Tt(ke,v,g),ae&6)Ft(g.component,S,M);else{if(ae&128){g.suspense.unmount(S,M);return}we&&yn(g,null,v,"beforeUnmount"),ae&64?g.type.remove(g,v,S,re,M):j&&!j.hasOnce&&(D!==Ue||Q>0&&Q&64)?Qe(j,v,S,!1,!0):(D===Ue&&Q&384||!$&&ae&16)&&Qe(W,v,S),M&&ye(g)}(Te&&(ke=J&&J.onVnodeUnmounted)||we)&&et(()=>{ke&&Tt(ke,v,g),we&&yn(g,null,v,"unmounted")},S)},ye=g=>{const{type:v,el:S,anchor:M,transition:$}=g;if(v===Ue){je(S,M);return}if(v===no){x(g);return}const D=()=>{s(S),$&&!$.persisted&&$.afterLeave&&$.afterLeave()};if(g.shapeFlag&1&&$&&!$.persisted){const{leave:J,delayLeave:K}=$,W=()=>J(S,D);K?K(g.el,D,W):W()}else D()},je=(g,v)=>{let S;for(;g!==v;)S=p(g),s(g),g=S;s(v)},Ft=(g,v,S)=>{const{bum:M,scope:$,job:D,subTree:J,um:K,m:W,a:j,parent:ae,slots:{__:Q}}=g;Zi(W),Zi(j),M&&is(M),ae&&ce(Q)&&Q.forEach(ie=>{ae.renderCache[ie]=void 0}),$.stop(),D&&(D.flags|=8,ne(J,g,v,S)),K&&et(K,v),et(()=>{g.isUnmounted=!0},v),v&&v.pendingBranch&&!v.isUnmounted&&g.asyncDep&&!g.asyncResolved&&g.suspenseId===v.pendingId&&(v.deps--,v.deps===0&&v.resolve())},Qe=(g,v,S,M=!1,$=!1,D=0)=>{for(let J=D;J<g.length;J++)ne(g[J],v,S,M,$)},P=g=>{if(g.shapeFlag&6)return P(g.component.subTree);if(g.shapeFlag&128)return g.suspense.next();const v=p(g.anchor||g.el),S=v&&v[nu];return S?p(S):v};let Z=!1;const G=(g,v,S)=>{g==null?v._vnode&&ne(v._vnode,null,null,!0):y(v._vnode||null,g,v,null,null,null,S),v._vnode=g,Z||(Z=!0,Hi(),Ql(),Z=!1)},re={p:y,um:ne,m:de,r:ye,mt:ve,mc:k,pc:B,pbc:Y,n:P,o:e};return{render:G,hydrate:void 0,createApp:Sf(G)}}function to({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function bn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Df(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function hi(e,t,n=!1){const r=e.children,s=t.children;if(ce(r)&&ce(s))for(let o=0;o<r.length;o++){const i=r[o];let a=s[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=s[o]=un(s[o]),a.el=i.el),!n&&a.patchFlag!==-2&&hi(i,a)),a.type===Ls&&(a.el=i.el),a.type===xt&&!a.el&&(a.el=i.el)}}function If(e){const t=e.slice(),n=[0];let r,s,o,i,a;const l=e.length;for(r=0;r<l;r++){const c=e[r];if(c!==0){if(s=n[n.length-1],e[s]<c){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<c?o=a+1:i=a;c<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Eu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Eu(t)}function Zi(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const $f=Symbol.for("v-scx"),Bf=()=>nt($f);function st(e,t){return Bs(e,null,t)}function Au(e,t){return Bs(e,null,{flush:"post"})}function qe(e,t,n){return Bs(e,t,n)}function Bs(e,t,n=Pe){const{immediate:r,deep:s,flush:o,once:i}=n,a=Xe({},n),l=t&&r||!t&&o!=="post";let c;if(Pr){if(o==="sync"){const h=Bf();c=h.__watcherHandles||(h.__watcherHandles=[])}else if(!l){const h=()=>{};return h.stop=$t,h.resume=$t,h.pause=$t,h}}const u=Je;a.call=(h,m,y)=>qt(h,u,m,y);let d=!1;o==="post"?a.scheduler=h=>{et(h,u&&u.suspense)}:o!=="sync"&&(d=!0,a.scheduler=(h,m)=>{m?h():li(h)}),a.augmentJob=h=>{t&&(h.flags|=4),d&&(h.flags|=2,u&&(h.id=u.uid,h.i=u))};const p=Yd(e,t,a);return Pr&&(c?c.push(p):l&&p()),p}function qf(e,t,n){const r=this.proxy,s=Le(e)?e.includes(".")?Ou(r,e):()=>r[e]:e.bind(r,r);let o;fe(t)?o=t:(o=t.handler,n=t);const i=Lr(this),a=Bs(s,o.bind(r),n);return i(),a}function Ou(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const Lf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${lt(t)}Modifiers`]||e[`${gn(t)}Modifiers`];function Nf(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Pe;let s=n;const o=t.startsWith("update:"),i=o&&Lf(r,t.slice(7));i&&(i.trim&&(s=n.map(u=>Le(u)?u.trim():u)),i.number&&(s=n.map(So)));let a,l=r[a=pr(t)]||r[a=pr(lt(t))];!l&&o&&(l=r[a=pr(gn(t))]),l&&qt(l,e,6,s);const c=r[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,qt(c,e,6,s)}}function ku(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},a=!1;if(!fe(e)){const l=c=>{const u=ku(c,t,!0);u&&(a=!0,Xe(i,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(De(e)&&r.set(e,null),null):(ce(o)?o.forEach(l=>i[l]=null):Xe(i,o),De(e)&&r.set(e,i),i)}function qs(e,t){return!e||!ws(t)?!1:(t=t.slice(2).replace(/Once$/,""),Oe(e,t[0].toLowerCase()+t.slice(1))||Oe(e,gn(t))||Oe(e,t))}function ea(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:a,emit:l,render:c,renderCache:u,props:d,data:p,setupState:h,ctx:m,inheritAttrs:y}=e,_=hs(e);let C,O;try{if(n.shapeFlag&4){const x=s||r,R=x;C=Dt(c.call(R,x,u,d,h,p,m)),O=a}else{const x=t;C=Dt(x.length>1?x(d,{attrs:a,slots:i,emit:l}):x(d,null)),O=t.props?a:Ff(a)}}catch(x){br.length=0,Is(x,e,1),C=E(xt)}let b=C;if(O&&y!==!1){const x=Object.keys(O),{shapeFlag:R}=b;x.length&&R&7&&(o&&x.some(ti)&&(O=jf(O,o)),b=En(b,O,!1,!0))}return n.dirs&&(b=En(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&ui(b,n.transition),C=b,hs(_),C}const Ff=e=>{let t;for(const n in e)(n==="class"||n==="style"||ws(n))&&((t||(t={}))[n]=e[n]);return t},jf=(e,t)=>{const n={};for(const r in e)(!ti(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function zf(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:l}=t,c=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?ta(r,i,c):!!i;if(l&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const p=u[d];if(i[p]!==r[p]&&!qs(c,p))return!0}}}else return(s||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?ta(r,i,c):!0:!!i;return!1}function ta(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!qs(n,o))return!0}return!1}function Vf({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const Pu=e=>e.__isSuspense;function Hf(e,t){t&&t.pendingBranch?ce(e)?t.effects.push(...e):t.effects.push(e):Qd(e)}const Ue=Symbol.for("v-fgt"),Ls=Symbol.for("v-txt"),xt=Symbol.for("v-cmt"),no=Symbol.for("v-stc"),br=[];let ft=null;function T(e=!1){br.push(ft=e?null:[])}function Uf(){br.pop(),ft=br[br.length-1]||null}let Or=1;function na(e,t=!1){Or+=e,e<0&&ft&&t&&(ft.hasOnce=!0)}function Ru(e){return e.dynamicChildren=Or>0?ft||zn:null,Uf(),Or>0&&ft&&ft.push(e),e}function be(e,t,n,r,s,o){return Ru(oe(e,t,n,r,s,o,!0))}function z(e,t,n,r,s){return Ru(E(e,t,n,r,s,!0))}function kr(e){return e?e.__v_isVNode===!0:!1}function or(e,t){return e.type===t.type&&e.key===t.key}const Tu=({key:e})=>e??null,ls=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Le(e)||Re(e)||fe(e)?{i:We,r:e,k:t,f:!!n}:e:null);function oe(e,t=null,n=null,r=0,s=null,o=e===Ue?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Tu(t),ref:t&&ls(t),scopeId:eu,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:We};return a?(mi(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Le(n)?8:16),Or>0&&!i&&ft&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&ft.push(l),l}const E=Wf;function Wf(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===cu)&&(e=xt),kr(e)){const a=En(e,t,!0);return n&&mi(a,n),Or>0&&!o&&ft&&(a.shapeFlag&6?ft[ft.indexOf(e)]=a:ft.push(a)),a.patchFlag=-2,a}if(ep(e)&&(e=e.__vccOpts),t){t=mt(t);let{class:a,style:l}=t;a&&!Le(a)&&(t.class=Ee(a)),De(l)&&(ai(l)&&!ce(l)&&(l=Xe({},l)),t.style=vn(l))}const i=Le(e)?1:Pu(e)?128:Zd(e)?64:De(e)?4:fe(e)?2:0;return oe(e,t,n,r,s,i,o,!0)}function mt(e){return e?ai(e)||bu(e)?Xe({},e):e:null}function En(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:a,transition:l}=e,c=t?ue(s||{},t):s,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Tu(c),ref:t&&t.ref?n&&o?ce(o)?o.concat(ls(t)):[o,ls(t)]:ls(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ue?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&En(e.ssContent),ssFallback:e.ssFallback&&En(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&ui(u,l.clone(u)),u}function $e(e=" ",t=0){return E(Ls,null,e,t)}function qr(e="",t=!1){return t?(T(),z(xt,null,e)):E(xt,null,e)}function Dt(e){return e==null||typeof e=="boolean"?E(xt):ce(e)?E(Ue,null,e.slice()):kr(e)?un(e):E(Ls,null,String(e))}function un(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:En(e)}function mi(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(ce(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),mi(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!bu(t)?t._ctx=We:s===3&&We&&(We.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else fe(t)?(t={default:t,_ctx:We},n=32):(t=String(t),r&64?(n=16,t=[$e(t)]):n=8);e.children=t,e.shapeFlag|=n}function ue(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Ee([t.class,r.class]));else if(s==="style")t.style=vn([t.style,r.style]);else if(ws(s)){const o=t[s],i=r[s];i&&o!==i&&!(ce(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Tt(e,t,n,r=null){qt(e,t,7,[n,r])}const Kf=gu();let Gf=0;function Yf(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||Kf,o={uid:Gf++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Pl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wu(r,s),emitsOptions:ku(r,s),emit:null,emitted:null,propsDefaults:Pe,inheritAttrs:r.inheritAttrs,ctx:Pe,data:Pe,props:Pe,attrs:Pe,slots:Pe,refs:Pe,setupState:Pe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Nf.bind(null,o),e.ce&&e.ce(o),o}let Je=null;const ut=()=>Je||We;let gs,$o;{const e=Es(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};gs=t("__VUE_INSTANCE_SETTERS__",n=>Je=n),$o=t("__VUE_SSR_SETTERS__",n=>Pr=n)}const Lr=e=>{const t=Je;return gs(e),e.scope.on(),()=>{e.scope.off(),gs(t)}},ra=()=>{Je&&Je.scope.off(),gs(null)};function Mu(e){return e.vnode.shapeFlag&4}let Pr=!1;function Jf(e,t=!1,n=!1){t&&$o(t);const{props:r,children:s}=e.vnode,o=Mu(e);Ef(e,r,o,t),Pf(e,s,n||t);const i=o?Xf(e,t):void 0;return t&&$o(!1),i}function Xf(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,mf);const{setup:r}=n;if(r){Yt();const s=e.setupContext=r.length>1?Iu(e):null,o=Lr(e),i=Br(r,e,0,[e.props,s]),a=Cl(i);if(Jt(),o(),(a||e.sp)&&!Un(e)&&ou(e),a){if(i.then(ra,ra),t)return i.then(l=>{sa(e,l)}).catch(l=>{Is(l,e,0)});e.asyncDep=i}else sa(e,i)}else Du(e)}function sa(e,t,n){fe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:De(t)&&(e.setupState=Gl(t)),Du(e)}function Du(e,t,n){const r=e.type;e.render||(e.render=r.render||$t);{const s=Lr(e);Yt();try{yf(e)}finally{Jt(),s()}}}const Qf={get(e,t){return Ye(e,"get",""),e[t]}};function Iu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Qf),slots:e.slots,emit:e.emit,expose:t}}function Ns(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Gl(Ds(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in yr)return yr[n](e)},has(t,n){return n in t||n in yr}})):e.proxy}function Zf(e,t=!0){return fe(e)?e.displayName||e.name:e.name||t&&e.__name}function ep(e){return fe(e)&&"__vccOpts"in e}const H=(e,t)=>Kd(e,t,Pr);function _t(e,t,n){const r=arguments.length;return r===2?De(t)&&!ce(t)?kr(t)?E(e,null,[t]):E(e,t):E(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&kr(n)&&(n=[n]),E(e,t,n))}const tp="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Bo;const oa=typeof window<"u"&&window.trustedTypes;if(oa)try{Bo=oa.createPolicy("vue",{createHTML:e=>e})}catch{}const $u=Bo?e=>Bo.createHTML(e):e=>e,np="http://www.w3.org/2000/svg",rp="http://www.w3.org/1998/Math/MathML",Ut=typeof document<"u"?document:null,ia=Ut&&Ut.createElement("template"),sp={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?Ut.createElementNS(np,e):t==="mathml"?Ut.createElementNS(rp,e):n?Ut.createElement(e,{is:n}):Ut.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>Ut.createTextNode(e),createComment:e=>Ut.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ut.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{ia.innerHTML=$u(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=ia.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},op=Symbol("_vtc");function ip(e,t,n){const r=e[op];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vs=Symbol("_vod"),Bu=Symbol("_vsh"),ap={beforeMount(e,{value:t},{transition:n}){e[vs]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):ir(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),ir(e,!0),r.enter(e)):r.leave(e,()=>{ir(e,!1)}):ir(e,t))},beforeUnmount(e,{value:t}){ir(e,t)}};function ir(e,t){e.style.display=t?e[vs]:"none",e[Bu]=!t}const lp=Symbol(""),up=/(^|;)\s*display\s*:/;function cp(e,t,n){const r=e.style,s=Le(n);let o=!1;if(n&&!s){if(t)if(Le(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&us(r,a,"")}else for(const i in t)n[i]==null&&us(r,i,"");for(const i in n)i==="display"&&(o=!0),us(r,i,n[i])}else if(s){if(t!==n){const i=r[lp];i&&(n+=";"+i),r.cssText=n,o=up.test(n)}}else t&&e.removeAttribute("style");vs in e&&(e[vs]=o?r.display:"",e[Bu]&&(r.display="none"))}const aa=/\s*!important$/;function us(e,t,n){if(ce(n))n.forEach(r=>us(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=dp(e,t);aa.test(n)?e.setProperty(gn(r),n.replace(aa,""),"important"):e[r]=n}}const la=["Webkit","Moz","ms"],ro={};function dp(e,t){const n=ro[t];if(n)return n;let r=lt(t);if(r!=="filter"&&r in e)return ro[t]=r;r=Ss(r);for(let s=0;s<la.length;s++){const o=la[s]+r;if(o in e)return ro[t]=o}return t}const ua="http://www.w3.org/1999/xlink";function ca(e,t,n,r,s,o=bd(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ua,t.slice(6,t.length)):e.setAttributeNS(ua,t,n):n==null||o&&!Al(n)?e.removeAttribute(t):e.setAttribute(t,o?"":en(n)?String(n):n)}function da(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?$u(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Al(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function Nn(e,t,n,r){e.addEventListener(t,n,r)}function fp(e,t,n,r){e.removeEventListener(t,n,r)}const fa=Symbol("_vei");function pp(e,t,n,r,s=null){const o=e[fa]||(e[fa]={}),i=o[t];if(r&&i)i.value=r;else{const[a,l]=hp(t);if(r){const c=o[t]=vp(r,s);Nn(e,a,c,l)}else i&&(fp(e,a,i,l),o[t]=void 0)}}const pa=/(?:Once|Passive|Capture)$/;function hp(e){let t;if(pa.test(e)){t={};let r;for(;r=e.match(pa);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):gn(e.slice(2)),t]}let so=0;const mp=Promise.resolve(),gp=()=>so||(mp.then(()=>so=0),so=Date.now());function vp(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;qt(yp(r,n.value),t,5,[r])};return n.value=e,n.attached=gp(),n}function yp(e,t){if(ce(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const ha=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,bp=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?ip(e,r,i):t==="style"?cp(e,n,r):ws(t)?ti(t)||pp(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):_p(e,t,r,i))?(da(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ca(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Le(r))?da(e,lt(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),ca(e,t,r,i))};function _p(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&ha(t)&&fe(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return ha(t)&&Le(n)?!1:t in e}const ma=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ce(t)?n=>is(t,n):t};function wp(e){e.target.composing=!0}function ga(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const oo=Symbol("_assign"),xp={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[oo]=ma(s);const o=r||s.props&&s.props.type==="number";Nn(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=So(a)),e[oo](a)}),n&&Nn(e,"change",()=>{e.value=e.value.trim()}),t||(Nn(e,"compositionstart",wp),Nn(e,"compositionend",ga),Nn(e,"change",ga))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[oo]=ma(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?So(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===l)||(e.value=l))}},Cp=["ctrl","shift","alt","meta"],Sp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Cp.some(n=>e[`${n}Key`]&&!t.includes(n))},qu=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const a=Sp[t[i]];if(a&&a(s,t))return}return e(s,...o)})},Ep={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Lu=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=s=>{if(!("key"in s))return;const o=gn(s.key);if(t.some(i=>i===o||Ep[i]===o))return e(s)})},Ap=Xe({patchProp:bp},sp);let va;function Op(){return va||(va=Tf(Ap))}const kp=(...e)=>{const t=Op().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Rp(r);if(!s)return;const o=t._component;!fe(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,Pp(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function Pp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Rp(e){return Le(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Nu;const Fs=e=>Nu=e,Fu=Symbol();function qo(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var _r;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(_r||(_r={}));function Tp(){const e=As(!0),t=e.run(()=>L({}));let n=[],r=[];const s=Ds({install(o){Fs(s),s._a=o,o.provide(Fu,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const ju=()=>{};function ya(e,t,n,r=ju){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&$r()&&Os(s),s}function Tn(e,...t){e.slice().forEach(n=>{n(...t)})}const Mp=e=>e(),ba=Symbol(),io=Symbol();function Lo(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];qo(s)&&qo(r)&&e.hasOwnProperty(n)&&!Re(r)&&!fn(r)?e[n]=Lo(s,r):e[n]=r}return e}const Dp=Symbol();function Ip(e){return!qo(e)||!Object.prototype.hasOwnProperty.call(e,Dp)}const{assign:on}=Object;function $p(e){return!!(Re(e)&&e.effect)}function Bp(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let l;function c(){a||(n.state.value[e]=s?s():{});const u=wt(n.state.value[e]);return on(u,o,Object.keys(i||{}).reduce((d,p)=>(d[p]=Ds(H(()=>{Fs(n);const h=n._s.get(e);return i[p].call(h,h)})),d),{}))}return l=zu(e,c,t,n,r,!0),l}function zu(e,t,n={},r,s,o){let i;const a=on({actions:{}},n),l={deep:!0};let c,u,d=[],p=[],h;const m=r.state.value[e];!o&&!m&&(r.state.value[e]={}),L({});let y;function _(k){let A;c=u=!1,typeof k=="function"?(k(r.state.value[e]),A={type:_r.patchFunction,storeId:e,events:h}):(Lo(r.state.value[e],k),A={type:_r.patchObject,payload:k,storeId:e,events:h});const Y=y=Symbol();Ke().then(()=>{y===Y&&(c=!0)}),u=!0,Tn(d,A,r.state.value[e])}const C=o?function(){const{state:A}=n,Y=A?A():{};this.$patch(se=>{on(se,Y)})}:ju;function O(){i.stop(),d=[],p=[],r._s.delete(e)}const b=(k,A="")=>{if(ba in k)return k[io]=A,k;const Y=function(){Fs(r);const se=Array.from(arguments),le=[],ge=[];function ve(U){le.push(U)}function xe(U){ge.push(U)}Tn(p,{args:se,name:Y[io],store:R,after:ve,onError:xe});let V;try{V=k.apply(this&&this.$id===e?this:R,se)}catch(U){throw Tn(ge,U),U}return V instanceof Promise?V.then(U=>(Tn(le,U),U)).catch(U=>(Tn(ge,U),Promise.reject(U))):(Tn(le,V),V)};return Y[ba]=!0,Y[io]=A,Y},x={_p:r,$id:e,$onAction:ya.bind(null,p),$patch:_,$reset:C,$subscribe(k,A={}){const Y=ya(d,k,A.detached,()=>se()),se=i.run(()=>qe(()=>r.state.value[e],le=>{(A.flush==="sync"?u:c)&&k({storeId:e,type:_r.direct,events:h},le)},on({},l,A)));return Y},$dispose:O},R=Xt(x);r._s.set(e,R);const F=(r._a&&r._a.runWithContext||Mp)(()=>r._e.run(()=>(i=As()).run(()=>t({action:b}))));for(const k in F){const A=F[k];if(Re(A)&&!$p(A)||fn(A))o||(m&&Ip(A)&&(Re(A)?A.value=m[k]:Lo(A,m[k])),r.state.value[e][k]=A);else if(typeof A=="function"){const Y=b(A,k);F[k]=Y,a.actions[k]=A}}return on(R,F),on(Ce(R),F),Object.defineProperty(R,"$state",{get:()=>r.state.value[e],set:k=>{_(A=>{on(A,k)})}}),r._p.forEach(k=>{on(R,i.run(()=>k({store:R,app:r._a,pinia:r,options:a})))}),m&&o&&n.hydrate&&n.hydrate(R.$state,m),c=!0,u=!0,R}/*! #__NO_SIDE_EFFECTS__ */function gi(e,t,n){let r;const s=typeof t=="function";r=s?n:t;function o(i,a){const l=di();return i=i||(l?nt(Fu,null):null),i&&Fs(i),i=Nu,i._s.has(e)||(s?zu(e,t,r,i):Bp(e,r,i)),i._s.get(e)}return o.$id=e,o}function Vu(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Vu(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Hu(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=Vu(e))&&(r&&(r+=" "),r+=t);return r}const _a=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,wa=Hu,vi=(e,t)=>n=>{var r;if(t?.variants==null)return wa(e,n?.class,n?.className);const{variants:s,defaultVariants:o}=t,i=Object.keys(s).map(c=>{const u=n?.[c],d=o?.[c];if(u===null)return null;const p=_a(u)||_a(d);return s[c][p]}),a=n&&Object.entries(n).reduce((c,u)=>{let[d,p]=u;return p===void 0||(c[d]=p),c},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,u)=>{let{class:d,className:p,...h}=u;return Object.entries(h).every(m=>{let[y,_]=m;return Array.isArray(_)?_.includes({...o,...a}[y]):{...o,...a}[y]===_})?[...c,d,p]:c},[]);return wa(e,i,l,n?.class,n?.className)},yi="-",qp=e=>{const t=Np(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(yi);return a[0]===""&&a.length!==1&&a.shift(),Uu(a,t)||Lp(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},Uu=(e,t)=>{if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?Uu(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const o=e.join(yi);return t.validators.find(({validator:i})=>i(o))?.classGroupId},xa=/^\[(.+)\]$/,Lp=e=>{if(xa.test(e)){const t=xa.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Np=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const s in n)No(n[s],r,s,t);return r},No=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const o=s===""?t:Ca(t,s);o.classGroupId=n;return}if(typeof s=="function"){if(Fp(s)){No(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([o,i])=>{No(i,Ca(t,o),n,r)})})},Ca=(e,t)=>{let n=e;return t.split(yi).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Fp=e=>e.isThemeGetter,jp=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return s(o,i),i},set(o,i){n.has(o)?n.set(o,i):s(o,i)}}},Fo="!",jo=":",zp=jo.length,Vp=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=s=>{const o=[];let i=0,a=0,l=0,c;for(let m=0;m<s.length;m++){let y=s[m];if(i===0&&a===0){if(y===jo){o.push(s.slice(l,m)),l=m+zp;continue}if(y==="/"){c=m;continue}}y==="["?i++:y==="]"?i--:y==="("?a++:y===")"&&a--}const u=o.length===0?s:s.substring(l),d=Hp(u),p=d!==u,h=c&&c>l?c-l:void 0;return{modifiers:o,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:h}};if(t){const s=t+jo,o=r;r=i=>i.startsWith(s)?o(i.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(n){const s=r;r=o=>n({className:o,parseClassName:s})}return r},Hp=e=>e.endsWith(Fo)?e.substring(0,e.length-1):e.startsWith(Fo)?e.substring(1):e,Up=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const s=[];let o=[];return r.forEach(i=>{i[0]==="["||t[i]?(s.push(...o.sort(),i),o=[]):o.push(i)}),s.push(...o.sort()),s}},Wp=e=>({cache:jp(e.cacheSize),parseClassName:Vp(e),sortModifiers:Up(e),...qp(e)}),Kp=/\s+/,Gp=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:o}=t,i=[],a=e.trim().split(Kp);let l="";for(let c=a.length-1;c>=0;c-=1){const u=a[c],{isExternal:d,modifiers:p,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:y}=n(u);if(d){l=u+(l.length>0?" "+l:l);continue}let _=!!y,C=r(_?m.substring(0,y):m);if(!C){if(!_){l=u+(l.length>0?" "+l:l);continue}if(C=r(m),!C){l=u+(l.length>0?" "+l:l);continue}_=!1}const O=o(p).join(":"),b=h?O+Fo:O,x=b+C;if(i.includes(x))continue;i.push(x);const R=s(C,_);for(let N=0;N<R.length;++N){const F=R[N];i.push(b+F)}l=u+(l.length>0?" "+l:l)}return l};function Yp(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Wu(t))&&(r&&(r+=" "),r+=n);return r}const Wu=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Wu(e[r]))&&(n&&(n+=" "),n+=t);return n};function Jp(e,...t){let n,r,s,o=i;function i(l){const c=t.reduce((u,d)=>d(u),e());return n=Wp(c),r=n.cache.get,s=n.cache.set,o=a,a(l)}function a(l){const c=r(l);if(c)return c;const u=Gp(l,n);return s(l,u),u}return function(){return o(Yp.apply(null,arguments))}}const ze=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ku=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Gu=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Xp=/^\d+\/\d+$/,Qp=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Zp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,eh=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,th=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,nh=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Mn=e=>Xp.test(e),me=e=>!!e&&!Number.isNaN(Number(e)),rn=e=>!!e&&Number.isInteger(Number(e)),ao=e=>e.endsWith("%")&&me(e.slice(0,-1)),zt=e=>Qp.test(e),rh=()=>!0,sh=e=>Zp.test(e)&&!eh.test(e),Yu=()=>!1,oh=e=>th.test(e),ih=e=>nh.test(e),ah=e=>!ee(e)&&!te(e),lh=e=>Zn(e,Qu,Yu),ee=e=>Ku.test(e),_n=e=>Zn(e,Zu,sh),lo=e=>Zn(e,ph,me),Sa=e=>Zn(e,Ju,Yu),uh=e=>Zn(e,Xu,ih),Gr=e=>Zn(e,ec,oh),te=e=>Gu.test(e),ar=e=>er(e,Zu),ch=e=>er(e,hh),Ea=e=>er(e,Ju),dh=e=>er(e,Qu),fh=e=>er(e,Xu),Yr=e=>er(e,ec,!0),Zn=(e,t,n)=>{const r=Ku.exec(e);return r?r[1]?t(r[1]):n(r[2]):!1},er=(e,t,n=!1)=>{const r=Gu.exec(e);return r?r[1]?t(r[1]):n:!1},Ju=e=>e==="position"||e==="percentage",Xu=e=>e==="image"||e==="url",Qu=e=>e==="length"||e==="size"||e==="bg-size",Zu=e=>e==="length",ph=e=>e==="number",hh=e=>e==="family-name",ec=e=>e==="shadow",mh=()=>{const e=ze("color"),t=ze("font"),n=ze("text"),r=ze("font-weight"),s=ze("tracking"),o=ze("leading"),i=ze("breakpoint"),a=ze("container"),l=ze("spacing"),c=ze("radius"),u=ze("shadow"),d=ze("inset-shadow"),p=ze("text-shadow"),h=ze("drop-shadow"),m=ze("blur"),y=ze("perspective"),_=ze("aspect"),C=ze("ease"),O=ze("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],R=()=>[...x(),te,ee],N=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],k=()=>[te,ee,l],A=()=>[Mn,"full","auto",...k()],Y=()=>[rn,"none","subgrid",te,ee],se=()=>["auto",{span:["full",rn,te,ee]},rn,te,ee],le=()=>[rn,"auto",te,ee],ge=()=>["auto","min","max","fr",te,ee],ve=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],xe=()=>["start","end","center","stretch","center-safe","end-safe"],V=()=>["auto",...k()],U=()=>[Mn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],B=()=>[e,te,ee],Ne=()=>[...x(),Ea,Sa,{position:[te,ee]}],X=()=>["no-repeat",{repeat:["","x","y","space","round"]}],de=()=>["auto","cover","contain",dh,lh,{size:[te,ee]}],ne=()=>[ao,ar,_n],ye=()=>["","none","full",c,te,ee],je=()=>["",me,ar,_n],Ft=()=>["solid","dashed","dotted","double"],Qe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>[me,ao,Ea,Sa],Z=()=>["","none",m,te,ee],G=()=>["none",me,te,ee],re=()=>["none",me,te,ee],Se=()=>[me,te,ee],g=()=>[Mn,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[zt],breakpoint:[zt],color:[rh],container:[zt],"drop-shadow":[zt],ease:["in","out","in-out"],font:[ah],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[zt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[zt],shadow:[zt],spacing:["px",me],text:[zt],"text-shadow":[zt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Mn,ee,te,_]}],container:["container"],columns:[{columns:[me,ee,te,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:R()}],overflow:[{overflow:N()}],"overflow-x":[{"overflow-x":N()}],"overflow-y":[{"overflow-y":N()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[rn,"auto",te,ee]}],basis:[{basis:[Mn,"full","auto",a,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[me,Mn,"auto","initial","none",ee]}],grow:[{grow:["",me,te,ee]}],shrink:[{shrink:["",me,te,ee]}],order:[{order:[rn,"first","last","none",te,ee]}],"grid-cols":[{"grid-cols":Y()}],"col-start-end":[{col:se()}],"col-start":[{"col-start":le()}],"col-end":[{"col-end":le()}],"grid-rows":[{"grid-rows":Y()}],"row-start-end":[{row:se()}],"row-start":[{"row-start":le()}],"row-end":[{"row-end":le()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ge()}],"auto-rows":[{"auto-rows":ge()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...ve(),"normal"]}],"justify-items":[{"justify-items":[...xe(),"normal"]}],"justify-self":[{"justify-self":["auto",...xe()]}],"align-content":[{content:["normal",...ve()]}],"align-items":[{items:[...xe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...xe(),{baseline:["","last"]}]}],"place-content":[{"place-content":ve()}],"place-items":[{"place-items":[...xe(),"baseline"]}],"place-self":[{"place-self":["auto",...xe()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:V()}],mx:[{mx:V()}],my:[{my:V()}],ms:[{ms:V()}],me:[{me:V()}],mt:[{mt:V()}],mr:[{mr:V()}],mb:[{mb:V()}],ml:[{ml:V()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:U()}],w:[{w:[a,"screen",...U()]}],"min-w":[{"min-w":[a,"screen","none",...U()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...U()]}],h:[{h:["screen","lh",...U()]}],"min-h":[{"min-h":["screen","lh","none",...U()]}],"max-h":[{"max-h":["screen","lh",...U()]}],"font-size":[{text:["base",n,ar,_n]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,te,lo]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ao,ee]}],"font-family":[{font:[ch,ee,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,te,ee]}],"line-clamp":[{"line-clamp":[me,"none",te,lo]}],leading:[{leading:[o,...k()]}],"list-image":[{"list-image":["none",te,ee]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",te,ee]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Ft(),"wavy"]}],"text-decoration-thickness":[{decoration:[me,"from-font","auto",te,_n]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[me,"auto",te,ee]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",te,ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",te,ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Ne()}],"bg-repeat":[{bg:X()}],"bg-size":[{bg:de()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},rn,te,ee],radial:["",te,ee],conic:[rn,te,ee]},fh,uh]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:ne()}],"gradient-via-pos":[{via:ne()}],"gradient-to-pos":[{to:ne()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:ye()}],"rounded-s":[{"rounded-s":ye()}],"rounded-e":[{"rounded-e":ye()}],"rounded-t":[{"rounded-t":ye()}],"rounded-r":[{"rounded-r":ye()}],"rounded-b":[{"rounded-b":ye()}],"rounded-l":[{"rounded-l":ye()}],"rounded-ss":[{"rounded-ss":ye()}],"rounded-se":[{"rounded-se":ye()}],"rounded-ee":[{"rounded-ee":ye()}],"rounded-es":[{"rounded-es":ye()}],"rounded-tl":[{"rounded-tl":ye()}],"rounded-tr":[{"rounded-tr":ye()}],"rounded-br":[{"rounded-br":ye()}],"rounded-bl":[{"rounded-bl":ye()}],"border-w":[{border:je()}],"border-w-x":[{"border-x":je()}],"border-w-y":[{"border-y":je()}],"border-w-s":[{"border-s":je()}],"border-w-e":[{"border-e":je()}],"border-w-t":[{"border-t":je()}],"border-w-r":[{"border-r":je()}],"border-w-b":[{"border-b":je()}],"border-w-l":[{"border-l":je()}],"divide-x":[{"divide-x":je()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":je()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Ft(),"hidden","none"]}],"divide-style":[{divide:[...Ft(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...Ft(),"none","hidden"]}],"outline-offset":[{"outline-offset":[me,te,ee]}],"outline-w":[{outline:["",me,ar,_n]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",u,Yr,Gr]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",d,Yr,Gr]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:je()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[me,_n]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":je()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",p,Yr,Gr]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[me,te,ee]}],"mix-blend":[{"mix-blend":[...Qe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Qe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[me]}],"mask-image-linear-from-pos":[{"mask-linear-from":P()}],"mask-image-linear-to-pos":[{"mask-linear-to":P()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":P()}],"mask-image-t-to-pos":[{"mask-t-to":P()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":P()}],"mask-image-r-to-pos":[{"mask-r-to":P()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":P()}],"mask-image-b-to-pos":[{"mask-b-to":P()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":P()}],"mask-image-l-to-pos":[{"mask-l-to":P()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":P()}],"mask-image-x-to-pos":[{"mask-x-to":P()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":P()}],"mask-image-y-to-pos":[{"mask-y-to":P()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[te,ee]}],"mask-image-radial-from-pos":[{"mask-radial-from":P()}],"mask-image-radial-to-pos":[{"mask-radial-to":P()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[me]}],"mask-image-conic-from-pos":[{"mask-conic-from":P()}],"mask-image-conic-to-pos":[{"mask-conic-to":P()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Ne()}],"mask-repeat":[{mask:X()}],"mask-size":[{mask:de()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",te,ee]}],filter:[{filter:["","none",te,ee]}],blur:[{blur:Z()}],brightness:[{brightness:[me,te,ee]}],contrast:[{contrast:[me,te,ee]}],"drop-shadow":[{"drop-shadow":["","none",h,Yr,Gr]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",me,te,ee]}],"hue-rotate":[{"hue-rotate":[me,te,ee]}],invert:[{invert:["",me,te,ee]}],saturate:[{saturate:[me,te,ee]}],sepia:[{sepia:["",me,te,ee]}],"backdrop-filter":[{"backdrop-filter":["","none",te,ee]}],"backdrop-blur":[{"backdrop-blur":Z()}],"backdrop-brightness":[{"backdrop-brightness":[me,te,ee]}],"backdrop-contrast":[{"backdrop-contrast":[me,te,ee]}],"backdrop-grayscale":[{"backdrop-grayscale":["",me,te,ee]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[me,te,ee]}],"backdrop-invert":[{"backdrop-invert":["",me,te,ee]}],"backdrop-opacity":[{"backdrop-opacity":[me,te,ee]}],"backdrop-saturate":[{"backdrop-saturate":[me,te,ee]}],"backdrop-sepia":[{"backdrop-sepia":["",me,te,ee]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",te,ee]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[me,"initial",te,ee]}],ease:[{ease:["linear","initial",C,te,ee]}],delay:[{delay:[me,te,ee]}],animate:[{animate:["none",O,te,ee]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,te,ee]}],"perspective-origin":[{"perspective-origin":R()}],rotate:[{rotate:G()}],"rotate-x":[{"rotate-x":G()}],"rotate-y":[{"rotate-y":G()}],"rotate-z":[{"rotate-z":G()}],scale:[{scale:re()}],"scale-x":[{"scale-x":re()}],"scale-y":[{"scale-y":re()}],"scale-z":[{"scale-z":re()}],"scale-3d":["scale-3d"],skew:[{skew:Se()}],"skew-x":[{"skew-x":Se()}],"skew-y":[{"skew-y":Se()}],transform:[{transform:[te,ee,"","none","gpu","cpu"]}],"transform-origin":[{origin:R()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:g()}],"translate-x":[{"translate-x":g()}],"translate-y":[{"translate-y":g()}],"translate-z":[{"translate-z":g()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",te,ee]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",te,ee]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[me,ar,_n,lo]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},gh=Jp(mh);function he(...e){return gh(Hu(e))}function gt(e,t){const n=typeof e=="string"&&!t?`${e}Context`:t,r=Symbol(n);return[i=>{const a=nt(r,i);if(a||a===null)return a;throw new Error(`Injection \`${r.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(", ")}`:`\`${e}\``}`)},i=>(Wn(r,i),i)]}function at(){let e=document.activeElement;if(e==null)return null;for(;e!=null&&e.shadowRoot!=null&&e.shadowRoot.activeElement!=null;)e=e.shadowRoot.activeElement;return e}function tc(e,t,n){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),r.dispatchEvent(s)}function bi(e){return e?e.flatMap(t=>t.type===Ue?bi(t.children):[t]):[]}const vh=["INPUT","TEXTAREA"];function yh(e,t,n,r={}){if(!t||r.enableIgnoredElement&&vh.includes(t.nodeName))return null;const{arrowKeyOptions:s="both",attributeName:o="[data-reka-collection-item]",itemsArray:i=[],loop:a=!0,dir:l="ltr",preventScroll:c=!0,focus:u=!1}=r,[d,p,h,m,y,_]=[e.key==="ArrowRight",e.key==="ArrowLeft",e.key==="ArrowUp",e.key==="ArrowDown",e.key==="Home",e.key==="End"],C=h||m,O=d||p;if(!y&&!_&&(!C&&!O||s==="vertical"&&O||s==="horizontal"&&C))return null;const b=n?Array.from(n.querySelectorAll(o)):i;if(!b.length)return null;c&&e.preventDefault();let x=null;return O||C?x=nc(b,t,{goForward:C?m:l==="ltr"?d:p,loop:a}):y?x=b.at(0)||null:_&&(x=b.at(-1)||null),u&&x?.focus(),x}function nc(e,t,n,r=e.length){if(--r===0)return null;const s=e.indexOf(t),o=n.goForward?s+1:s-1;if(!n.loop&&(o<0||o>=e.length))return null;const i=(o+e.length)%e.length,a=e[i];return a?a.hasAttribute("disabled")&&a.getAttribute("disabled")!=="false"?nc(e,a,n,r):a:null}const[rc,Yw]=gt("ConfigProvider");function bh(e,t){var n;const r=Gt();return st(()=>{r.value=e()},{...t,flush:(n=void 0)!=null?n:"sync"}),Ts(r)}function Nr(e){return $r()?(Os(e),!0):!1}function _h(){const e=new Set,t=o=>{e.delete(o)};return{on:o=>{e.add(o);const i=()=>t(o);return Nr(i),{off:i}},off:t,trigger:(...o)=>Promise.all(Array.from(e).map(i=>i(...o))),clear:()=>{e.clear()}}}function wh(e){let t=!1,n;const r=As(!0);return(...s)=>(t||(n=r.run(()=>e(...s)),t=!0),n)}function sc(e){let t=0,n,r;const s=()=>{t-=1,r&&t<=0&&(r.stop(),n=void 0,r=void 0)};return(...o)=>(t+=1,r||(r=As(!0),n=r.run(()=>e(...o))),Nr(s),n)}const At=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const xh=e=>typeof e<"u",Ch=Object.prototype.toString,Sh=e=>Ch.call(e)==="[object Object]",Aa=Eh();function Eh(){var e,t;return At&&((e=window?.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window?.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function Ah(e){return ut()}function uo(e){return Array.isArray(e)?e:[e]}function oc(e,t=1e4){return zd((n,r)=>{let s=Fe(e),o;const i=()=>setTimeout(()=>{s=Fe(e),r()},Fe(t));return Nr(()=>{clearTimeout(o)}),{get(){return n(),s},set(a){s=a,r(),clearTimeout(o),o=i()}}})}function Oh(e,t){Ah()&&lu(e,t)}function ic(e,t,n={}){const{immediate:r=!0,immediateCallback:s=!1}=n,o=Gt(!1);let i=null;function a(){i&&(clearTimeout(i),i=null)}function l(){o.value=!1,a()}function c(...u){s&&e(),a(),o.value=!0,i=setTimeout(()=>{o.value=!1,i=null,e(...u)},Fe(t))}return r&&(o.value=!0,At&&c()),Nr(l),{isPending:Ts(o),start:c,stop:l}}function kh(e,t,n){return qe(e,t,{...n,immediate:!0})}const _i=At?window:void 0;function tr(e){var t;const n=Fe(e);return(t=n?.$el)!=null?t:n}function Gn(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,l,c,u)=>(a.addEventListener(l,c,u),()=>a.removeEventListener(l,c,u)),s=H(()=>{const a=uo(Fe(e[0])).filter(l=>l!=null);return a.every(l=>typeof l!="string")?a:void 0}),o=kh(()=>{var a,l;return[(l=(a=s.value)==null?void 0:a.map(c=>tr(c)))!=null?l:[_i].filter(c=>c!=null),uo(Fe(s.value?e[1]:e[0])),uo(f(s.value?e[2]:e[1])),Fe(s.value?e[3]:e[2])]},([a,l,c,u])=>{if(n(),!a?.length||!l?.length||!c?.length)return;const d=Sh(u)?{...u}:u;t.push(...a.flatMap(p=>l.flatMap(h=>c.map(m=>r(p,h,m,d)))))},{flush:"post"}),i=()=>{o(),n()};return Nr(n),i}function Ph(){const e=Gt(!1),t=ut();return t&&ht(()=>{e.value=!0},t),e}function Rh(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function Th(...e){let t,n,r={};e.length===3?(t=e[0],n=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],r=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:s=_i,eventName:o="keydown",passive:i=!1,dedupe:a=!1}=r,l=Rh(t);return Gn(s,o,u=>{u.repeat&&Fe(a)||l(u)&&n(u)},i)}function Mh(e){return JSON.parse(JSON.stringify(e))}function Fr(e,t,n,r={}){var s,o,i;const{clone:a=!1,passive:l=!1,eventName:c,deep:u=!1,defaultValue:d,shouldEmit:p}=r,h=ut(),m=n||h?.emit||((s=h?.$emit)==null?void 0:s.bind(h))||((i=(o=h?.proxy)==null?void 0:o.$emit)==null?void 0:i.bind(h?.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const _=b=>a?typeof a=="function"?a(b):Mh(b):b,C=()=>xh(e[t])?_(e[t]):d,O=b=>{p?p(b)&&m(y,b):m(y,b)};if(l){const b=C(),x=L(b);let R=!1;return qe(()=>e[t],N=>{R||(R=!0,x.value=_(N),Ke(()=>R=!1))}),qe(x,N=>{!R&&(N!==e[t]||u)&&O(N)},{deep:u}),x}else return H({get(){return C()},set(b){O(b)}})}function co(e){if(e===null||typeof e!="object")return!1;const t=Object.getPrototypeOf(e);return t!==null&&t!==Object.prototype&&Object.getPrototypeOf(t)!==null||Symbol.iterator in e?!1:Symbol.toStringTag in e?Object.prototype.toString.call(e)==="[object Module]":!0}function zo(e,t,n=".",r){if(!co(t))return zo(e,{},n,r);const s=Object.assign({},t);for(const o in e){if(o==="__proto__"||o==="constructor")continue;const i=e[o];i!=null&&(r&&r(s,o,i,n)||(Array.isArray(i)&&Array.isArray(s[o])?s[o]=[...i,...s[o]]:co(i)&&co(s[o])?s[o]=zo(i,s[o],(n?`${n}.`:"")+o.toString(),r):s[o]=i))}return s}function Dh(e){return(...t)=>t.reduce((n,r)=>zo(n,r,"",e),{})}const Ih=Dh(),$h=sc(()=>{const e=L(new Map),t=L(),n=H(()=>{for(const i of e.value.values())if(i)return!0;return!1}),r=rc({scrollBody:L(!0)});let s=null;const o=()=>{document.body.style.paddingRight="",document.body.style.marginRight="",document.body.style.pointerEvents="",document.documentElement.style.removeProperty("--scrollbar-width"),document.body.style.overflow=t.value??"",Aa&&s?.(),t.value=void 0};return qe(n,(i,a)=>{if(!At)return;if(!i){a&&o();return}t.value===void 0&&(t.value=document.body.style.overflow);const l=window.innerWidth-document.documentElement.clientWidth,c={padding:l,margin:0},u=r.scrollBody?.value?typeof r.scrollBody.value=="object"?Ih({padding:r.scrollBody.value.padding===!0?l:r.scrollBody.value.padding,margin:r.scrollBody.value.margin===!0?l:r.scrollBody.value.margin},c):c:{padding:0,margin:0};l>0&&(document.body.style.paddingRight=typeof u.padding=="number"?`${u.padding}px`:String(u.padding),document.body.style.marginRight=typeof u.margin=="number"?`${u.margin}px`:String(u.margin),document.documentElement.style.setProperty("--scrollbar-width",`${l}px`),document.body.style.overflow="hidden"),Aa&&(s=Gn(document,"touchmove",d=>Bh(d),{passive:!1})),Ke(()=>{document.body.style.pointerEvents="none",document.body.style.overflow="hidden"})},{immediate:!0,flush:"sync"}),e});function ac(e){const t=Math.random().toString(36).substring(2,7),n=$h();n.value.set(t,e??!1);const r=H({get:()=>n.value.get(t)??!1,set:s=>n.value.set(t,s)});return Oh(()=>{n.value.delete(t)}),r}function lc(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!(n instanceof Element)||n.tagName==="BODY"?!1:lc(n)}}function Bh(e){const t=e||window.event,n=t.target;return n instanceof Element&&lc(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.cancelable&&t.preventDefault(),!1)}function wi(e){const t=rc({dir:L("ltr")});return H(()=>e?.value||t.dir?.value||"ltr")}function jr(e){const t=ut(),n=t?.type.emits,r={};return n?.length||console.warn(`No emitted event found. Please check component: ${t?.type.__name}`),n?.forEach(s=>{r[pr(lt(s))]=(...o)=>e(s,...o)}),r}let fo=0;function qh(){st(e=>{if(!At)return;const t=document.querySelectorAll("[data-reka-focus-guard]");document.body.insertAdjacentElement("afterbegin",t[0]??Oa()),document.body.insertAdjacentElement("beforeend",t[1]??Oa()),fo++,e(()=>{fo===1&&document.querySelectorAll("[data-reka-focus-guard]").forEach(n=>n.remove()),fo--})})}function Oa(){const e=document.createElement("span");return e.setAttribute("data-reka-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}function _e(){const e=ut(),t=L(),n=H(()=>["#text","#comment"].includes(t.value?.$el.nodeName)?t.value?.$el.nextElementSibling:tr(t)),r=Object.assign({},e.exposed),s={};for(const i in e.props)Object.defineProperty(s,i,{enumerable:!0,configurable:!0,get:()=>e.props[i]});if(Object.keys(r).length>0)for(const i in r)Object.defineProperty(s,i,{enumerable:!0,configurable:!0,get:()=>r[i]});Object.defineProperty(s,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=s;function o(i){t.value=i,i&&(Object.defineProperty(s,"$el",{enumerable:!0,configurable:!0,get:()=>i instanceof Element?i:i.$el}),e.exposed=s)}return{forwardRef:o,currentRef:t,currentElement:n}}function zr(e){const t=ut(),n=Object.keys(t?.type.props??{}).reduce((s,o)=>{const i=(t?.type.props[o]).default;return i!==void 0&&(s[o]=i),s},{}),r=Ud(e);return H(()=>{const s={},o=t?.vnode.props??{};return Object.keys(o).forEach(i=>{s[lt(i)]=o[i]}),Object.keys({...n,...s}).reduce((i,a)=>(r.value[a]!==void 0&&(i[a]=r.value[a]),i),{})})}function Ot(e,t){const n=zr(e),r=t?jr(t):{};return H(()=>({...n.value,...r}))}function Lh(e,t){const n=oc(!1,300),r=L(null),s=_h();function o(){r.value=null,n.value=!1}function i(a,l){const c=a.currentTarget,u={x:a.clientX,y:a.clientY},d=Nh(u,c.getBoundingClientRect()),p=Fh(u,d),h=jh(l.getBoundingClientRect()),m=Vh([...p,...h]);r.value=m,n.value=!0}return st(a=>{if(e.value&&t.value){const l=u=>i(u,t.value),c=u=>i(u,e.value);e.value.addEventListener("pointerleave",l),t.value.addEventListener("pointerleave",c),a(()=>{e.value?.removeEventListener("pointerleave",l),t.value?.removeEventListener("pointerleave",c)})}}),st(a=>{if(r.value){const l=c=>{if(!r.value||!(c.target instanceof HTMLElement))return;const u=c.target,d={x:c.clientX,y:c.clientY},p=e.value?.contains(u)||t.value?.contains(u),h=!zh(d,r.value),m=!!u.closest("[data-grace-area-trigger]");p?o():(h||m)&&(o(),s.trigger())};e.value?.ownerDocument.addEventListener("pointermove",l),a(()=>e.value?.ownerDocument.removeEventListener("pointermove",l))}}),{isPointerInTransit:n,onPointerExit:s.on}}function Nh(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,s,o)){case o:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Fh(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function jh(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function zh(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o].x,l=t[o].y,c=t[i].x,u=t[i].y;l>r!=u>r&&n<(c-a)*(r-l)/(u-l)+a&&(s=!s)}return s}function Vh(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Hh(t)}function Hh(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Uh=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Dn=new WeakMap,Jr=new WeakMap,Xr={},po=0,uc=function(e){return e&&(e.host||uc(e.parentNode))},Wh=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=uc(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Kh=function(e,t,n,r){var s=Wh(t,Array.isArray(e)?e:[e]);Xr[n]||(Xr[n]=new WeakMap);var o=Xr[n],i=[],a=new Set,l=new Set(s),c=function(d){!d||a.has(d)||(a.add(d),c(d.parentNode))};s.forEach(c);var u=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(p){if(a.has(p))u(p);else try{var h=p.getAttribute(r),m=h!==null&&h!=="false",y=(Dn.get(p)||0)+1,_=(o.get(p)||0)+1;Dn.set(p,y),o.set(p,_),i.push(p),y===1&&m&&Jr.set(p,!0),_===1&&p.setAttribute(n,"true"),m||p.setAttribute(r,"true")}catch(C){console.error("aria-hidden: cannot operate on ",p,C)}})};return u(t),a.clear(),po++,function(){i.forEach(function(d){var p=Dn.get(d)-1,h=o.get(d)-1;Dn.set(d,p),o.set(d,h),p||(Jr.has(d)||d.removeAttribute(r),Jr.delete(d)),h||d.removeAttribute(n)}),po--,po||(Dn=new WeakMap,Dn=new WeakMap,Jr=new WeakMap,Xr={})}},Gh=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=Uh(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),Kh(r,s,n,"aria-hidden")):function(){return null}};function cc(e){let t;qe(()=>tr(e),n=>{n?t=Gh(n):t&&t()}),On(()=>{t&&t()})}function Rr(e,t="reka"){return`${t}-${nf?.()}`}function Yh(e){const t=L(),n=H(()=>t.value?.width??0),r=H(()=>t.value?.height??0);return ht(()=>{const s=tr(e);if(s){t.value={width:s.offsetWidth,height:s.offsetHeight};const o=new ResizeObserver(i=>{if(!Array.isArray(i)||!i.length)return;const a=i[0];let l,c;if("borderBoxSize"in a){const u=a.borderBoxSize,d=Array.isArray(u)?u[0]:u;l=d.inlineSize,c=d.blockSize}else l=s.offsetWidth,c=s.offsetHeight;t.value={width:l,height:c}});return o.observe(s,{box:"border-box"}),()=>o.unobserve(s)}else t.value=void 0}),{width:n,height:r}}function Jh(e,t){const n=L(e);function r(o){return t[n.value][o]??n.value}return{state:n,dispatch:o=>{n.value=r(o)}}}function Xh(e){const t=oc("",1e3);return{search:t,handleTypeaheadSearch:(s,o)=>{t.value=t.value+s;{const i=at(),a=o.map(p=>({...p,textValue:p.value?.textValue??p.ref.textContent?.trim()??""})),l=a.find(p=>p.ref===i),c=a.map(p=>p.textValue),u=Zh(c,t.value,l?.textValue),d=a.find(p=>p.textValue===u);return d&&d.ref.focus(),d?.ref}},resetTypeahead:()=>{t.value=""}}}function Qh(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Zh(e,t,n){const s=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let i=Qh(e,Math.max(o,0));s.length===1&&(i=i.filter(c=>c!==n));const l=i.find(c=>c.toLowerCase().startsWith(s.toLowerCase()));return l!==n?l:void 0}function em(e,t){const n=L({}),r=L("none"),s=L(e),o=e.value?"mounted":"unmounted";let i;const a=t.value?.ownerDocument.defaultView??_i,{state:l,dispatch:c}=Jh(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),u=_=>{if(At){const C=new CustomEvent(_,{bubbles:!1,cancelable:!1});t.value?.dispatchEvent(C)}};qe(e,async(_,C)=>{const O=C!==_;if(await Ke(),O){const b=r.value,x=Qr(t.value);_?(c("MOUNT"),u("enter"),x==="none"&&u("after-enter")):x==="none"||x==="undefined"||n.value?.display==="none"?(c("UNMOUNT"),u("leave"),u("after-leave")):C&&b!==x?(c("ANIMATION_OUT"),u("leave")):(c("UNMOUNT"),u("after-leave"))}},{immediate:!0});const d=_=>{const C=Qr(t.value),O=C.includes(_.animationName),b=l.value==="mounted"?"enter":"leave";if(_.target===t.value&&O&&(u(`after-${b}`),c("ANIMATION_END"),!s.value)){const x=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",i=a?.setTimeout(()=>{t.value?.style.animationFillMode==="forwards"&&(t.value.style.animationFillMode=x)})}_.target===t.value&&C==="none"&&c("ANIMATION_END")},p=_=>{_.target===t.value&&(r.value=Qr(t.value))},h=qe(t,(_,C)=>{_?(n.value=getComputedStyle(_),_.addEventListener("animationstart",p),_.addEventListener("animationcancel",d),_.addEventListener("animationend",d)):(c("ANIMATION_END"),i!==void 0&&a?.clearTimeout(i),C?.removeEventListener("animationstart",p),C?.removeEventListener("animationcancel",d),C?.removeEventListener("animationend",d))},{immediate:!0}),m=qe(l,()=>{const _=Qr(t.value);r.value=l.value==="mounted"?_:"none"});return On(()=>{h(),m()}),{isPresent:H(()=>["mounted","unmountSuspended"].includes(l.value))}}function Qr(e){return e&&getComputedStyle(e).animationName||"none"}var js=I({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){const{present:r,forceMount:s}=wt(e),o=L(),{isPresent:i}=em(r,o);n({present:i});let a=t.default({present:i.value});a=bi(a||[]);const l=ut();if(a&&a?.length>1){const c=l?.parent?.type.name?`<${l.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${c}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map(u=>`  - ${u}`).join(`
`)].join(`
`))}return()=>s.value||r.value||i.value?_t(t.default({present:i.value})[0],{ref:c=>{const u=tr(c);return typeof u?.hasAttribute>"u"||(u?.hasAttribute("data-reka-popper-content-wrapper")?o.value=u.firstElementChild:o.value=u),u}}):null}});const Vo=I({name:"PrimitiveSlot",inheritAttrs:!1,setup(e,{attrs:t,slots:n}){return()=>{if(!n.default)return null;const r=bi(n.default()),s=r.findIndex(l=>l.type!==xt);if(s===-1)return r;const o=r[s];delete o.props?.ref;const i=o.props?ue(t,o.props):t,a=En({...o,props:{}},i);return r.length===1?a:(r[s]=a,r)}}}),tm=["area","img","input"],Ie=I({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:n}){const r=e.asChild?"template":e.as;return typeof r=="string"&&tm.includes(r)?()=>_t(r,t):r!=="template"?()=>_t(e.as,t,{default:n.default}):()=>_t(Vo,t,{default:n.default})}});function ka(){const e=L(),t=H(()=>["#text","#comment"].includes(e.value?.$el.nodeName)?e.value?.$el.nextElementSibling:tr(e));return{primitiveElement:e,currentElement:t}}const[nn,nm]=gt("DialogRoot");var rm=I({inheritAttrs:!1,__name:"DialogRoot",props:{open:{type:Boolean,required:!1,default:void 0},defaultOpen:{type:Boolean,required:!1,default:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,s=Fr(n,"open",t,{defaultValue:n.defaultOpen,passive:n.open===void 0}),o=L(),i=L(),{modal:a}=wt(n);return nm({open:s,modal:a,openModal:()=>{s.value=!0},onOpenChange:l=>{s.value=l},onOpenToggle:()=>{s.value=!s.value},contentId:"",titleId:"",descriptionId:"",triggerElement:o,contentElement:i}),(l,c)=>q(l.$slots,"default",{open:f(s),close:()=>s.value=!1})}}),sm=rm,om=I({__name:"DialogClose",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e;_e();const n=nn();return(r,s)=>(T(),z(f(Ie),ue(t,{type:r.as==="button"?"button":void 0,onClick:s[0]||(s[0]=o=>f(n).onOpenChange(!1))}),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["type"]))}}),im=om;const am="dismissableLayer.pointerDownOutside",lm="dismissableLayer.focusOutside";function dc(e,t){const n=t.closest("[data-dismissable-layer]"),r=e.dataset.dismissableLayer===""?e:e.querySelector("[data-dismissable-layer]"),s=Array.from(e.ownerDocument.querySelectorAll("[data-dismissable-layer]"));return!!(n&&(r===n||s.indexOf(r)<s.indexOf(n)))}function um(e,t,n=!0){const r=t?.value?.ownerDocument??globalThis?.document,s=L(!1),o=L(()=>{});return st(i=>{if(!At||!Fe(n))return;const a=async c=>{const u=c.target;if(!(!t?.value||!u)){if(dc(t.value,u)){s.value=!1;return}if(c.target&&!s.value){let p=function(){tc(am,e,d)};const d={originalEvent:c};c.pointerType==="touch"?(r.removeEventListener("click",o.value),o.value=p,r.addEventListener("click",o.value,{once:!0})):p()}else r.removeEventListener("click",o.value);s.value=!1}},l=window.setTimeout(()=>{r.addEventListener("pointerdown",a)},0);i(()=>{window.clearTimeout(l),r.removeEventListener("pointerdown",a),r.removeEventListener("click",o.value)})}),{onPointerDownCapture:()=>{Fe(n)&&(s.value=!0)}}}function cm(e,t,n=!0){const r=t?.value?.ownerDocument??globalThis?.document,s=L(!1);return st(o=>{if(!At||!Fe(n))return;const i=async a=>{if(!t?.value)return;await Ke(),await Ke();const l=a.target;!t.value||!l||dc(t.value,l)||a.target&&!s.value&&tc(lm,e,{originalEvent:a})};r.addEventListener("focusin",i),o(()=>r.removeEventListener("focusin",i))}),{onFocusCapture:()=>{Fe(n)&&(s.value=!0)},onBlurCapture:()=>{Fe(n)&&(s.value=!1)}}}const Vt=Xt({layersRoot:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set});var dm=I({__name:"DismissableLayer",props:{disableOutsidePointerEvents:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","dismiss"],setup(e,{emit:t}){const n=e,r=t,{forwardRef:s,currentElement:o}=_e(),i=H(()=>o.value?.ownerDocument??globalThis.document),a=H(()=>Vt.layersRoot),l=H(()=>o.value?Array.from(a.value).indexOf(o.value):-1),c=H(()=>Vt.layersWithOutsidePointerEventsDisabled.size>0),u=H(()=>{const m=Array.from(a.value),[y]=[...Vt.layersWithOutsidePointerEventsDisabled].slice(-1),_=m.indexOf(y);return l.value>=_}),d=um(async m=>{const y=[...Vt.branches].some(_=>_?.contains(m.target));!u.value||y||(r("pointerDownOutside",m),r("interactOutside",m),await Ke(),m.defaultPrevented||r("dismiss"))},o),p=cm(m=>{[...Vt.branches].some(_=>_?.contains(m.target))||(r("focusOutside",m),r("interactOutside",m),m.defaultPrevented||r("dismiss"))},o);Th("Escape",m=>{l.value===a.value.size-1&&(r("escapeKeyDown",m),m.defaultPrevented||r("dismiss"))});let h;return st(m=>{o.value&&(n.disableOutsidePointerEvents&&(Vt.layersWithOutsidePointerEventsDisabled.size===0&&(h=i.value.body.style.pointerEvents,i.value.body.style.pointerEvents="none"),Vt.layersWithOutsidePointerEventsDisabled.add(o.value)),a.value.add(o.value),m(()=>{n.disableOutsidePointerEvents&&Vt.layersWithOutsidePointerEventsDisabled.size===1&&(i.value.body.style.pointerEvents=h)}))}),st(m=>{m(()=>{o.value&&(a.value.delete(o.value),Vt.layersWithOutsidePointerEventsDisabled.delete(o.value))})}),(m,y)=>(T(),z(f(Ie),{ref:f(s),"as-child":m.asChild,as:m.as,"data-dismissable-layer":"",style:vn({pointerEvents:c.value?u.value?"auto":"none":void 0}),onFocusCapture:f(p).onFocusCapture,onBlurCapture:f(p).onBlurCapture,onPointerdownCapture:f(d).onPointerDownCapture},{default:w(()=>[q(m.$slots,"default")]),_:3},8,["as-child","as","style","onFocusCapture","onBlurCapture","onPointerdownCapture"]))}}),xi=dm;const fm=wh(()=>L([]));function pm(){const e=fm();return{add(t){const n=e.value[0];t!==n&&n?.pause(),e.value=Pa(e.value,t),e.value.unshift(t)},remove(t){e.value=Pa(e.value,t),e.value[0]?.resume()}}}function Pa(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function hm(e){return e.filter(t=>t.tagName!=="A")}const ho="focusScope.autoFocusOnMount",mo="focusScope.autoFocusOnUnmount",Ra={bubbles:!1,cancelable:!0};function mm(e,{select:t=!1}={}){const n=at();for(const r of e)if(an(r,{select:t}),at()!==n)return!0}function gm(e){const t=fc(e),n=Ta(t,e),r=Ta(t.reverse(),e);return[n,r]}function fc(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ta(e,t){for(const n of e)if(!vm(n,{upTo:t}))return n}function vm(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ym(e){return e instanceof HTMLInputElement&&"select"in e}function an(e,{select:t=!1}={}){if(e&&e.focus){const n=at();e.focus({preventScroll:!0}),e!==n&&ym(e)&&t&&e.select()}}var bm=I({__name:"FocusScope",props:{loop:{type:Boolean,required:!1,default:!1},trapped:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["mountAutoFocus","unmountAutoFocus"],setup(e,{emit:t}){const n=e,r=t,{currentRef:s,currentElement:o}=_e(),i=L(null),a=pm(),l=Xt({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}});st(u=>{if(!At)return;const d=o.value;if(!n.trapped)return;function p(_){if(l.paused||!d)return;const C=_.target;d.contains(C)?i.value=C:an(i.value,{select:!0})}function h(_){if(l.paused||!d)return;const C=_.relatedTarget;C!==null&&(d.contains(C)||an(i.value,{select:!0}))}function m(_){d.contains(i.value)||an(d)}document.addEventListener("focusin",p),document.addEventListener("focusout",h);const y=new MutationObserver(m);d&&y.observe(d,{childList:!0,subtree:!0}),u(()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",h),y.disconnect()})}),st(async u=>{const d=o.value;if(await Ke(),!d)return;a.add(l);const p=at();if(!d.contains(p)){const m=new CustomEvent(ho,Ra);d.addEventListener(ho,y=>r("mountAutoFocus",y)),d.dispatchEvent(m),m.defaultPrevented||(mm(hm(fc(d)),{select:!0}),at()===p&&an(d))}u(()=>{d.removeEventListener(ho,_=>r("mountAutoFocus",_));const m=new CustomEvent(mo,Ra),y=_=>{r("unmountAutoFocus",_)};d.addEventListener(mo,y),d.dispatchEvent(m),setTimeout(()=>{m.defaultPrevented||an(p??document.body,{select:!0}),d.removeEventListener(mo,y),a.remove(l)},0)})});function c(u){if(!n.loop&&!n.trapped||l.paused)return;const d=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,p=at();if(d&&p){const h=u.currentTarget,[m,y]=gm(h);m&&y?!u.shiftKey&&p===y?(u.preventDefault(),n.loop&&an(m,{select:!0})):u.shiftKey&&p===m&&(u.preventDefault(),n.loop&&an(y,{select:!0})):p===h&&u.preventDefault()}}return(u,d)=>(T(),z(f(Ie),{ref_key:"currentRef",ref:s,tabindex:"-1","as-child":u.asChild,as:u.as,onKeydown:c},{default:w(()=>[q(u.$slots,"default")]),_:3},8,["as-child","as"]))}}),pc=bm;const _m="menu.itemSelect",Ho=["Enter"," "],wm=["ArrowDown","PageUp","Home"],hc=["ArrowUp","PageDown","End"],xm=[...wm,...hc];[...Ho],[...Ho];function mc(e){return e?"open":"closed"}function Cm(e){const t=at();for(const n of e)if(n===t||(n.focus(),at()!==t))return}function Sm(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o].x,l=t[o].y,c=t[i].x,u=t[i].y;l>r!=u>r&&n<(c-a)*(r-l)/(u-l)+a&&(s=!s)}return s}function Em(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Sm(n,t)}function Uo(e){return e.pointerType==="mouse"}var Am=I({__name:"DialogContentImpl",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=nn(),{forwardRef:o,currentElement:i}=_e();return s.titleId||=Rr(void 0,"reka-dialog-title"),s.descriptionId||=Rr(void 0,"reka-dialog-description"),ht(()=>{s.contentElement=i,at()!==document.body&&(s.triggerElement.value=at())}),(a,l)=>(T(),z(f(pc),{"as-child":"",loop:"",trapped:n.trapFocus,onMountAutoFocus:l[5]||(l[5]=c=>r("openAutoFocus",c)),onUnmountAutoFocus:l[6]||(l[6]=c=>r("closeAutoFocus",c))},{default:w(()=>[E(f(xi),ue({id:f(s).contentId,ref:f(o),as:a.as,"as-child":a.asChild,"disable-outside-pointer-events":a.disableOutsidePointerEvents,role:"dialog","aria-describedby":f(s).descriptionId,"aria-labelledby":f(s).titleId,"data-state":f(mc)(f(s).open.value)},a.$attrs,{onDismiss:l[0]||(l[0]=c=>f(s).onOpenChange(!1)),onEscapeKeyDown:l[1]||(l[1]=c=>r("escapeKeyDown",c)),onFocusOutside:l[2]||(l[2]=c=>r("focusOutside",c)),onInteractOutside:l[3]||(l[3]=c=>r("interactOutside",c)),onPointerDownOutside:l[4]||(l[4]=c=>r("pointerDownOutside",c))}),{default:w(()=>[q(a.$slots,"default")]),_:3},16,["id","as","as-child","disable-outside-pointer-events","aria-describedby","aria-labelledby","data-state"])]),_:3},8,["trapped"]))}}),gc=Am,Om=I({__name:"DialogContentModal",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=nn(),o=jr(r),{forwardRef:i,currentElement:a}=_e();return cc(a),(l,c)=>(T(),z(gc,ue({...n,...f(o)},{ref:f(i),"trap-focus":f(s).open.value,"disable-outside-pointer-events":!0,onCloseAutoFocus:c[0]||(c[0]=u=>{u.defaultPrevented||(u.preventDefault(),f(s).triggerElement.value?.focus())}),onPointerDownOutside:c[1]||(c[1]=u=>{const d=u.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0;(d.button===2||p)&&u.preventDefault()}),onFocusOutside:c[2]||(c[2]=u=>{u.preventDefault()})}),{default:w(()=>[q(l.$slots,"default")]),_:3},16,["trap-focus"]))}}),km=Om,Pm=I({__name:"DialogContentNonModal",props:{forceMount:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,s=jr(t);_e();const o=nn(),i=L(!1),a=L(!1);return(l,c)=>(T(),z(gc,ue({...n,...f(s)},{"trap-focus":!1,"disable-outside-pointer-events":!1,onCloseAutoFocus:c[0]||(c[0]=u=>{u.defaultPrevented||(i.value||f(o).triggerElement.value?.focus(),u.preventDefault()),i.value=!1,a.value=!1}),onInteractOutside:c[1]||(c[1]=u=>{u.defaultPrevented||(i.value=!0,u.detail.originalEvent.type==="pointerdown"&&(a.value=!0));const d=u.target;f(o).triggerElement.value?.contains(d)&&u.preventDefault(),u.detail.originalEvent.type==="focusin"&&a.value&&u.preventDefault()})}),{default:w(()=>[q(l.$slots,"default")]),_:3},16))}}),Rm=Pm,Tm=I({__name:"DialogContent",props:{forceMount:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=nn(),o=jr(r),{forwardRef:i}=_e();return(a,l)=>(T(),z(f(js),{present:a.forceMount||f(s).open.value},{default:w(()=>[f(s).modal.value?(T(),z(km,ue({key:0,ref:f(i)},{...n,...f(o),...a.$attrs}),{default:w(()=>[q(a.$slots,"default")]),_:3},16)):(T(),z(Rm,ue({key:1,ref:f(i)},{...n,...f(o),...a.$attrs}),{default:w(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Mm=Tm,Dm=I({__name:"DialogDescription",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"p"}},setup(e){const t=e;_e();const n=nn();return(r,s)=>(T(),z(f(Ie),ue(t,{id:f(n).descriptionId}),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["id"]))}}),Im=Dm,$m=I({__name:"DialogOverlayImpl",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=nn();return ac(!0),_e(),(n,r)=>(T(),z(f(Ie),{as:n.as,"as-child":n.asChild,"data-state":f(t).open.value?"open":"closed",style:{"pointer-events":"auto"}},{default:w(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","data-state"]))}}),Bm=$m,qm=I({__name:"DialogOverlay",props:{forceMount:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=nn(),{forwardRef:n}=_e();return(r,s)=>f(t)?.modal.value?(T(),z(f(js),{key:0,present:r.forceMount||f(t).open.value},{default:w(()=>[E(Bm,ue(r.$attrs,{ref:f(n),as:r.as,"as-child":r.asChild}),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["as","as-child"])]),_:3},8,["present"])):qr("v-if",!0)}}),Lm=qm,Nm=I({__name:"Teleport",props:{to:{type:null,required:!1,default:"body"},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=Ph();return(n,r)=>f(t)||n.forceMount?(T(),z(tf,{key:0,to:n.to,disabled:n.disabled,defer:n.defer},[q(n.$slots,"default")],8,["to","disabled","defer"])):qr("v-if",!0)}}),Ci=Nm,Fm=I({__name:"DialogPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ci),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),jm=Fm,zm=I({__name:"DialogTitle",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"h2"}},setup(e){const t=e,n=nn();return _e(),(r,s)=>(T(),z(f(Ie),ue(t,{id:f(n).titleId}),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["id"]))}}),Vm=zm;const[vc,Hm]=gt("AvatarRoot");var Um=I({__name:"AvatarRoot",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){return _e(),Hm({imageLoadingStatus:L("idle")}),(t,n)=>(T(),z(f(Ie),{"as-child":t.asChild,as:t.as},{default:w(()=>[q(t.$slots,"default")]),_:3},8,["as-child","as"]))}}),Wm=Um,Km=I({__name:"AvatarFallback",props:{delayMs:{type:Number,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){const t=e,n=vc();_e();const r=L(t.delayMs===void 0);return st(s=>{if(t.delayMs&&At){const o=window.setTimeout(()=>{r.value=!0},t.delayMs);s(()=>{window.clearTimeout(o)})}}),(s,o)=>r.value&&f(n).imageLoadingStatus.value!=="loaded"?(T(),z(f(Ie),{key:0,"as-child":s.asChild,as:s.as},{default:w(()=>[q(s.$slots,"default")]),_:3},8,["as-child","as"])):qr("v-if",!0)}}),Gm=Km;function Ma(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Ym(e,{referrerPolicy:t,crossOrigin:n}={}){const r=L(!1),s=L(null),o=H(()=>r.value?(!s.value&&At&&(s.value=new window.Image),s.value):null),i=L(Ma(o.value,e.value)),a=l=>()=>{r.value&&(i.value=l)};return ht(()=>{r.value=!0,st(l=>{const c=o.value;if(!c)return;i.value=Ma(c,e.value);const u=a("loaded"),d=a("error");c.addEventListener("load",u),c.addEventListener("error",d),t?.value&&(c.referrerPolicy=t.value),typeof n?.value=="string"&&(c.crossOrigin=n.value),l(()=>{c.removeEventListener("load",u),c.removeEventListener("error",d)})})}),On(()=>{r.value=!1}),i}var Jm=I({__name:"AvatarImage",props:{src:{type:String,required:!0},referrerPolicy:{type:null,required:!1},crossOrigin:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"img"}},emits:["loadingStatusChange"],setup(e,{emit:t}){const n=e,r=t,{src:s,referrerPolicy:o,crossOrigin:i}=wt(n);_e();const a=vc(),l=Ym(s,{referrerPolicy:o,crossOrigin:i});return qe(l,c=>{r("loadingStatusChange",c),c!=="idle"&&(a.imageLoadingStatus.value=c)},{immediate:!0}),(c,u)=>tu((T(),z(f(Ie),{role:"img","as-child":c.asChild,as:c.as,src:f(s),"referrer-policy":f(o)},{default:w(()=>[q(c.$slots,"default")]),_:3},8,["as-child","as","src","referrer-policy"])),[[ap,f(l)==="loaded"]])}}),Xm=Jm;const Da="data-reka-collection-item";function yc(e={}){const{key:t="",isProvider:n=!1}=e,r=`${t}CollectionProvider`;let s;if(n){const u=L(new Map);s={collectionRef:L(),itemMap:u},Wn(r,s)}else s=nt(r);const o=(u=!1)=>{const d=s.collectionRef.value;if(!d)return[];const p=Array.from(d.querySelectorAll(`[${Da}]`)),m=Array.from(s.itemMap.value.values()).sort((y,_)=>p.indexOf(y.ref)-p.indexOf(_.ref));return u?m:m.filter(y=>y.ref.dataset.disabled!=="")},i=I({name:"CollectionSlot",setup(u,{slots:d}){const{primitiveElement:p,currentElement:h}=ka();return qe(h,()=>{s.collectionRef.value=h.value}),()=>_t(Vo,{ref:p},d)}}),a=I({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(u,{slots:d,attrs:p}){const{primitiveElement:h,currentElement:m}=ka();return st(y=>{if(m.value){const _=Ds(m.value);s.itemMap.value.set(_,{ref:m.value,value:u.value}),y(()=>s.itemMap.value.delete(_))}}),()=>_t(Vo,{...p,[Da]:"",ref:h},d)}}),l=H(()=>Array.from(s.itemMap.value.values())),c=H(()=>s.itemMap.value.size);return{getItems:o,reactiveItems:l,itemMapSize:c,CollectionSlot:i,CollectionItem:a}}const Qm="rovingFocusGroup.onEntryFocus",Zm={bubbles:!1,cancelable:!0};function eg(e,t=!1){const n=at();for(const r of e)if(r===n||(r.focus({preventScroll:t}),at()!==n))return}const[Jw,tg]=gt("RovingFocusGroup");var ng=I({__name:"RovingFocusGroup",props:{orientation:{type:String,required:!1,default:void 0},dir:{type:String,required:!1},loop:{type:Boolean,required:!1,default:!1},currentTabStopId:{type:[String,null],required:!1},defaultCurrentTabStopId:{type:String,required:!1},preventScrollOnEntryFocus:{type:Boolean,required:!1,default:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["entryFocus","update:currentTabStopId"],setup(e,{expose:t,emit:n}){const r=e,s=n,{loop:o,orientation:i,dir:a}=wt(r),l=wi(a),c=Fr(r,"currentTabStopId",s,{defaultValue:r.defaultCurrentTabStopId,passive:r.currentTabStopId===void 0}),u=L(!1),d=L(!1),p=L(0),{getItems:h,CollectionSlot:m}=yc({isProvider:!0});function y(C){const O=!d.value;if(C.currentTarget&&C.target===C.currentTarget&&O&&!u.value){const b=new CustomEvent(Qm,Zm);if(C.currentTarget.dispatchEvent(b),s("entryFocus",b),!b.defaultPrevented){const x=h().map(k=>k.ref).filter(k=>k.dataset.disabled!==""),R=x.find(k=>k.getAttribute("data-active")===""),N=x.find(k=>k.id===c.value),F=[R,N,...x].filter(Boolean);eg(F,r.preventScrollOnEntryFocus)}}d.value=!1}function _(){setTimeout(()=>{d.value=!1},1)}return t({getItems:h}),tg({loop:o,dir:l,orientation:i,currentTabStopId:c,onItemFocus:C=>{c.value=C},onItemShiftTab:()=>{u.value=!0},onFocusableItemAdd:()=>{p.value++},onFocusableItemRemove:()=>{p.value--}}),(C,O)=>(T(),z(f(m),null,{default:w(()=>[E(f(Ie),{tabindex:u.value||p.value===0?-1:0,"data-orientation":f(i),as:C.as,"as-child":C.asChild,dir:f(l),style:{outline:"none"},onMousedown:O[0]||(O[0]=b=>d.value=!0),onMouseup:_,onFocus:y,onBlur:O[1]||(O[1]=b=>u.value=!1)},{default:w(()=>[q(C.$slots,"default")]),_:3},8,["tabindex","data-orientation","as","as-child","dir"])]),_:3}))}}),rg=ng,sg=I({__name:"VisuallyHidden",props:{feature:{type:String,required:!1,default:"focusable"},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"span"}},setup(e){return(t,n)=>(T(),z(f(Ie),{as:t.as,"as-child":t.asChild,"aria-hidden":t.feature==="focusable"?"true":void 0,"data-hidden":t.feature==="fully-hidden"?"":void 0,tabindex:t.feature==="fully-hidden"?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:w(()=>[q(t.$slots,"default")]),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))}}),og=sg;const[bc,ig]=gt("PopperRoot");var ag=I({inheritAttrs:!1,__name:"PopperRoot",setup(e){const t=L();return ig({anchor:t,onAnchorChange:n=>t.value=n}),(n,r)=>q(n.$slots,"default")}}),_c=ag,lg=I({__name:"PopperAnchor",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,{forwardRef:n,currentElement:r}=_e(),s=bc();return Au(()=>{s.onAnchorChange(t.reference??r.value)}),(o,i)=>(T(),z(f(Ie),{ref:f(n),as:o.as,"as-child":o.asChild},{default:w(()=>[q(o.$slots,"default")]),_:3},8,["as","as-child"]))}}),wc=lg;const ug={key:0,d:"M0 0L6 6L12 0"},cg={key:1,d:"M0 0L4.58579 4.58579C5.36683 5.36683 6.63316 5.36684 7.41421 4.58579L12 0"};var dg=I({__name:"Arrow",props:{width:{type:Number,required:!1,default:10},height:{type:Number,required:!1,default:5},rounded:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const t=e;return _e(),(n,r)=>(T(),z(f(Ie),ue(t,{width:n.width,height:n.height,viewBox:n.asChild?void 0:"0 0 12 6",preserveAspectRatio:n.asChild?void 0:"none"}),{default:w(()=>[q(n.$slots,"default",{},()=>[n.rounded?(T(),be("path",cg)):(T(),be("path",ug))])]),_:3},16,["width","height","viewBox","preserveAspectRatio"]))}}),fg=dg;function pg(e){return e!==null}function hg(e){return{name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:s}=t,i=s.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,u]=Wo(n),d={start:"0%",center:"50%",end:"100%"}[u],p=(s.arrow?.x??0)+a/2,h=(s.arrow?.y??0)+l/2;let m="",y="";return c==="bottom"?(m=i?d:`${p}px`,y=`${-l}px`):c==="top"?(m=i?d:`${p}px`,y=`${r.floating.height+l}px`):c==="right"?(m=`${-l}px`,y=i?d:`${h}px`):c==="left"&&(m=`${r.floating.width+l}px`,y=i?d:`${h}px`),{data:{x:m,y}}}}}function Wo(e){const[t,n="center"]=e.split("-");return[t,n]}const mg=["top","right","bottom","left"],hn=Math.min,dt=Math.max,ys=Math.round,Zr=Math.floor,Bt=e=>({x:e,y:e}),gg={left:"right",right:"left",bottom:"top",top:"bottom"},vg={start:"end",end:"start"};function Ko(e,t,n){return dt(e,hn(t,n))}function Qt(e,t){return typeof e=="function"?e(t):e}function Zt(e){return e.split("-")[0]}function nr(e){return e.split("-")[1]}function Si(e){return e==="x"?"y":"x"}function Ei(e){return e==="y"?"height":"width"}const yg=new Set(["top","bottom"]);function It(e){return yg.has(Zt(e))?"y":"x"}function Ai(e){return Si(It(e))}function bg(e,t,n){n===void 0&&(n=!1);const r=nr(e),s=Ai(e),o=Ei(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=bs(i)),[i,bs(i)]}function _g(e){const t=bs(e);return[Go(e),t,Go(t)]}function Go(e){return e.replace(/start|end/g,t=>vg[t])}const Ia=["left","right"],$a=["right","left"],wg=["top","bottom"],xg=["bottom","top"];function Cg(e,t,n){switch(e){case"top":case"bottom":return n?t?$a:Ia:t?Ia:$a;case"left":case"right":return t?wg:xg;default:return[]}}function Sg(e,t,n,r){const s=nr(e);let o=Cg(Zt(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(Go)))),o}function bs(e){return e.replace(/left|right|bottom|top/g,t=>gg[t])}function Eg(e){return{top:0,right:0,bottom:0,left:0,...e}}function xc(e){return typeof e!="number"?Eg(e):{top:e,right:e,bottom:e,left:e}}function _s(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function Ba(e,t,n){let{reference:r,floating:s}=e;const o=It(t),i=Ai(t),a=Ei(i),l=Zt(t),c=o==="y",u=r.x+r.width/2-s.width/2,d=r.y+r.height/2-s.height/2,p=r[a]/2-s[a]/2;let h;switch(l){case"top":h={x:u,y:r.y-s.height};break;case"bottom":h={x:u,y:r.y+r.height};break;case"right":h={x:r.x+r.width,y:d};break;case"left":h={x:r.x-s.width,y:d};break;default:h={x:r.x,y:r.y}}switch(nr(t)){case"start":h[i]-=p*(n&&c?-1:1);break;case"end":h[i]+=p*(n&&c?-1:1);break}return h}const Ag=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:u,y:d}=Ba(c,r,l),p=r,h={},m=0;for(let y=0;y<a.length;y++){const{name:_,fn:C}=a[y],{x:O,y:b,data:x,reset:R}=await C({x:u,y:d,initialPlacement:r,placement:p,strategy:s,middlewareData:h,rects:c,platform:i,elements:{reference:e,floating:t}});u=O??u,d=b??d,h={...h,[_]:{...h[_],...x}},R&&m<=50&&(m++,typeof R=="object"&&(R.placement&&(p=R.placement),R.rects&&(c=R.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):R.rects),{x:u,y:d}=Ba(c,p,l)),y=-1)}return{x:u,y:d,placement:p,strategy:s,middlewareData:h}};async function Tr(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=Qt(t,e),m=xc(h),_=a[p?d==="floating"?"reference":"floating":d],C=_s(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(_)))==null||n?_:_.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),O=d==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,b=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),x=await(o.isElement==null?void 0:o.isElement(b))?await(o.getScale==null?void 0:o.getScale(b))||{x:1,y:1}:{x:1,y:1},R=_s(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:O,offsetParent:b,strategy:l}):O);return{top:(C.top-R.top+m.top)/x.y,bottom:(R.bottom-C.bottom+m.bottom)/x.y,left:(C.left-R.left+m.left)/x.x,right:(R.right-C.right+m.right)/x.x}}const Og=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:u=0}=Qt(e,t)||{};if(c==null)return{};const d=xc(u),p={x:n,y:r},h=Ai(s),m=Ei(h),y=await i.getDimensions(c),_=h==="y",C=_?"top":"left",O=_?"bottom":"right",b=_?"clientHeight":"clientWidth",x=o.reference[m]+o.reference[h]-p[h]-o.floating[m],R=p[h]-o.reference[h],N=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let F=N?N[b]:0;(!F||!await(i.isElement==null?void 0:i.isElement(N)))&&(F=a.floating[b]||o.floating[m]);const k=x/2-R/2,A=F/2-y[m]/2-1,Y=hn(d[C],A),se=hn(d[O],A),le=Y,ge=F-y[m]-se,ve=F/2-y[m]/2+k,xe=Ko(le,ve,ge),V=!l.arrow&&nr(s)!=null&&ve!==xe&&o.reference[m]/2-(ve<le?Y:se)-y[m]/2<0,U=V?ve<le?ve-le:ve-ge:0;return{[h]:p[h]+U,data:{[h]:xe,centerOffset:ve-xe-U,...V&&{alignmentOffset:U}},reset:V}}}),kg=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:y=!0,..._}=Qt(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const C=Zt(s),O=It(a),b=Zt(a)===a,x=await(l.isRTL==null?void 0:l.isRTL(c.floating)),R=p||(b||!y?[bs(a)]:_g(a)),N=m!=="none";!p&&N&&R.push(...Sg(a,y,m,x));const F=[a,...R],k=await Tr(t,_),A=[];let Y=((r=o.flip)==null?void 0:r.overflows)||[];if(u&&A.push(k[C]),d){const ve=bg(s,i,x);A.push(k[ve[0]],k[ve[1]])}if(Y=[...Y,{placement:s,overflows:A}],!A.every(ve=>ve<=0)){var se,le;const ve=(((se=o.flip)==null?void 0:se.index)||0)+1,xe=F[ve];if(xe&&(!(d==="alignment"?O!==It(xe):!1)||Y.every(B=>It(B.placement)===O?B.overflows[0]>0:!0)))return{data:{index:ve,overflows:Y},reset:{placement:xe}};let V=(le=Y.filter(U=>U.overflows[0]<=0).sort((U,B)=>U.overflows[1]-B.overflows[1])[0])==null?void 0:le.placement;if(!V)switch(h){case"bestFit":{var ge;const U=(ge=Y.filter(B=>{if(N){const Ne=It(B.placement);return Ne===O||Ne==="y"}return!0}).map(B=>[B.placement,B.overflows.filter(Ne=>Ne>0).reduce((Ne,X)=>Ne+X,0)]).sort((B,Ne)=>B[1]-Ne[1])[0])==null?void 0:ge[0];U&&(V=U);break}case"initialPlacement":V=a;break}if(s!==V)return{reset:{placement:V}}}return{}}}};function qa(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function La(e){return mg.some(t=>e[t]>=0)}const Pg=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Qt(e,t);switch(r){case"referenceHidden":{const o=await Tr(t,{...s,elementContext:"reference"}),i=qa(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:La(i)}}}case"escaped":{const o=await Tr(t,{...s,altBoundary:!0}),i=qa(o,n.floating);return{data:{escapedOffsets:i,escaped:La(i)}}}default:return{}}}}},Cc=new Set(["left","top"]);async function Rg(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=Zt(n),a=nr(n),l=It(n)==="y",c=Cc.has(i)?-1:1,u=o&&l?-1:1,d=Qt(t,e);let{mainAxis:p,crossAxis:h,alignmentAxis:m}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&typeof m=="number"&&(h=a==="end"?m*-1:m),l?{x:h*u,y:p*c}:{x:p*c,y:h*u}}const Tg=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,l=await Rg(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+l.x,y:o+l.y,data:{...l,placement:i}}}}},Mg=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:_=>{let{x:C,y:O}=_;return{x:C,y:O}}},...l}=Qt(e,t),c={x:n,y:r},u=await Tr(t,l),d=It(Zt(s)),p=Si(d);let h=c[p],m=c[d];if(o){const _=p==="y"?"top":"left",C=p==="y"?"bottom":"right",O=h+u[_],b=h-u[C];h=Ko(O,h,b)}if(i){const _=d==="y"?"top":"left",C=d==="y"?"bottom":"right",O=m+u[_],b=m-u[C];m=Ko(O,m,b)}const y=a.fn({...t,[p]:h,[d]:m});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[p]:o,[d]:i}}}}}},Dg=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:o,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=Qt(e,t),u={x:n,y:r},d=It(s),p=Si(d);let h=u[p],m=u[d];const y=Qt(a,t),_=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(l){const b=p==="y"?"height":"width",x=o.reference[p]-o.floating[b]+_.mainAxis,R=o.reference[p]+o.reference[b]-_.mainAxis;h<x?h=x:h>R&&(h=R)}if(c){var C,O;const b=p==="y"?"width":"height",x=Cc.has(Zt(s)),R=o.reference[d]-o.floating[b]+(x&&((C=i.offset)==null?void 0:C[d])||0)+(x?0:_.crossAxis),N=o.reference[d]+o.reference[b]+(x?0:((O=i.offset)==null?void 0:O[d])||0)-(x?_.crossAxis:0);m<R?m=R:m>N&&(m=N)}return{[p]:h,[d]:m}}}},Ig=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:o,platform:i,elements:a}=t,{apply:l=()=>{},...c}=Qt(e,t),u=await Tr(t,c),d=Zt(s),p=nr(s),h=It(s)==="y",{width:m,height:y}=o.floating;let _,C;d==="top"||d==="bottom"?(_=d,C=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(C=d,_=p==="end"?"top":"bottom");const O=y-u.top-u.bottom,b=m-u.left-u.right,x=hn(y-u[_],O),R=hn(m-u[C],b),N=!t.middlewareData.shift;let F=x,k=R;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(k=b),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(F=O),N&&!p){const Y=dt(u.left,0),se=dt(u.right,0),le=dt(u.top,0),ge=dt(u.bottom,0);h?k=m-2*(Y!==0||se!==0?Y+se:dt(u.left,u.right)):F=y-2*(le!==0||ge!==0?le+ge:dt(u.top,u.bottom))}await l({...t,availableWidth:k,availableHeight:F});const A=await i.getDimensions(a.floating);return m!==A.width||y!==A.height?{reset:{rects:!0}}:{}}}};function zs(){return typeof window<"u"}function kn(e){return Oi(e)?(e.nodeName||"").toLowerCase():"#document"}function pt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Nt(e){var t;return(t=(Oi(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Oi(e){return zs()?e instanceof Node||e instanceof pt(e).Node:!1}function Ct(e){return zs()?e instanceof Element||e instanceof pt(e).Element:!1}function Lt(e){return zs()?e instanceof HTMLElement||e instanceof pt(e).HTMLElement:!1}function Na(e){return!zs()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof pt(e).ShadowRoot}const $g=new Set(["inline","contents"]);function Vr(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=St(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!$g.has(s)}const Bg=new Set(["table","td","th"]);function qg(e){return Bg.has(kn(e))}const Lg=[":popover-open",":modal"];function Vs(e){return Lg.some(t=>{try{return e.matches(t)}catch{return!1}})}const Ng=["transform","translate","scale","rotate","perspective"],Fg=["transform","translate","scale","rotate","perspective","filter"],jg=["paint","layout","strict","content"];function ki(e){const t=Pi(),n=Ct(e)?St(e):e;return Ng.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Fg.some(r=>(n.willChange||"").includes(r))||jg.some(r=>(n.contain||"").includes(r))}function zg(e){let t=mn(e);for(;Lt(t)&&!Yn(t);){if(ki(t))return t;if(Vs(t))return null;t=mn(t)}return null}function Pi(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Vg=new Set(["html","body","#document"]);function Yn(e){return Vg.has(kn(e))}function St(e){return pt(e).getComputedStyle(e)}function Hs(e){return Ct(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function mn(e){if(kn(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Na(e)&&e.host||Nt(e);return Na(t)?t.host:t}function Sc(e){const t=mn(e);return Yn(t)?e.ownerDocument?e.ownerDocument.body:e.body:Lt(t)&&Vr(t)?t:Sc(t)}function Mr(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=Sc(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=pt(s);if(o){const a=Yo(i);return t.concat(i,i.visualViewport||[],Vr(s)?s:[],a&&n?Mr(a):[])}return t.concat(s,Mr(s,[],n))}function Yo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ec(e){const t=St(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Lt(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=ys(n)!==o||ys(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function Ri(e){return Ct(e)?e:e.contextElement}function Kn(e){const t=Ri(e);if(!Lt(t))return Bt(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=Ec(t);let i=(o?ys(n.width):n.width)/r,a=(o?ys(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Hg=Bt(0);function Ac(e){const t=pt(e);return!Pi()||!t.visualViewport?Hg:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ug(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==pt(e)?!1:t}function An(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=Ri(e);let i=Bt(1);t&&(r?Ct(r)&&(i=Kn(r)):i=Kn(e));const a=Ug(o,n,r)?Ac(o):Bt(0);let l=(s.left+a.x)/i.x,c=(s.top+a.y)/i.y,u=s.width/i.x,d=s.height/i.y;if(o){const p=pt(o),h=r&&Ct(r)?pt(r):r;let m=p,y=Yo(m);for(;y&&r&&h!==m;){const _=Kn(y),C=y.getBoundingClientRect(),O=St(y),b=C.left+(y.clientLeft+parseFloat(O.paddingLeft))*_.x,x=C.top+(y.clientTop+parseFloat(O.paddingTop))*_.y;l*=_.x,c*=_.y,u*=_.x,d*=_.y,l+=b,c+=x,m=pt(y),y=Yo(m)}}return _s({width:u,height:d,x:l,y:c})}function Ti(e,t){const n=Hs(e).scrollLeft;return t?t.left+n:An(Nt(e)).left+n}function Oc(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:Ti(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function Wg(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=Nt(r),a=t?Vs(t.floating):!1;if(r===i||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=Bt(1);const u=Bt(0),d=Lt(r);if((d||!d&&!o)&&((kn(r)!=="body"||Vr(i))&&(l=Hs(r)),Lt(r))){const h=An(r);c=Kn(r),u.x=h.x+r.clientLeft,u.y=h.y+r.clientTop}const p=i&&!d&&!o?Oc(i,l,!0):Bt(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+p.x,y:n.y*c.y-l.scrollTop*c.y+u.y+p.y}}function Kg(e){return Array.from(e.getClientRects())}function Gg(e){const t=Nt(e),n=Hs(e),r=e.ownerDocument.body,s=dt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=dt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Ti(e);const a=-n.scrollTop;return St(r).direction==="rtl"&&(i+=dt(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function Yg(e,t){const n=pt(e),r=Nt(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,l=0;if(s){o=s.width,i=s.height;const c=Pi();(!c||c&&t==="fixed")&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:i,x:a,y:l}}const Jg=new Set(["absolute","fixed"]);function Xg(e,t){const n=An(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=Lt(e)?Kn(e):Bt(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,l=s*o.x,c=r*o.y;return{width:i,height:a,x:l,y:c}}function Fa(e,t,n){let r;if(t==="viewport")r=Yg(e,n);else if(t==="document")r=Gg(Nt(e));else if(Ct(t))r=Xg(t,n);else{const s=Ac(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return _s(r)}function kc(e,t){const n=mn(e);return n===t||!Ct(n)||Yn(n)?!1:St(n).position==="fixed"||kc(n,t)}function Qg(e,t){const n=t.get(e);if(n)return n;let r=Mr(e,[],!1).filter(a=>Ct(a)&&kn(a)!=="body"),s=null;const o=St(e).position==="fixed";let i=o?mn(e):e;for(;Ct(i)&&!Yn(i);){const a=St(i),l=ki(i);!l&&a.position==="fixed"&&(s=null),(o?!l&&!s:!l&&a.position==="static"&&!!s&&Jg.has(s.position)||Vr(i)&&!l&&kc(e,i))?r=r.filter(u=>u!==i):s=a,i=mn(i)}return t.set(e,r),r}function Zg(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Vs(t)?[]:Qg(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((c,u)=>{const d=Fa(t,u,s);return c.top=dt(d.top,c.top),c.right=hn(d.right,c.right),c.bottom=hn(d.bottom,c.bottom),c.left=dt(d.left,c.left),c},Fa(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function ev(e){const{width:t,height:n}=Ec(e);return{width:t,height:n}}function tv(e,t,n){const r=Lt(t),s=Nt(t),o=n==="fixed",i=An(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const l=Bt(0);function c(){l.x=Ti(s)}if(r||!r&&!o)if((kn(t)!=="body"||Vr(s))&&(a=Hs(t)),r){const h=An(t,!0,o,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else s&&c();o&&!r&&s&&c();const u=s&&!r&&!o?Oc(s,a):Bt(0),d=i.left+a.scrollLeft-l.x-u.x,p=i.top+a.scrollTop-l.y-u.y;return{x:d,y:p,width:i.width,height:i.height}}function go(e){return St(e).position==="static"}function ja(e,t){if(!Lt(e)||St(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Nt(e)===n&&(n=n.ownerDocument.body),n}function Pc(e,t){const n=pt(e);if(Vs(e))return n;if(!Lt(e)){let s=mn(e);for(;s&&!Yn(s);){if(Ct(s)&&!go(s))return s;s=mn(s)}return n}let r=ja(e,t);for(;r&&qg(r)&&go(r);)r=ja(r,t);return r&&Yn(r)&&go(r)&&!ki(r)?n:r||zg(e)||n}const nv=async function(e){const t=this.getOffsetParent||Pc,n=this.getDimensions,r=await n(e.floating);return{reference:tv(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function rv(e){return St(e).direction==="rtl"}const sv={convertOffsetParentRelativeRectToViewportRelativeRect:Wg,getDocumentElement:Nt,getClippingRect:Zg,getOffsetParent:Pc,getElementRects:nv,getClientRects:Kg,getDimensions:ev,getScale:Kn,isElement:Ct,isRTL:rv};function Rc(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function ov(e,t){let n=null,r;const s=Nt(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const c=e.getBoundingClientRect(),{left:u,top:d,width:p,height:h}=c;if(a||t(),!p||!h)return;const m=Zr(d),y=Zr(s.clientWidth-(u+p)),_=Zr(s.clientHeight-(d+h)),C=Zr(u),b={rootMargin:-m+"px "+-y+"px "+-_+"px "+-C+"px",threshold:dt(0,hn(1,l))||1};let x=!0;function R(N){const F=N[0].intersectionRatio;if(F!==l){if(!x)return i();F?i(!1,F):r=setTimeout(()=>{i(!1,1e-7)},1e3)}F===1&&!Rc(c,e.getBoundingClientRect())&&i(),x=!1}try{n=new IntersectionObserver(R,{...b,root:s.ownerDocument})}catch{n=new IntersectionObserver(R,b)}n.observe(e)}return i(!0),o}function iv(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=Ri(e),u=s||o?[...c?Mr(c):[],...Mr(t)]:[];u.forEach(C=>{s&&C.addEventListener("scroll",n,{passive:!0}),o&&C.addEventListener("resize",n)});const d=c&&a?ov(c,n):null;let p=-1,h=null;i&&(h=new ResizeObserver(C=>{let[O]=C;O&&O.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=h)==null||b.observe(t)})),n()}),c&&!l&&h.observe(c),h.observe(t));let m,y=l?An(e):null;l&&_();function _(){const C=An(e);y&&!Rc(y,C)&&n(),y=C,m=requestAnimationFrame(_)}return n(),()=>{var C;u.forEach(O=>{s&&O.removeEventListener("scroll",n),o&&O.removeEventListener("resize",n)}),d?.(),(C=h)==null||C.disconnect(),h=null,l&&cancelAnimationFrame(m)}}const av=Tg,lv=Mg,za=kg,uv=Ig,cv=Pg,dv=Og,fv=Dg,pv=(e,t,n)=>{const r=new Map,s={platform:sv,...n},o={...s.platform,_c:r};return Ag(e,t,{...s,platform:o})};function hv(e){return e!=null&&typeof e=="object"&&"$el"in e}function Jo(e){if(hv(e)){const t=e.$el;return Oi(t)&&kn(t)==="#comment"?null:t}return e}function Fn(e){return typeof e=="function"?e():f(e)}function mv(e){return{name:"arrow",options:e,fn(t){const n=Jo(Fn(e.element));return n==null?{}:dv({element:n,padding:e.padding}).fn(t)}}}function Tc(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Va(e,t){const n=Tc(e);return Math.round(t*n)/n}function gv(e,t,n){n===void 0&&(n={});const r=n.whileElementsMounted,s=H(()=>{var F;return(F=Fn(n.open))!=null?F:!0}),o=H(()=>Fn(n.middleware)),i=H(()=>{var F;return(F=Fn(n.placement))!=null?F:"bottom"}),a=H(()=>{var F;return(F=Fn(n.strategy))!=null?F:"absolute"}),l=H(()=>{var F;return(F=Fn(n.transform))!=null?F:!0}),c=H(()=>Jo(e.value)),u=H(()=>Jo(t.value)),d=L(0),p=L(0),h=L(a.value),m=L(i.value),y=Gt({}),_=L(!1),C=H(()=>{const F={position:h.value,left:"0",top:"0"};if(!u.value)return F;const k=Va(u.value,d.value),A=Va(u.value,p.value);return l.value?{...F,transform:"translate("+k+"px, "+A+"px)",...Tc(u.value)>=1.5&&{willChange:"transform"}}:{position:h.value,left:k+"px",top:A+"px"}});let O;function b(){if(c.value==null||u.value==null)return;const F=s.value;pv(c.value,u.value,{middleware:o.value,placement:i.value,strategy:a.value}).then(k=>{d.value=k.x,p.value=k.y,h.value=k.strategy,m.value=k.placement,y.value=k.middlewareData,_.value=F!==!1})}function x(){typeof O=="function"&&(O(),O=void 0)}function R(){if(x(),r===void 0){b();return}if(c.value!=null&&u.value!=null){O=r(c.value,u.value,b);return}}function N(){s.value||(_.value=!1)}return qe([o,i,a,s],b,{flush:"sync"}),qe([c,u],R,{flush:"sync"}),qe(s,N,{flush:"sync"}),$r()&&Os(x),{x:Rn(d),y:Rn(p),strategy:Rn(h),placement:Rn(m),middlewareData:Rn(y),isPositioned:Rn(_),floatingStyles:C,update:b}}const Mc={side:"bottom",sideOffset:0,sideFlip:!0,align:"center",alignOffset:0,alignFlip:!0,arrowPadding:0,avoidCollisions:!0,collisionBoundary:()=>[],collisionPadding:0,sticky:"partial",hideWhenDetached:!1,positionStrategy:"fixed",updatePositionStrategy:"optimized",prioritizePosition:!1},[vv,yv]=gt("PopperContent");var bv=I({inheritAttrs:!1,__name:"PopperContent",props:pu({side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...Mc}),emits:["placed"],setup(e,{emit:t}){const n=e,r=t,s=bc(),{forwardRef:o,currentElement:i}=_e(),a=L(),l=L(),{width:c,height:u}=Yh(l),d=H(()=>n.side+(n.align!=="center"?`-${n.align}`:"")),p=H(()=>typeof n.collisionPadding=="number"?n.collisionPadding:{top:0,right:0,bottom:0,left:0,...n.collisionPadding}),h=H(()=>Array.isArray(n.collisionBoundary)?n.collisionBoundary:[n.collisionBoundary]),m=H(()=>({padding:p.value,boundary:h.value.filter(pg),altBoundary:h.value.length>0})),y=H(()=>({mainAxis:n.sideFlip,crossAxis:n.alignFlip})),_=bh(()=>[av({mainAxis:n.sideOffset+u.value,alignmentAxis:n.alignOffset}),n.prioritizePosition&&n.avoidCollisions&&za({...m.value,...y.value}),n.avoidCollisions&&lv({mainAxis:!0,crossAxis:!!n.prioritizePosition,limiter:n.sticky==="partial"?fv():void 0,...m.value}),!n.prioritizePosition&&n.avoidCollisions&&za({...m.value,...y.value}),uv({...m.value,apply:({elements:le,rects:ge,availableWidth:ve,availableHeight:xe})=>{const{width:V,height:U}=ge.reference,B=le.floating.style;B.setProperty("--reka-popper-available-width",`${ve}px`),B.setProperty("--reka-popper-available-height",`${xe}px`),B.setProperty("--reka-popper-anchor-width",`${V}px`),B.setProperty("--reka-popper-anchor-height",`${U}px`)}}),l.value&&mv({element:l.value,padding:n.arrowPadding}),hg({arrowWidth:c.value,arrowHeight:u.value}),n.hideWhenDetached&&cv({strategy:"referenceHidden",...m.value})]),C=H(()=>n.reference??s.anchor.value),{floatingStyles:O,placement:b,isPositioned:x,middlewareData:R}=gv(C,a,{strategy:n.positionStrategy,placement:d,whileElementsMounted:(...le)=>iv(...le,{layoutShift:!n.disableUpdateOnLayoutShift,animationFrame:n.updatePositionStrategy==="always"}),middleware:_}),N=H(()=>Wo(b.value)[0]),F=H(()=>Wo(b.value)[1]);Au(()=>{x.value&&r("placed")});const k=H(()=>R.value.arrow?.centerOffset!==0),A=L("");st(()=>{i.value&&(A.value=window.getComputedStyle(i.value).zIndex)});const Y=H(()=>R.value.arrow?.x??0),se=H(()=>R.value.arrow?.y??0);return yv({placedSide:N,onArrowChange:le=>l.value=le,arrowX:Y,arrowY:se,shouldHideArrow:k}),(le,ge)=>(T(),be("div",{ref_key:"floatingRef",ref:a,"data-reka-popper-content-wrapper":"",style:vn({...f(O),transform:f(x)?f(O).transform:"translate(0, -200%)",minWidth:"max-content",zIndex:A.value,"--reka-popper-transform-origin":[f(R).transformOrigin?.x,f(R).transformOrigin?.y].join(" "),...f(R).hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}})},[E(f(Ie),ue({ref:f(o)},le.$attrs,{"as-child":n.asChild,as:le.as,"data-side":N.value,"data-align":F.value,style:{animation:f(x)?void 0:"none"}}),{default:w(()=>[q(le.$slots,"default")]),_:3},16,["as-child","as","data-side","data-align","style"])],4))}}),Dc=bv;const _v={top:"bottom",right:"left",bottom:"top",left:"right"};var wv=I({inheritAttrs:!1,__name:"PopperArrow",props:{width:{type:Number,required:!1},height:{type:Number,required:!1},rounded:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const{forwardRef:t}=_e(),n=vv(),r=H(()=>_v[n.placedSide.value]);return(s,o)=>(T(),be("span",{ref:i=>{f(n).onArrowChange(i)},style:vn({position:"absolute",left:f(n).arrowX?.value?`${f(n).arrowX?.value}px`:void 0,top:f(n).arrowY?.value?`${f(n).arrowY?.value}px`:void 0,[r.value]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[f(n).placedSide.value],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[f(n).placedSide.value],visibility:f(n).shouldHideArrow.value?"hidden":void 0})},[E(fg,ue(s.$attrs,{ref:f(t),style:{display:"block"},as:s.as,"as-child":s.asChild,rounded:s.rounded,width:s.width,height:s.height}),{default:w(()=>[q(s.$slots,"default")]),_:3},16,["as","as-child","rounded","width","height"])],4))}}),xv=wv,Cv=I({__name:"MenuAnchor",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(wc),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Sv=Cv;function Ev(){const e=L(!1);return ht(()=>{Gn("keydown",()=>{e.value=!0},{capture:!0,passive:!0}),Gn(["pointerdown","pointermove"],()=>{e.value=!1},{capture:!0,passive:!0})}),e}const Av=sc(Ev),[Us,Ov]=gt(["MenuRoot","MenuSub"],"MenuContext"),[Mi,kv]=gt("MenuRoot");var Pv=I({__name:"MenuRoot",props:{open:{type:Boolean,required:!1,default:!1},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,{modal:s,dir:o}=wt(n),i=wi(o),a=Fr(n,"open",r),l=L(),c=Av();return Ov({open:a,onOpenChange:u=>{a.value=u},content:l,onContentChange:u=>{l.value=u}}),kv({onClose:()=>{a.value=!1},isUsingKeyboardRef:c,dir:i,modal:s}),(u,d)=>(T(),z(f(_c),null,{default:w(()=>[q(u.$slots,"default")]),_:3}))}}),Rv=Pv;const[Ic,Tv]=gt("MenuContent");var Mv=I({__name:"MenuContentImpl",props:pu({loop:{type:Boolean,required:!1},disableOutsidePointerEvents:{type:Boolean,required:!1},disableOutsideScroll:{type:Boolean,required:!1},trapFocus:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},{...Mc}),emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus","dismiss"],setup(e,{emit:t}){const n=e,r=t,s=Us(),o=Mi(),{trapFocus:i,disableOutsidePointerEvents:a,loop:l}=wt(n);qh(),ac(a.value);const c=L(""),u=L(0),d=L(0),p=L(null),h=L("right"),m=L(0),y=L(null),_=L(),{forwardRef:C,currentElement:O}=_e(),{handleTypeaheadSearch:b}=Xh();qe(O,A=>{s.onContentChange(A)}),On(()=>{window.clearTimeout(u.value)});function x(A){return h.value===p.value?.side&&Em(A,p.value?.area)}async function R(A){r("openAutoFocus",A),!A.defaultPrevented&&(A.preventDefault(),O.value?.focus({preventScroll:!0}))}function N(A){if(A.defaultPrevented)return;const se=A.target.closest("[data-reka-menu-content]")===A.currentTarget,le=A.ctrlKey||A.altKey||A.metaKey,ge=A.key.length===1,ve=yh(A,at(),O.value,{loop:l.value,arrowKeyOptions:"vertical",dir:o?.dir.value,focus:!0,attributeName:"[data-reka-collection-item]:not([data-disabled])"});if(ve)return ve?.focus();if(A.code==="Space")return;const xe=_.value?.getItems()??[];if(se&&(A.key==="Tab"&&A.preventDefault(),!le&&ge&&b(A.key,xe)),A.target!==O.value||!xm.includes(A.key))return;A.preventDefault();const V=[...xe.map(U=>U.ref)];hc.includes(A.key)&&V.reverse(),Cm(V)}function F(A){A?.currentTarget?.contains?.(A.target)||(window.clearTimeout(u.value),c.value="")}function k(A){if(!Uo(A))return;const Y=A.target,se=m.value!==A.clientX;if(A?.currentTarget?.contains(Y)&&se){const le=A.clientX>m.value?"right":"left";h.value=le,m.value=A.clientX}}return Tv({onItemEnter:A=>!!x(A),onItemLeave:A=>{x(A)||(O.value?.focus(),y.value=null)},onTriggerLeave:A=>!!x(A),searchRef:c,pointerGraceTimerRef:d,onPointerGraceIntentChange:A=>{p.value=A}}),(A,Y)=>(T(),z(f(pc),{"as-child":"",trapped:f(i),onMountAutoFocus:R,onUnmountAutoFocus:Y[7]||(Y[7]=se=>r("closeAutoFocus",se))},{default:w(()=>[E(f(xi),{"as-child":"","disable-outside-pointer-events":f(a),onEscapeKeyDown:Y[2]||(Y[2]=se=>r("escapeKeyDown",se)),onPointerDownOutside:Y[3]||(Y[3]=se=>r("pointerDownOutside",se)),onFocusOutside:Y[4]||(Y[4]=se=>r("focusOutside",se)),onInteractOutside:Y[5]||(Y[5]=se=>r("interactOutside",se)),onDismiss:Y[6]||(Y[6]=se=>r("dismiss"))},{default:w(()=>[E(f(rg),{ref_key:"rovingFocusGroupRef",ref:_,"current-tab-stop-id":y.value,"onUpdate:currentTabStopId":Y[0]||(Y[0]=se=>y.value=se),"as-child":"",orientation:"vertical",dir:f(o).dir.value,loop:f(l),onEntryFocus:Y[1]||(Y[1]=se=>{r("entryFocus",se),f(o).isUsingKeyboardRef.value||se.preventDefault()})},{default:w(()=>[E(f(Dc),{ref:f(C),role:"menu",as:A.as,"as-child":A.asChild,"aria-orientation":"vertical","data-reka-menu-content":"","data-state":f(mc)(f(s).open.value),dir:f(o).dir.value,side:A.side,"side-offset":A.sideOffset,align:A.align,"align-offset":A.alignOffset,"avoid-collisions":A.avoidCollisions,"collision-boundary":A.collisionBoundary,"collision-padding":A.collisionPadding,"arrow-padding":A.arrowPadding,"prioritize-position":A.prioritizePosition,"position-strategy":A.positionStrategy,"update-position-strategy":A.updatePositionStrategy,sticky:A.sticky,"hide-when-detached":A.hideWhenDetached,reference:A.reference,onKeydown:N,onBlur:F,onPointermove:k},{default:w(()=>[q(A.$slots,"default")]),_:3},8,["as","as-child","data-state","dir","side","side-offset","align","align-offset","avoid-collisions","collision-boundary","collision-padding","arrow-padding","prioritize-position","position-strategy","update-position-strategy","sticky","hide-when-detached","reference"])]),_:3},8,["current-tab-stop-id","dir","loop"])]),_:3},8,["disable-outside-pointer-events"])]),_:3},8,["trapped"]))}}),$c=Mv,Dv=I({inheritAttrs:!1,__name:"MenuItemImpl",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,n=Ic(),{forwardRef:r}=_e(),{CollectionItem:s}=yc(),o=L(!1);async function i(l){l.defaultPrevented||Uo(l)&&(t.disabled?n.onItemLeave(l):n.onItemEnter(l)||l.currentTarget?.focus({preventScroll:!0}))}async function a(l){await Ke(),!l.defaultPrevented&&Uo(l)&&n.onItemLeave(l)}return(l,c)=>(T(),z(f(s),{value:{textValue:l.textValue}},{default:w(()=>[E(f(Ie),ue({ref:f(r),role:"menuitem",tabindex:"-1"},l.$attrs,{as:l.as,"as-child":l.asChild,"aria-disabled":l.disabled||void 0,"data-disabled":l.disabled?"":void 0,"data-highlighted":o.value?"":void 0,onPointermove:i,onPointerleave:a,onFocus:c[0]||(c[0]=async u=>{await Ke(),!(u.defaultPrevented||l.disabled)&&(o.value=!0)}),onBlur:c[1]||(c[1]=async u=>{await Ke(),!u.defaultPrevented&&(o.value=!1)})}),{default:w(()=>[q(l.$slots,"default")]),_:3},16,["as","as-child","aria-disabled","data-disabled","data-highlighted"])]),_:3},8,["value"]))}}),Iv=Dv,$v=I({__name:"MenuItem",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["select"],setup(e,{emit:t}){const n=e,r=t,{forwardRef:s,currentElement:o}=_e(),i=Mi(),a=Ic(),l=L(!1);async function c(){const u=o.value;if(!n.disabled&&u){const d=new CustomEvent(_m,{bubbles:!0,cancelable:!0});r("select",d),await Ke(),d.defaultPrevented?l.value=!1:i.onClose()}}return(u,d)=>(T(),z(Iv,ue(n,{ref:f(s),onClick:c,onPointerdown:d[0]||(d[0]=()=>{l.value=!0}),onPointerup:d[1]||(d[1]=async p=>{await Ke(),!p.defaultPrevented&&(l.value||p.currentTarget?.click())}),onKeydown:d[2]||(d[2]=async p=>{const h=f(a).searchRef.value!=="";u.disabled||h&&p.key===" "||f(Ho).includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})}),{default:w(()=>[q(u.$slots,"default")]),_:3},16))}}),Bv=$v,qv=I({__name:"MenuRootContentModal",props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=Ot(n,r),o=Us(),{forwardRef:i,currentElement:a}=_e();return cc(a),(l,c)=>(T(),z($c,ue(f(s),{ref:f(i),"trap-focus":f(o).open.value,"disable-outside-pointer-events":f(o).open.value,"disable-outside-scroll":!0,onDismiss:c[0]||(c[0]=u=>f(o).onOpenChange(!1)),onFocusOutside:c[1]||(c[1]=qu(u=>r("focusOutside",u),["prevent"]))}),{default:w(()=>[q(l.$slots,"default")]),_:3},16,["trap-focus","disable-outside-pointer-events"]))}}),Lv=qv,Nv=I({__name:"MenuRootContentNonModal",props:{loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const s=Ot(e,t),o=Us();return(i,a)=>(T(),z($c,ue(f(s),{"trap-focus":!1,"disable-outside-pointer-events":!1,"disable-outside-scroll":!1,onDismiss:a[0]||(a[0]=l=>f(o).onOpenChange(!1))}),{default:w(()=>[q(i.$slots,"default")]),_:3},16))}}),Fv=Nv,jv=I({__name:"MenuContent",props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","entryFocus","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const s=Ot(e,t),o=Us(),i=Mi();return(a,l)=>(T(),z(f(js),{present:a.forceMount||f(o).open.value},{default:w(()=>[f(i).modal.value?(T(),z(Lv,rt(ue({key:0},{...a.$attrs,...f(s)})),{default:w(()=>[q(a.$slots,"default")]),_:3},16)):(T(),z(Fv,rt(ue({key:1},{...a.$attrs,...f(s)})),{default:w(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),zv=jv,Vv=I({__name:"MenuLabel",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"div"}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Hv=Vv,Uv=I({__name:"MenuPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ci),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Wv=Uv,Kv=I({__name:"MenuSeparator",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),ue(t,{role:"separator","aria-orientation":"horizontal"}),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Gv=Kv;const[Bc,Yv]=gt("DropdownMenuRoot");var Jv=I({__name:"DropdownMenuRoot",props:{defaultOpen:{type:Boolean,required:!1},open:{type:Boolean,required:!1,default:void 0},dir:{type:String,required:!1},modal:{type:Boolean,required:!1,default:!0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t;_e();const s=Fr(n,"open",r,{defaultValue:n.defaultOpen,passive:n.open===void 0}),o=L(),{modal:i,dir:a}=wt(n),l=wi(a);return Yv({open:s,onOpenChange:c=>{s.value=c},onOpenToggle:()=>{s.value=!s.value},triggerId:"",triggerElement:o,contentId:"",modal:i,dir:l}),(c,u)=>(T(),z(f(Rv),{open:f(s),"onUpdate:open":u[0]||(u[0]=d=>Re(s)?s.value=d:null),dir:f(l),modal:f(i)},{default:w(()=>[q(c.$slots,"default",{open:f(s)})]),_:3},8,["open","dir","modal"]))}}),Xv=Jv,Qv=I({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean,required:!1},loop:{type:Boolean,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},sideFlip:{type:Boolean,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},alignFlip:{type:Boolean,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1},disableUpdateOnLayoutShift:{type:Boolean,required:!1},prioritizePosition:{type:Boolean,required:!1},reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:t}){const s=Ot(e,t);_e();const o=Bc(),i=L(!1);function a(l){l.defaultPrevented||(i.value||setTimeout(()=>{o.triggerElement.value?.focus()},0),i.value=!1,l.preventDefault())}return o.contentId||=Rr(void 0,"reka-dropdown-menu-content"),(l,c)=>(T(),z(f(zv),ue(f(s),{id:f(o).contentId,"aria-labelledby":f(o)?.triggerId,style:{"--reka-dropdown-menu-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-dropdown-menu-content-available-width":"var(--reka-popper-available-width)","--reka-dropdown-menu-content-available-height":"var(--reka-popper-available-height)","--reka-dropdown-menu-trigger-width":"var(--reka-popper-anchor-width)","--reka-dropdown-menu-trigger-height":"var(--reka-popper-anchor-height)"},onCloseAutoFocus:a,onInteractOutside:c[0]||(c[0]=u=>{if(u.defaultPrevented)return;const d=u.detail.originalEvent,p=d.button===0&&d.ctrlKey===!0,h=d.button===2||p;(!f(o).modal.value||h)&&(i.value=!0),f(o).triggerElement.value?.contains(u.target)&&u.preventDefault()})}),{default:w(()=>[q(l.$slots,"default")]),_:3},16,["id","aria-labelledby"]))}}),Zv=Qv,ey=I({__name:"DropdownMenuItem",props:{disabled:{type:Boolean,required:!1},textValue:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},emits:["select"],setup(e,{emit:t}){const n=e,s=jr(t);return _e(),(o,i)=>(T(),z(f(Bv),rt(mt({...n,...f(s)})),{default:w(()=>[q(o.$slots,"default")]),_:3},16))}}),ty=ey,ny=I({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return _e(),(n,r)=>(T(),z(f(Hv),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),ry=ny,sy=I({__name:"DropdownMenuPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(Wv),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),oy=sy,iy=I({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return _e(),(n,r)=>(T(),z(f(Gv),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),ay=iy,ly=I({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e,n=Bc(),{forwardRef:r,currentElement:s}=_e();return ht(()=>{n.triggerElement=s}),n.triggerId||=Rr(void 0,"reka-dropdown-menu-trigger"),(o,i)=>(T(),z(f(Sv),{"as-child":""},{default:w(()=>[E(f(Ie),{id:f(n).triggerId,ref:f(r),type:o.as==="button"?"button":void 0,"as-child":t.asChild,as:o.as,"aria-haspopup":"menu","aria-expanded":f(n).open.value,"aria-controls":f(n).open.value?f(n).contentId:void 0,"data-disabled":o.disabled?"":void 0,disabled:o.disabled,"data-state":f(n).open.value?"open":"closed",onClick:i[0]||(i[0]=async a=>{!o.disabled&&a.button===0&&a.ctrlKey===!1&&(f(n)?.onOpenToggle(),await Ke(),f(n).open.value&&a.preventDefault())}),onKeydown:i[1]||(i[1]=Lu(a=>{o.disabled||(["Enter"," "].includes(a.key)&&f(n).onOpenToggle(),a.key==="ArrowDown"&&f(n).onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())},["enter","space","arrow-down"]))},{default:w(()=>[q(o.$slots,"default")]),_:3},8,["id","type","as-child","as","aria-expanded","aria-controls","data-disabled","disabled","data-state"])]),_:3}))}}),uy=ly,cy=I({__name:"BaseSeparator",props:{orientation:{type:String,required:!1,default:"horizontal"},decorative:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e,n=["horizontal","vertical"];function r(a){return n.includes(a)}const s=H(()=>r(t.orientation)?t.orientation:"horizontal"),o=H(()=>s.value==="vertical"?t.orientation:void 0),i=H(()=>t.decorative?{role:"none"}:{"aria-orientation":o.value,role:"separator"});return(a,l)=>(T(),z(f(Ie),ue({as:a.as,"as-child":a.asChild,"data-orientation":s.value},i.value),{default:w(()=>[q(a.$slots,"default")]),_:3},16,["as","as-child","data-orientation"]))}}),dy=cy,fy=I({__name:"Separator",props:{orientation:{type:String,required:!1,default:"horizontal"},decorative:{type:Boolean,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(dy,rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),py=fy,hy=I({__name:"TooltipArrow",props:{width:{type:Number,required:!1,default:10},height:{type:Number,required:!1,default:5},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"svg"}},setup(e){const t=e;return _e(),(n,r)=>(T(),z(f(xv),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),my=hy;const[Di,gy]=gt("TooltipProvider");var vy=I({inheritAttrs:!1,__name:"TooltipProvider",props:{delayDuration:{type:Number,required:!1,default:700},skipDelayDuration:{type:Number,required:!1,default:300},disableHoverableContent:{type:Boolean,required:!1,default:!1},disableClosingTrigger:{type:Boolean,required:!1},disabled:{type:Boolean,required:!1},ignoreNonKeyboardFocus:{type:Boolean,required:!1,default:!1}},setup(e){const t=e,{delayDuration:n,skipDelayDuration:r,disableHoverableContent:s,disableClosingTrigger:o,ignoreNonKeyboardFocus:i,disabled:a}=wt(t);_e();const l=L(!0),c=L(!1),{start:u,stop:d}=ic(()=>{l.value=!0},r,{immediate:!1});return gy({isOpenDelayed:l,delayDuration:n,onOpen(){d(),l.value=!1},onClose(){u()},isPointerInTransitRef:c,disableHoverableContent:s,disableClosingTrigger:o,disabled:a,ignoreNonKeyboardFocus:i}),(p,h)=>q(p.$slots,"default")}}),yy=vy;const qc="tooltip.open",[Ws,by]=gt("TooltipRoot");var _y=I({__name:"TooltipRoot",props:{defaultOpen:{type:Boolean,required:!1,default:!1},open:{type:Boolean,required:!1,default:void 0},delayDuration:{type:Number,required:!1,default:void 0},disableHoverableContent:{type:Boolean,required:!1,default:void 0},disableClosingTrigger:{type:Boolean,required:!1,default:void 0},disabled:{type:Boolean,required:!1,default:void 0},ignoreNonKeyboardFocus:{type:Boolean,required:!1,default:void 0}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t;_e();const s=Di(),o=H(()=>n.disableHoverableContent??s.disableHoverableContent.value),i=H(()=>n.disableClosingTrigger??s.disableClosingTrigger.value),a=H(()=>n.disabled??s.disabled.value),l=H(()=>n.delayDuration??s.delayDuration.value),c=H(()=>n.ignoreNonKeyboardFocus??s.ignoreNonKeyboardFocus.value),u=Fr(n,"open",r,{defaultValue:n.defaultOpen,passive:n.open===void 0});qe(u,b=>{s.onClose&&(b?(s.onOpen(),document.dispatchEvent(new CustomEvent(qc))):s.onClose())});const d=L(!1),p=L(),h=H(()=>u.value?d.value?"delayed-open":"instant-open":"closed"),{start:m,stop:y}=ic(()=>{d.value=!0,u.value=!0},l,{immediate:!1});function _(){y(),d.value=!1,u.value=!0}function C(){y(),u.value=!1}function O(){m()}return by({contentId:"",open:u,stateAttribute:h,trigger:p,onTriggerChange(b){p.value=b},onTriggerEnter(){s.isOpenDelayed.value?O():_()},onTriggerLeave(){o.value?C():y()},onOpen:_,onClose:C,disableHoverableContent:o,disableClosingTrigger:i,disabled:a,ignoreNonKeyboardFocus:c}),(b,x)=>(T(),z(f(_c),null,{default:w(()=>[q(b.$slots,"default",{open:f(u)})]),_:3}))}}),wy=_y,xy=I({__name:"TooltipContentImpl",props:{ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1,default:"top"},sideOffset:{type:Number,required:!1,default:0},align:{type:null,required:!1,default:"center"},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1,default:!0},collisionBoundary:{type:null,required:!1,default:()=>[]},collisionPadding:{type:[Number,Object],required:!1,default:0},arrowPadding:{type:Number,required:!1,default:0},sticky:{type:String,required:!1,default:"partial"},hideWhenDetached:{type:Boolean,required:!1,default:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,s=Ws(),{forwardRef:o}=_e(),i=gf(),a=H(()=>i.default?.({})),l=H(()=>{if(n.ariaLabel)return n.ariaLabel;let u="";function d(p){typeof p.children=="string"&&p.type!==xt?u+=p.children:Array.isArray(p.children)&&p.children.forEach(h=>d(h))}return a.value?.forEach(p=>d(p)),u}),c=H(()=>{const{ariaLabel:u,...d}=n;return d});return ht(()=>{Gn(window,"scroll",u=>{u.target?.contains(s.trigger.value)&&s.onClose()}),Gn(window,qc,s.onClose)}),(u,d)=>(T(),z(f(xi),{"as-child":"","disable-outside-pointer-events":!1,onEscapeKeyDown:d[0]||(d[0]=p=>r("escapeKeyDown",p)),onPointerDownOutside:d[1]||(d[1]=p=>{f(s).disableClosingTrigger.value&&f(s).trigger.value?.contains(p.target)&&p.preventDefault(),r("pointerDownOutside",p)}),onFocusOutside:d[2]||(d[2]=qu(()=>{},["prevent"])),onDismiss:d[3]||(d[3]=p=>f(s).onClose())},{default:w(()=>[E(f(Dc),ue({ref:f(o),"data-state":f(s).stateAttribute.value},{...u.$attrs,...c.value},{style:{"--reka-tooltip-content-transform-origin":"var(--reka-popper-transform-origin)","--reka-tooltip-content-available-width":"var(--reka-popper-available-width)","--reka-tooltip-content-available-height":"var(--reka-popper-available-height)","--reka-tooltip-trigger-width":"var(--reka-popper-anchor-width)","--reka-tooltip-trigger-height":"var(--reka-popper-anchor-height)"}}),{default:w(()=>[q(u.$slots,"default"),E(f(og),{id:f(s).contentId,role:"tooltip"},{default:w(()=>[$e(Ve(l.value),1)]),_:1},8,["id"])]),_:3},16,["data-state"])]),_:3}))}}),Lc=xy,Cy=I({__name:"TooltipContentHoverable",props:{ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1},sideOffset:{type:Number,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},setup(e){const n=zr(e),{forwardRef:r,currentElement:s}=_e(),{trigger:o,onClose:i}=Ws(),a=Di(),{isPointerInTransit:l,onPointerExit:c}=Lh(o,s);return a.isPointerInTransitRef=l,c(()=>{i()}),(u,d)=>(T(),z(Lc,ue({ref:f(r)},f(n)),{default:w(()=>[q(u.$slots,"default")]),_:3},16))}}),Sy=Cy,Ey=I({__name:"TooltipContent",props:{forceMount:{type:Boolean,required:!1},ariaLabel:{type:String,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1},side:{type:null,required:!1,default:"top"},sideOffset:{type:Number,required:!1},align:{type:null,required:!1},alignOffset:{type:Number,required:!1},avoidCollisions:{type:Boolean,required:!1},collisionBoundary:{type:null,required:!1},collisionPadding:{type:[Number,Object],required:!1},arrowPadding:{type:Number,required:!1},sticky:{type:String,required:!1},hideWhenDetached:{type:Boolean,required:!1},positionStrategy:{type:String,required:!1},updatePositionStrategy:{type:String,required:!1}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,s=Ws(),o=Ot(n,r),{forwardRef:i}=_e();return(a,l)=>(T(),z(f(js),{present:a.forceMount||f(s).open.value},{default:w(()=>[(T(),z(Ar(f(s).disableHoverableContent.value?Lc:Sy),ue({ref:f(i)},f(o)),{default:w(()=>[q(a.$slots,"default")]),_:3},16))]),_:3},8,["present"]))}}),Ay=Ey,Oy=I({__name:"TooltipPortal",props:{to:{type:null,required:!1},disabled:{type:Boolean,required:!1},defer:{type:Boolean,required:!1},forceMount:{type:Boolean,required:!1}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ci),rt(mt(t)),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),ky=Oy,Py=I({__name:"TooltipTrigger",props:{reference:{type:null,required:!1},asChild:{type:Boolean,required:!1},as:{type:null,required:!1,default:"button"}},setup(e){const t=e,n=Ws(),r=Di();n.contentId||=Rr(void 0,"reka-tooltip-content");const{forwardRef:s,currentElement:o}=_e(),i=L(!1),a=L(!1),l=H(()=>n.disabled.value?{}:{click:y,focus:h,pointermove:d,pointerleave:p,pointerdown:u,blur:m});ht(()=>{n.onTriggerChange(o.value)});function c(){setTimeout(()=>{i.value=!1},1)}function u(){n.open&&!n.disableClosingTrigger.value&&n.onClose(),i.value=!0,document.addEventListener("pointerup",c,{once:!0})}function d(_){_.pointerType!=="touch"&&!a.value&&!r.isPointerInTransitRef.value&&(n.onTriggerEnter(),a.value=!0)}function p(){n.onTriggerLeave(),a.value=!1}function h(_){i.value||n.ignoreNonKeyboardFocus.value&&!_.target.matches?.(":focus-visible")||n.onOpen()}function m(){n.onClose()}function y(){n.disableClosingTrigger.value||n.onClose()}return(_,C)=>(T(),z(f(wc),{"as-child":"",reference:_.reference},{default:w(()=>[E(f(Ie),ue({ref:f(s),"aria-describedby":f(n).open.value?f(n).contentId:void 0,"data-state":f(n).stateAttribute.value,as:_.as,"as-child":t.asChild,"data-grace-area-trigger":""},hf(l.value)),{default:w(()=>[q(_.$slots,"default")]),_:3},16,["aria-describedby","data-state","as","as-child"])]),_:3},8,["reference"]))}}),Ry=Py;const Ty=I({__name:"Sheet",props:{open:{type:Boolean},defaultOpen:{type:Boolean},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const s=Ot(e,t);return(o,i)=>(T(),z(f(sm),ue({"data-slot":"sheet"},f(s)),{default:w(()=>[q(o.$slots,"default")]),_:3},16))}});function My(e){return $r()?(Os(e),!0):!1}const vo=new WeakMap,Dy=(...e)=>{var t;const n=e[0],r=(t=ut())==null?void 0:t.proxy;if(r==null&&!di())throw new Error("injectLocal must be called in setup");return r&&vo.has(r)&&n in vo.get(r)?vo.get(r)[n]:nt(...e)};function Iy(e){if(!Re(e))return Xt(e);const t=new Proxy({},{get(n,r,s){return f(Reflect.get(e.value,r,s))},set(n,r,s){return Re(e.value[r])&&!Re(s)?e.value[r].value=s:e.value[r]=s,!0},deleteProperty(n,r){return Reflect.deleteProperty(e.value,r)},has(n,r){return Reflect.has(e.value,r)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return Xt(t)}function $y(e){return Iy(H(e))}function vt(e,...t){const n=t.flat(),r=n[0];return $y(()=>Object.fromEntries(typeof r=="function"?Object.entries(wt(e)).filter(([s,o])=>!r(Fe(o),s)):Object.entries(wt(e)).filter(s=>!n.includes(s[0]))))}const By=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const qy=e=>typeof e<"u",Ly=Object.prototype.toString,Ny=e=>Ly.call(e)==="[object Object]";function Ha(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function yo(e){return Array.isArray(e)?e:[e]}function Fy(e,t,n){return qe(e,t,{...n,immediate:!0})}const Nc=By?window:void 0;function jy(e){var t;const n=Fe(e);return(t=n?.$el)!=null?t:n}function Fc(...e){const t=[],n=()=>{t.forEach(a=>a()),t.length=0},r=(a,l,c,u)=>(a.addEventListener(l,c,u),()=>a.removeEventListener(l,c,u)),s=H(()=>{const a=yo(Fe(e[0])).filter(l=>l!=null);return a.every(l=>typeof l!="string")?a:void 0}),o=Fy(()=>{var a,l;return[(l=(a=s.value)==null?void 0:a.map(c=>jy(c)))!=null?l:[Nc].filter(c=>c!=null),yo(Fe(s.value?e[1]:e[0])),yo(f(s.value?e[2]:e[1])),Fe(s.value?e[3]:e[2])]},([a,l,c,u])=>{if(n(),!a?.length||!l?.length||!c?.length)return;const d=Ny(u)?{...u}:u;t.push(...a.flatMap(p=>l.flatMap(h=>c.map(m=>r(p,h,m,d)))))},{flush:"post"}),i=()=>{o(),n()};return My(n),i}function zy(){const e=Gt(!1),t=ut();return t&&ht(()=>{e.value=!0},t),e}function Vy(e){const t=zy();return H(()=>(t.value,!!e()))}const Hy=Symbol("vueuse-ssr-width");function Uy(){const e=di()?Dy(Hy,null):null;return typeof e=="number"?e:void 0}function Wy(e,t={}){const{window:n=Nc,ssrWidth:r=Uy()}=t,s=Vy(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),o=Gt(typeof r=="number"),i=Gt(),a=Gt(!1),l=c=>{a.value=c.matches};return st(()=>{if(o.value){o.value=!s.value;const c=Fe(e).split(",");a.value=c.some(u=>{const d=u.includes("not all"),p=u.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),h=u.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let m=!!(p||h);return p&&m&&(m=r>=Ha(p[1])),h&&m&&(m=r<=Ha(h[1])),d?!m:m});return}s.value&&(i.value=n.matchMedia(Fe(e)),a.value=i.value.matches)}),Fc(i,"change",l,{passive:!0}),H(()=>a.value)}function Ky(e){return JSON.parse(JSON.stringify(e))}function jc(e,t,n,r={}){var s,o,i;const{clone:a=!1,passive:l=!1,eventName:c,deep:u=!1,defaultValue:d,shouldEmit:p}=r,h=ut(),m=n||h?.emit||((s=h?.$emit)==null?void 0:s.bind(h))||((i=(o=h?.proxy)==null?void 0:o.$emit)==null?void 0:i.bind(h?.proxy));let y=c;t||(t="modelValue"),y=y||`update:${t.toString()}`;const _=b=>a?typeof a=="function"?a(b):Ky(b):b,C=()=>qy(e[t])?_(e[t]):d,O=b=>{p?p(b)&&m(y,b):m(y,b)};if(l){const b=C(),x=L(b);let R=!1;return qe(()=>e[t],N=>{R||(R=!0,x.value=_(N),Ke(()=>R=!1))}),qe(x,N=>{!R&&(N!==e[t]||u)&&O(N)},{deep:u}),x}else return H({get(){return C()},set(b){O(b)}})}/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ua=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Gy=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),Yy=e=>{const t=Gy(e);return t.charAt(0).toUpperCase()+t.slice(1)},Jy=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),Wa=e=>e==="";/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var lr={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xy=({name:e,iconNode:t,absoluteStrokeWidth:n,"absolute-stroke-width":r,strokeWidth:s,"stroke-width":o,size:i=lr.width,color:a=lr.stroke,...l},{slots:c})=>_t("svg",{...lr,...l,width:i,height:i,stroke:a,"stroke-width":Wa(n)||Wa(r)||n===!0||r===!0?Number(s||o||lr["stroke-width"])*24/Number(i):s||o||lr["stroke-width"],class:Jy("lucide",l.class,...e?[`lucide-${Ua(Yy(e))}-icon`,`lucide-${Ua(e)}`]:["lucide-icon"])},[...t.map(u=>_t(...u)),...c.default?[c.default()]:[]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=(e,t)=>(n,{slots:r,attrs:s})=>_t(Xy,{...s,...n,iconNode:t,name:e},r);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qy=Be("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zy=Be("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eb=Be("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tb=Be("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nb=Be("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=Be("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rb=Be("clipboard-check",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sb=Be("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vc=Be("factory",[["path",{d:"M12 16h.01",key:"1drbdi"}],["path",{d:"M16 16h.01",key:"1f9h7w"}],["path",{d:"M3 19a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a.5.5 0 0 0-.769-.422l-4.462 2.844A.5.5 0 0 1 15 10.5v-2a.5.5 0 0 0-.769-.422L9.77 10.922A.5.5 0 0 1 9 10.5V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z",key:"1iv0i2"}],["path",{d:"M8 16h.01",key:"18s6g9"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ob=Be("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ib=Be("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ab=Be("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lb=Be("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ub=Be("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cb=Be("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const db=Be("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=Be("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=Be("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fb=Be("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wc=Be("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pb=Be("user-cog",[["path",{d:"M10 15H6a4 4 0 0 0-4 4v2",key:"1nfge6"}],["path",{d:"m14.305 16.53.923-.382",key:"1itpsq"}],["path",{d:"m15.228 13.852-.923-.383",key:"eplpkm"}],["path",{d:"m16.852 12.228-.383-.923",key:"13v3q0"}],["path",{d:"m16.852 17.772-.383.924",key:"1i8mnm"}],["path",{d:"m19.148 12.228.383-.923",key:"1q8j1v"}],["path",{d:"m19.53 18.696-.382-.924",key:"vk1qj3"}],["path",{d:"m20.772 13.852.924-.383",key:"n880s0"}],["path",{d:"m20.772 16.148.924.383",key:"1g6xey"}],["circle",{cx:"18",cy:"15",r:"3",key:"gjjjvw"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hb=Be("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=Be("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);/**
 * @license lucide-vue-next v0.536.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gb=Be("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),vb=I({__name:"SheetOverlay",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(Lm),ue({"data-slot":"sheet-overlay",class:f(he)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",t.class)},f(n)),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),yb=I({inheritAttrs:!1,__name:"SheetContent",props:{class:{},side:{default:"right"},forceMount:{type:Boolean},disableOutsidePointerEvents:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","openAutoFocus","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=vt(n,"class","side"),o=Ot(s,r);return(i,a)=>(T(),z(f(jm),null,{default:w(()=>[E(vb),E(f(Mm),ue({"data-slot":"sheet-content",class:f(he)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",i.side==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",i.side==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",i.side==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",i.side==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",n.class)},{...f(o),...i.$attrs}),{default:w(()=>[q(i.$slots,"default"),E(f(im),{class:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none"},{default:w(()=>[E(f(gb),{class:"size-4"}),a[0]||(a[0]=oe("span",{class:"sr-only"},"Close",-1))]),_:1,__:[0]})]),_:3},16,["class"])]),_:3}))}}),bb=I({__name:"SheetDescription",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(Im),ue({"data-slot":"sheet-description",class:f(he)("text-muted-foreground text-sm",t.class)},f(n)),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),_b=I({__name:"SheetHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sheet-header",class:Ee(f(he)("flex flex-col gap-1.5 p-4",t.class))},[q(n.$slots,"default")],2))}}),wb=I({__name:"SheetTitle",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(Vm),ue({"data-slot":"sheet-title",class:f(he)("text-foreground font-semibold",t.class)},f(n)),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),xb="sidebar_state",Cb=3600*24*7,Sb="16rem",Eb="18rem",Ab="3rem",Ob="b",[Ii,kb]=gt("Sidebar"),Pb={class:"flex h-full w-full flex-col"},Rb=["data-state","data-collapsible","data-variant","data-side"],Tb={"data-sidebar":"sidebar",class:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm"},Mb=I({inheritAttrs:!1,__name:"Sidebar",props:{side:{default:"left"},variant:{default:"sidebar"},collapsible:{default:"offcanvas"},class:{}},setup(e){const t=e,{isMobile:n,state:r,openMobile:s,setOpenMobile:o}=Ii();return(i,a)=>i.collapsible==="none"?(T(),be("div",ue({key:0,"data-slot":"sidebar",class:f(he)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",t.class)},i.$attrs),[q(i.$slots,"default")],16)):f(n)?(T(),z(f(Ty),ue({key:1,open:f(s)},i.$attrs,{"onUpdate:open":f(o)}),{default:w(()=>[E(f(yb),{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",side:i.side,class:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:vn({"--sidebar-width":f(Eb)})},{default:w(()=>[E(_b,{class:"sr-only"},{default:w(()=>[E(wb,null,{default:w(()=>a[0]||(a[0]=[$e("Sidebar",-1)])),_:1,__:[0]}),E(bb,null,{default:w(()=>a[1]||(a[1]=[$e("Displays the mobile sidebar.",-1)])),_:1,__:[1]})]),_:1}),oe("div",Pb,[q(i.$slots,"default")])]),_:3},8,["side","style"])]),_:3},16,["open","onUpdate:open"])):(T(),be("div",{key:2,class:"group peer text-sidebar-foreground hidden md:block","data-slot":"sidebar","data-state":f(r),"data-collapsible":f(r)==="collapsed"?i.collapsible:"","data-variant":i.variant,"data-side":i.side},[oe("div",{class:Ee(f(he)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",i.variant==="floating"||i.variant==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)"))},null,2),oe("div",ue({class:f(he)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",i.side==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",i.variant==="floating"||i.variant==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",t.class)},i.$attrs),[oe("div",Tb,[q(i.$slots,"default")])],16)],8,Rb))}}),Db=I({__name:"SidebarContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sidebar-content","data-sidebar":"content",class:Ee(f(he)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t.class))},[q(n.$slots,"default")],2))}}),Ib=I({__name:"SidebarFooter",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",class:Ee(f(he)("flex flex-col gap-2 p-2",t.class))},[q(n.$slots,"default")],2))}}),Ka=I({__name:"SidebarGroup",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sidebar-group","data-sidebar":"group",class:Ee(f(he)("relative flex w-full min-w-0 flex-col p-2",t.class))},[q(n.$slots,"default")],2))}}),Ga=I({__name:"SidebarGroupContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",class:Ee(f(he)("w-full text-sm",t.class))},[q(n.$slots,"default")],2))}}),Ya=I({__name:"SidebarGroupLabel",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),{"data-slot":"sidebar-group-label","data-sidebar":"group-label",as:n.as,"as-child":n.asChild,class:Ee(f(he)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t.class))},{default:w(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),$b=I({__name:"SidebarHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"sidebar-header","data-sidebar":"header",class:Ee(f(he)("flex flex-col gap-2 p-2",t.class))},[q(n.$slots,"default")],2))}}),Bb=I({__name:"Input",props:{defaultValue:{},modelValue:{},class:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,s=jc(n,"modelValue",t,{passive:!0,defaultValue:n.defaultValue});return(o,i)=>tu((T(),be("input",{"onUpdate:modelValue":i[0]||(i[0]=a=>Re(s)?s.value=a:null),"data-slot":"input",class:Ee(f(he)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",n.class))},null,2)),[[xp,f(s)]])}}),qb=I({__name:"SidebarInset",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("main",{"data-slot":"sidebar-inset",class:Ee(f(he)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t.class))},[q(n.$slots,"default")],2))}}),es=I({__name:"SidebarMenu",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",class:Ee(f(he)("flex w-full min-w-0 flex-col gap-1",t.class))},[q(n.$slots,"default")],2))}}),Lb=I({__name:"Tooltip",props:{defaultOpen:{type:Boolean},open:{type:Boolean},delayDuration:{},disableHoverableContent:{type:Boolean},disableClosingTrigger:{type:Boolean},disabled:{type:Boolean},ignoreNonKeyboardFocus:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const s=Ot(e,t);return(o,i)=>(T(),z(f(wy),ue({"data-slot":"tooltip"},f(s)),{default:w(()=>[q(o.$slots,"default")]),_:3},16))}}),Nb=I({inheritAttrs:!1,__name:"TooltipContent",props:{forceMount:{type:Boolean},ariaLabel:{},asChild:{type:Boolean},as:{},side:{},sideOffset:{default:4},align:{},alignOffset:{},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},class:{}},emits:["escapeKeyDown","pointerDownOutside"],setup(e,{emit:t}){const n=e,r=t,s=vt(n,"class"),o=Ot(s,r);return(i,a)=>(T(),z(f(ky),null,{default:w(()=>[E(f(Ay),ue({"data-slot":"tooltip-content"},{...f(o),...i.$attrs},{class:f(he)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",n.class)}),{default:w(()=>[q(i.$slots,"default"),E(f(my),{class:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]),_:3},16,["class"])]),_:3}))}}),Fb=I({__name:"TooltipTrigger",props:{reference:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ry),ue({"data-slot":"tooltip-trigger"},t),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Ja=I({__name:"SidebarMenuButtonChild",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),ue({"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":n.size,"data-active":n.isActive,class:f(he)(f(Ub)({variant:n.variant,size:n.size}),t.class),as:n.as,"as-child":n.asChild},n.$attrs),{default:w(()=>[q(n.$slots,"default")]),_:3},16,["data-size","data-active","class","as","as-child"]))}}),ts=I({inheritAttrs:!1,__name:"SidebarMenuButton",props:{variant:{default:"default"},size:{default:"default"},isActive:{type:Boolean},class:{},asChild:{type:Boolean},as:{default:"button"},tooltip:{}},setup(e){const t=e,{isMobile:n,state:r}=Ii(),s=vt(t,"tooltip");return(o,i)=>o.tooltip?(T(),z(f(Lb),{key:1},{default:w(()=>[E(f(Fb),{"as-child":""},{default:w(()=>[E(Ja,rt(mt({...f(s),...o.$attrs})),{default:w(()=>[q(o.$slots,"default")]),_:3},16)]),_:3}),E(f(Nb),{side:"right",align:"center",hidden:f(r)!=="collapsed"||f(n)},{default:w(()=>[typeof o.tooltip=="string"?(T(),be(Ue,{key:0},[$e(Ve(o.tooltip),1)],64)):(T(),z(Ar(o.tooltip),{key:1}))]),_:1},8,["hidden"])]),_:3})):(T(),z(Ja,rt(ue({key:0},{...f(s),...o.$attrs})),{default:w(()=>[q(o.$slots,"default")]),_:3},16))}}),ns=I({__name:"SidebarMenuItem",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",class:Ee(f(he)("group/menu-item relative",t.class))},[q(n.$slots,"default")],2))}}),jb=I({__name:"SidebarProvider",props:{defaultOpen:{type:Boolean,default:!0},open:{type:Boolean,default:void 0},class:{}},emits:["update:open"],setup(e,{emit:t}){const n=e,r=t,s=Wy("(max-width: 768px)"),o=L(!1),i=jc(n,"open",r,{defaultValue:n.defaultOpen??!1,passive:n.open===void 0});function a(d){i.value=d,document.cookie=`${xb}=${i.value}; path=/; max-age=${Cb}`}function l(d){o.value=d}function c(){return s.value?l(!o.value):a(!i.value)}Fc("keydown",d=>{d.key===Ob&&(d.metaKey||d.ctrlKey)&&(d.preventDefault(),c())});const u=H(()=>i.value?"expanded":"collapsed");return kb({state:u,open:i,setOpen:a,isMobile:s,openMobile:o,setOpenMobile:l,toggleSidebar:c}),(d,p)=>(T(),z(f(yy),{"delay-duration":0},{default:w(()=>[oe("div",ue({"data-slot":"sidebar-wrapper",style:{"--sidebar-width":f(Sb),"--sidebar-width-icon":f(Ab)},class:f(he)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n.class)},d.$attrs),[q(d.$slots,"default")],16)]),_:3}))}}),zb=I({__name:"Separator",props:{orientation:{default:"horizontal"},decorative:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(py),ue({"data-slot":"separator-root"},f(n),{class:f(he)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t.class)}),null,16,["class"]))}}),dr=I({__name:"Button",props:{variant:{},size:{},class:{},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),{"data-slot":"button",as:n.as,"as-child":n.asChild,class:Ee(f(he)(f(Vb)({variant:n.variant,size:n.size}),t.class))},{default:w(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),Vb=vi("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}}),Hb=I({__name:"SidebarTrigger",props:{class:{}},setup(e){const t=e,{toggleSidebar:n}=Ii();return(r,s)=>(T(),z(f(dr),{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",class:Ee(f(he)("h-7 w-7",t.class)),onClick:f(n)},{default:w(()=>[E(f(cb)),s[0]||(s[0]=oe("span",{class:"sr-only"},"Toggle Sidebar",-1))]),_:1,__:[0]},8,["class","onClick"]))}}),Ub=vi("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}}),Wb={baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"},useMock:!1};let Kb={...Wb};function rs(e,t="API_ERROR",n){return{code:t,message:e,details:n,timestamp:new Date().toISOString()}}async function Jn(e,t={}){const{config:n={},retries:r=0,retryDelay:s=1e3,...o}=t,i={...Kb,...n},a=i.baseURL||"",l=e.startsWith("http")?e:`${a}${e}`,c={...i.headers,...o.headers},u=new AbortController,d=setTimeout(()=>{u.abort()},i.timeout||1e4);try{const p=await fetch(l,{...o,headers:c,signal:u.signal});if(clearTimeout(d),!p.ok){const y=await p.text();let _;try{_=JSON.parse(y)}catch{_={message:y}}throw rs(_.message||`HTTP ${p.status}: ${p.statusText}`,`HTTP_${p.status}`,_)}const h=p.headers.get("content-type");let m;return h?.includes("application/json")?m=await p.json():m=await p.text(),i.useMock&&typeof m=="object"&&m.success!==void 0?m:{data:m,success:!0,message:"Request successful",timestamp:new Date().toISOString()}}catch(p){if(clearTimeout(d),p instanceof Error&&p.name==="AbortError"){const h=rs(`Request timeout after ${i.timeout}ms`,"TIMEOUT_ERROR");if(r>0)return await new Promise(m=>setTimeout(m,s)),Jn(e,{...t,retries:r-1,retryDelay:s*1.5});throw h}if(p instanceof TypeError&&p.message.includes("fetch")){const h=rs("Network error - please check your connection","NETWORK_ERROR",p);if(r>0)return await new Promise(m=>setTimeout(m,s)),Jn(e,{...t,retries:r-1,retryDelay:s*1.5});throw h}throw typeof p=="object"&&p!==null&&"code"in p?p:rs(p instanceof Error?p.message:"Unknown error occurred","UNKNOWN_ERROR",p)}}async function Xa(e,t,n){let r=e;if(t&&Object.keys(t).length>0){const s=new URLSearchParams;Object.entries(t).forEach(([o,i])=>{i!=null&&s.append(o,String(i))}),r+=`?${s.toString()}`}return Jn(r,{...n,method:"GET"})}async function Gb(e,t,n){return Jn(e,{...n,method:"POST",body:t?JSON.stringify(t):void 0})}async function Yb(e,t,n){return Jn(e,{...n,method:"PUT",body:t?JSON.stringify(t):void 0})}async function Jb(e,t){return Jn(e,{...t,method:"DELETE"})}class Ks{static async getList(t,n,r){const s=`/${t}/${n}.json`;return Xa(s,r)}static async getById(t,n,r){const s=`/${t}/${n}/${r}.json`;return Xa(s)}static async create(t,n,r){const s=`/${t}/${n}`;return Gb(s,r)}static async update(t,n,r,s){const o=`/${t}/${n}/${r}`;return Yb(o,s)}static async delete(t,n,r){const s=`/${t}/${n}/${r}`;return Jb(s)}}class bo extends Ks{static async getUsers(t){return this.getList("common","users",t)}static async getRoles(t){return this.getList("common","roles",t)}static async getUserById(t){return this.getById("common","users",t)}static async getRoleById(t){return this.getById("common","roles",t)}}class Xb extends Ks{static async getMaterialCategories(t){return this.getList("metadata","materialCategory",t)}static async getMaterials(t){return this.getList("metadata","material",t)}static async getMaterialById(t){return this.getById("metadata","material",t)}static async getMaterialsByCategory(t,n){return this.getList("metadata","material",{...n,categoryId:t})}}class Qa extends Ks{static async getCustomers(t){return this.getList("crm","customers",t)}static async getCustomerById(t){return this.getById("crm","customers",t)}static async getOrders(t){return this.getList("crm","orders",t)}static async getOrderById(t){return this.getById("crm","orders",t)}static async getOrdersByCustomer(t,n){return this.getList("crm","orders",{...n,customerId:t})}}class Qb extends Ks{static async getStock(t){return this.getList("inventory","stock",t)}static async getStockById(t){return this.getById("inventory","stock",t)}static async getStockByMaterial(t,n){return this.getList("inventory","stock",{...n,materialId:t})}static async getLowStockItems(t){return this.getList("inventory","stock",{...t,lowStock:!0})}}const Kc=gi("user",()=>{const e=L(!1),t=L(null),n=L([]),r=L(null),s=L(!1),o=L(null),i=H(()=>{if(!n.value.length)return[];if(n.value.some(x=>x.code==="admin"))return["*"];const b=n.value.reduce((x,R)=>[...x,...R.permissions],[]);return[...new Set(b)]}),a=H(()=>t.value?{id:t.value.id,name:t.value.name,username:t.value.username,email:t.value.email,avatar:t.value.avatar,department:t.value.department,roles:t.value.roles,roleNames:n.value.map(b=>b.name)}:null),l=H(()=>t.value?.roles.includes("admin")||!1),c=async b=>{try{s.value=!0,o.value=null,await new Promise(k=>setTimeout(k,1e3));const x=await bo.getUsers();if(!x.success||!x.data)throw new Error("无法加载用户数据");const R=x.data.find(k=>k.username===b.username);if(!R)throw new Error("用户名或密码错误");if(!R.isActive)throw new Error("用户账户已被禁用");const N=await bo.getRoles();if(!N.success||!N.data)throw new Error("无法加载角色数据");const F=N.data.filter(k=>R.roles.includes(k.code));return t.value=R,n.value=F,e.value=!0,r.value=`mock_token_${R.id}_${Date.now()}`,m(),!0}catch(x){return o.value=x instanceof Error?x.message:"登录失败",!1}finally{s.value=!1}},u=async()=>{try{s.value=!0,await new Promise(b=>setTimeout(b,500)),_(),C()}finally{s.value=!1}},d=(b,x={})=>{if(!e.value||!t.value)return!1;if(l.value||i.value.includes("*"))return!0;const R=Array.isArray(b)?b:[b];return x.requireAll?R.every(N=>i.value.includes(N)):R.some(N=>i.value.includes(N))},p=b=>!e.value||!t.value?!1:(Array.isArray(b)?b:[b]).some(R=>t.value.roles.includes(R)),h=async()=>{if(!t.value)return!1;try{s.value=!0;const b=await bo.getUserById(t.value.id);return b.success&&b.data?(t.value=b.data,m(),!0):!1}catch(b){return o.value=b instanceof Error?b.message:"刷新用户信息失败",!1}finally{s.value=!1}},m=()=>{if(typeof window>"u")return;const b={isAuthenticated:e.value,currentUser:t.value,userRoles:n.value,token:r.value,timestamp:Date.now()};localStorage.setItem("auth_state",JSON.stringify(b))},y=()=>{if(typeof window>"u")return!1;try{const b=localStorage.getItem("auth_state");if(!b)return!1;const x=JSON.parse(b);return Date.now()-x.timestamp>1440*60*1e3?(C(),!1):(e.value=x.isAuthenticated,t.value=x.currentUser,n.value=x.userRoles,r.value=x.token,!0)}catch(b){return console.error("恢复认证状态失败:",b),C(),!1}},_=()=>{e.value=!1,t.value=null,n.value=[],r.value=null,o.value=null},C=()=>{typeof window>"u"||localStorage.removeItem("auth_state")};return{isAuthenticated:e,currentUser:t,userRoles:n,token:r,isLoading:s,error:o,permissions:i,userInfo:a,isAdmin:l,login:c,logout:u,hasPermission:d,hasRole:p,refreshUserInfo:h,restoreAuthState:y,clearError:()=>{o.value=null}}}),Gc=I({__name:"Avatar",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),z(f(Wm),{"data-slot":"avatar",class:Ee(f(he)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t.class))},{default:w(()=>[q(n.$slots,"default")]),_:3},8,["class"]))}}),Yc=I({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(Gm),ue({"data-slot":"avatar-fallback"},f(n),{class:f(he)("bg-muted flex size-full items-center justify-center rounded-full",t.class)}),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),Jc=I({__name:"AvatarImage",props:{src:{},referrerPolicy:{},crossOrigin:{},asChild:{type:Boolean},as:{}},setup(e){const t=e;return(n,r)=>(T(),z(f(Xm),ue({"data-slot":"avatar-image"},t,{class:"aspect-square size-full"}),{default:w(()=>[q(n.$slots,"default")]),_:3},16))}}),Zb={class:"flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground"},e_={class:"grid flex-1 text-left text-sm leading-tight"},t_={class:"truncate font-semibold"},n_={class:"truncate text-xs"},r_=I({__name:"AppSidebar",setup(e){const t=Kc(),n=[{title:"仪表盘",url:"/dashboard",icon:ib,roles:["admin","enterprise_manager","sales_engineer","production_scheduler","quality_engineer","warehouse_manager","procurement_manager","finance_manager","production_supervisor"]},{title:"客户关系",url:"/crm",icon:mb,roles:["admin","enterprise_manager","sales_engineer","finance_manager"]},{title:"库存管理",url:"/inventory",icon:ub,roles:["admin","enterprise_manager","warehouse_manager","procurement_manager","production_scheduler"]},{title:"生产执行",url:"/mes",icon:Vc,roles:["admin","enterprise_manager","production_scheduler","production_supervisor","cutting_operator","tempering_operator","laminating_operator"]},{title:"采购管理",url:"/procurement",icon:Uc,roles:["admin","enterprise_manager","procurement_manager","warehouse_manager"]},{title:"质量管理",url:"/quality",icon:rb,roles:["admin","enterprise_manager","quality_engineer","production_supervisor"]}],r=[{title:"用户管理",url:"/admin/users",icon:pb,roles:["admin"]},{title:"元数据管理",url:"/admin/metadata",icon:sb,roles:["admin"]},{title:"系统设置",url:"/admin/settings",icon:Hc,roles:["admin"]}],s=H(()=>t.currentUser?n.filter(i=>i.roles.some(a=>t.currentUser?.roles.includes(a))):[]),o=H(()=>t.isAdmin);return(i,a)=>{const l=ci("router-link");return T(),z(f(Mb),{variant:"inset"},{default:w(()=>[E(f($b),null,{default:w(()=>[E(f(es),null,{default:w(()=>[E(f(ns),null,{default:w(()=>[E(f(ts),{size:"lg","as-child":""},{default:w(()=>[E(l,{to:"/",class:"flex items-center gap-2"},{default:w(()=>[oe("div",Zb,[E(f(Zy),{class:"size-4"})]),a[0]||(a[0]=oe("div",{class:"grid flex-1 text-left text-sm leading-tight"},[oe("span",{class:"truncate font-semibold"},"玻璃ERP系统"),oe("span",{class:"truncate text-xs"},"Glass Manufacturing")],-1))]),_:1,__:[0]})]),_:1})]),_:1})]),_:1})]),_:1}),E(f(Db),null,{default:w(()=>[E(f(Ka),null,{default:w(()=>[E(f(Ya),null,{default:w(()=>a[1]||(a[1]=[$e("主要功能",-1)])),_:1,__:[1]}),E(f(Ga),null,{default:w(()=>[E(f(es),null,{default:w(()=>[(T(!0),be(Ue,null,Po(s.value,c=>(T(),z(f(ns),{key:c.title},{default:w(()=>[E(f(ts),{"as-child":"",tooltip:c.title},{default:w(()=>[E(l,{to:c.url,class:"flex items-center gap-2"},{default:w(()=>[(T(),z(Ar(c.icon),{class:"size-4"})),oe("span",null,Ve(c.title),1)]),_:2},1032,["to"])]),_:2},1032,["tooltip"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1}),o.value?(T(),z(f(Ka),{key:0},{default:w(()=>[E(f(Ya),null,{default:w(()=>a[2]||(a[2]=[$e("系统管理",-1)])),_:1,__:[2]}),E(f(Ga),null,{default:w(()=>[E(f(es),null,{default:w(()=>[(T(),be(Ue,null,Po(r,c=>E(f(ns),{key:c.title},{default:w(()=>[E(f(ts),{"as-child":"",tooltip:c.title},{default:w(()=>[E(l,{to:c.url,class:"flex items-center gap-2"},{default:w(()=>[(T(),z(Ar(c.icon),{class:"size-4"})),oe("span",null,Ve(c.title),1)]),_:2},1032,["to"])]),_:2},1032,["tooltip"])]),_:2},1024)),64))]),_:1})]),_:1})]),_:1})):qr("",!0)]),_:1}),E(f(Ib),null,{default:w(()=>[E(f(es),null,{default:w(()=>[E(f(ns),null,{default:w(()=>[E(f(ts),{size:"lg",class:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"},{default:w(()=>[E(f(Gc),{class:"h-8 w-8 rounded-lg"},{default:w(()=>[E(f(Jc),{src:f(t).currentUser?.avatar||"",alt:f(t).currentUser?.name||""},null,8,["src","alt"]),E(f(Yc),{class:"rounded-lg"},{default:w(()=>[$e(Ve(f(t).currentUser?.name?.charAt(0)||"U"),1)]),_:1})]),_:1}),oe("div",e_,[oe("span",t_,Ve(f(t).currentUser?.name||"未登录"),1),oe("span",n_,Ve(f(t).currentUser?.department||""),1)]),E(f(tb),{class:"ml-auto size-4"})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}});/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const jn=typeof document<"u";function Xc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function s_(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Xc(e.default)}const Ae=Object.assign;function _o(e,t){const n={};for(const r in t){const s=t[r];n[r]=Et(s)?s.map(e):e(s)}return n}const wr=()=>{},Et=Array.isArray,Qc=/#/g,o_=/&/g,i_=/\//g,a_=/=/g,l_=/\?/g,Zc=/\+/g,u_=/%5B/g,c_=/%5D/g,ed=/%5E/g,d_=/%60/g,td=/%7B/g,f_=/%7C/g,nd=/%7D/g,p_=/%20/g;function $i(e){return encodeURI(""+e).replace(f_,"|").replace(u_,"[").replace(c_,"]")}function h_(e){return $i(e).replace(td,"{").replace(nd,"}").replace(ed,"^")}function Xo(e){return $i(e).replace(Zc,"%2B").replace(p_,"+").replace(Qc,"%23").replace(o_,"%26").replace(d_,"`").replace(td,"{").replace(nd,"}").replace(ed,"^")}function m_(e){return Xo(e).replace(a_,"%3D")}function g_(e){return $i(e).replace(Qc,"%23").replace(l_,"%3F")}function v_(e){return e==null?"":g_(e).replace(i_,"%2F")}function Dr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const y_=/\/$/,b_=e=>e.replace(y_,"");function wo(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=C_(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:Dr(i)}}function __(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Za(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function w_(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Xn(t.matched[r],n.matched[s])&&rd(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Xn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function rd(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!x_(e[n],t[n]))return!1;return!0}function x_(e,t){return Et(e)?el(e,t):Et(t)?el(t,e):e===t}function el(e,t){return Et(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function C_(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const sn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ir;(function(e){e.pop="pop",e.push="push"})(Ir||(Ir={}));var xr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(xr||(xr={}));function S_(e){if(!e)if(jn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),b_(e)}const E_=/^[^#]+#/;function A_(e,t){return e.replace(E_,"#")+t}function O_(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Gs=()=>({left:window.scrollX,top:window.scrollY});function k_(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=O_(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function tl(e,t){return(history.state?history.state.position-t:-1)+e}const Qo=new Map;function P_(e,t){Qo.set(e,t)}function R_(e){const t=Qo.get(e);return Qo.delete(e),t}let T_=()=>location.protocol+"//"+location.host;function sd(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let a=s.includes(e.slice(o))?e.slice(o).length:1,l=s.slice(a);return l[0]!=="/"&&(l="/"+l),Za(l,"")}return Za(n,e)+r+s}function M_(e,t,n,r){let s=[],o=[],i=null;const a=({state:p})=>{const h=sd(e,location),m=n.value,y=t.value;let _=0;if(p){if(n.value=h,t.value=p,i&&i===m){i=null;return}_=y?p.position-y.position:0}else r(h);s.forEach(C=>{C(n.value,m,{delta:_,type:Ir.pop,direction:_?_>0?xr.forward:xr.back:xr.unknown})})};function l(){i=n.value}function c(p){s.push(p);const h=()=>{const m=s.indexOf(p);m>-1&&s.splice(m,1)};return o.push(h),h}function u(){const{history:p}=window;p.state&&p.replaceState(Ae({},p.state,{scroll:Gs()}),"")}function d(){for(const p of o)p();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:d}}function nl(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Gs():null}}function D_(e){const{history:t,location:n}=window,r={value:sd(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,c,u){const d=e.indexOf("#"),p=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+l:T_()+e+l;try{t[u?"replaceState":"pushState"](c,"",p),s.value=c}catch(h){console.error(h),n[u?"replace":"assign"](p)}}function i(l,c){const u=Ae({},t.state,nl(s.value.back,l,s.value.forward,!0),c,{position:s.value.position});o(l,u,!0),r.value=l}function a(l,c){const u=Ae({},s.value,t.state,{forward:l,scroll:Gs()});o(u.current,u,!0);const d=Ae({},nl(r.value,l,null),{position:u.position+1},c);o(l,d,!1),r.value=l}return{location:r,state:s,push:a,replace:i}}function I_(e){e=S_(e);const t=D_(e),n=M_(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=Ae({location:"",base:e,go:r,createHref:A_.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function $_(e){return typeof e=="string"||e&&typeof e=="object"}function od(e){return typeof e=="string"||typeof e=="symbol"}const id=Symbol("");var rl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(rl||(rl={}));function Qn(e,t){return Ae(new Error,{type:e,[id]:!0},t)}function Ht(e,t){return e instanceof Error&&id in e&&(t==null||!!(e.type&t))}const sl="[^/]+?",B_={sensitive:!1,strict:!1,start:!0,end:!0},q_=/[.+*?^${}()[\]/\\]/g;function L_(e,t){const n=Ae({},B_,t),r=[];let s=n.start?"^":"";const o=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(s+="/");for(let d=0;d<c.length;d++){const p=c[d];let h=40+(n.sensitive?.25:0);if(p.type===0)d||(s+="/"),s+=p.value.replace(q_,"\\$&"),h+=40;else if(p.type===1){const{value:m,repeatable:y,optional:_,regexp:C}=p;o.push({name:m,repeatable:y,optional:_});const O=C||sl;if(O!==sl){h+=10;try{new RegExp(`(${O})`)}catch(x){throw new Error(`Invalid custom RegExp for param "${m}" (${O}): `+x.message)}}let b=y?`((?:${O})(?:/(?:${O}))*)`:`(${O})`;d||(b=_&&c.length<2?`(?:/${b})`:"/"+b),_&&(b+="?"),s+=b,h+=20,_&&(h+=-8),y&&(h+=-20),O===".*"&&(h+=-50)}u.push(h)}r.push(u)}if(n.strict&&n.end){const c=r.length-1;r[c][r[c].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function a(c){const u=c.match(i),d={};if(!u)return null;for(let p=1;p<u.length;p++){const h=u[p]||"",m=o[p-1];d[m.name]=h&&m.repeatable?h.split("/"):h}return d}function l(c){let u="",d=!1;for(const p of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const h of p)if(h.type===0)u+=h.value;else if(h.type===1){const{value:m,repeatable:y,optional:_}=h,C=m in c?c[m]:"";if(Et(C)&&!y)throw new Error(`Provided param "${m}" is an array but it is not repeatable (* or + modifiers)`);const O=Et(C)?C.join("/"):C;if(!O)if(_)p.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${m}"`);u+=O}}return u||"/"}return{re:i,score:r,keys:o,parse:a,stringify:l}}function N_(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ad(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=N_(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(ol(r))return 1;if(ol(s))return-1}return s.length-r.length}function ol(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const F_={type:0,value:""},j_=/[a-zA-Z0-9_]/;function z_(e){if(!e)return[[]];if(e==="/")return[[F_]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${c}": ${h}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a=0,l,c="",u="";function d(){c&&(n===0?o.push({type:0,value:c}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function p(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(c&&d(),i()):l===":"?(d(),n=1):p();break;case 4:p(),n=r;break;case 1:l==="("?n=2:j_.test(l)?p():(d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),d(),i(),s}function V_(e,t,n){const r=L_(z_(e.path),n),s=Ae(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function H_(e,t){const n=[],r=new Map;t=ul({strict:!1,end:!0,sensitive:!1},t);function s(d){return r.get(d)}function o(d,p,h){const m=!h,y=al(d);y.aliasOf=h&&h.record;const _=ul(t,d),C=[y];if("alias"in d){const x=typeof d.alias=="string"?[d.alias]:d.alias;for(const R of x)C.push(al(Ae({},y,{components:h?h.record.components:y.components,path:R,aliasOf:h?h.record:y})))}let O,b;for(const x of C){const{path:R}=x;if(p&&R[0]!=="/"){const N=p.record.path,F=N[N.length-1]==="/"?"":"/";x.path=p.record.path+(R&&F+R)}if(O=V_(x,p,_),h?h.alias.push(O):(b=b||O,b!==O&&b.alias.push(O),m&&d.name&&!ll(O)&&i(d.name)),ld(O)&&l(O),y.children){const N=y.children;for(let F=0;F<N.length;F++)o(N[F],O,h&&h.children[F])}h=h||O}return b?()=>{i(b)}:wr}function i(d){if(od(d)){const p=r.get(d);p&&(r.delete(d),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(d);p>-1&&(n.splice(p,1),d.record.name&&r.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function a(){return n}function l(d){const p=K_(d,n);n.splice(p,0,d),d.record.name&&!ll(d)&&r.set(d.record.name,d)}function c(d,p){let h,m={},y,_;if("name"in d&&d.name){if(h=r.get(d.name),!h)throw Qn(1,{location:d});_=h.record.name,m=Ae(il(p.params,h.keys.filter(b=>!b.optional).concat(h.parent?h.parent.keys.filter(b=>b.optional):[]).map(b=>b.name)),d.params&&il(d.params,h.keys.map(b=>b.name))),y=h.stringify(m)}else if(d.path!=null)y=d.path,h=n.find(b=>b.re.test(y)),h&&(m=h.parse(y),_=h.record.name);else{if(h=p.name?r.get(p.name):n.find(b=>b.re.test(p.path)),!h)throw Qn(1,{location:d,currentLocation:p});_=h.record.name,m=Ae({},p.params,d.params),y=h.stringify(m)}const C=[];let O=h;for(;O;)C.unshift(O.record),O=O.parent;return{name:_,path:y,params:m,matched:C,meta:W_(C)}}e.forEach(d=>o(d));function u(){n.length=0,r.clear()}return{addRoute:o,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:a,getRecordMatcher:s}}function il(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function al(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:U_(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function U_(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function ll(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function W_(e){return e.reduce((t,n)=>Ae(t,n.meta),{})}function ul(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function K_(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;ad(e,t[o])<0?r=o:n=o+1}const s=G_(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function G_(e){let t=e;for(;t=t.parent;)if(ld(t)&&ad(e,t)===0)return t}function ld({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Y_(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(Zc," "),i=o.indexOf("="),a=Dr(i<0?o:o.slice(0,i)),l=i<0?null:Dr(o.slice(i+1));if(a in t){let c=t[a];Et(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function cl(e){let t="";for(let n in e){const r=e[n];if(n=m_(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Et(r)?r.map(o=>o&&Xo(o)):[r&&Xo(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function J_(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Et(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const X_=Symbol(""),dl=Symbol(""),Ys=Symbol(""),Bi=Symbol(""),Zo=Symbol("");function ur(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function cn(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,l)=>{const c=p=>{p===!1?l(Qn(4,{from:n,to:t})):p instanceof Error?l(p):$_(p)?l(Qn(2,{from:t,to:p})):(i&&r.enterCallbacks[s]===i&&typeof p=="function"&&i.push(p),a())},u=o(()=>e.call(r&&r.instances[s],t,n,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(p=>l(p))})}function xo(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(Xc(l)){const u=(l.__vccOpts||l)[t];u&&o.push(cn(u,n,r,i,a,s))}else{let c=l();o.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const d=s_(u)?u.default:u;i.mods[a]=u,i.components[a]=d;const h=(d.__vccOpts||d)[t];return h&&cn(h,n,r,i,a,s)()}))}}return o}function fl(e){const t=nt(Ys),n=nt(Bi),r=H(()=>{const l=f(e.to);return t.resolve(l)}),s=H(()=>{const{matched:l}=r.value,{length:c}=l,u=l[c-1],d=n.matched;if(!u||!d.length)return-1;const p=d.findIndex(Xn.bind(null,u));if(p>-1)return p;const h=pl(l[c-2]);return c>1&&pl(u)===h&&d[d.length-1].path!==h?d.findIndex(Xn.bind(null,l[c-2])):p}),o=H(()=>s.value>-1&&nw(n.params,r.value.params)),i=H(()=>s.value>-1&&s.value===n.matched.length-1&&rd(n.params,r.value.params));function a(l={}){if(tw(l)){const c=t[f(e.replace)?"replace":"push"](f(e.to)).catch(wr);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:r,href:H(()=>r.value.href),isActive:o,isExactActive:i,navigate:a}}function Q_(e){return e.length===1?e[0]:e}const Z_=I({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:fl,setup(e,{slots:t}){const n=Xt(fl(e)),{options:r}=nt(Ys),s=H(()=>({[hl(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[hl(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Q_(t.default(n));return e.custom?o:_t("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),ew=Z_;function tw(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function nw(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Et(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function pl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const hl=(e,t,n)=>e??t??n,rw=I({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=nt(Zo),s=H(()=>e.route||r.value),o=nt(dl,0),i=H(()=>{let c=f(o);const{matched:u}=s.value;let d;for(;(d=u[c])&&!d.components;)c++;return c}),a=H(()=>s.value.matched[i.value]);Wn(dl,H(()=>i.value+1)),Wn(X_,a),Wn(Zo,s);const l=L();return qe(()=>[l.value,a.value,e.name],([c,u,d],[p,h,m])=>{u&&(u.instances[d]=c,h&&h!==u&&c&&c===p&&(u.leaveGuards.size||(u.leaveGuards=h.leaveGuards),u.updateGuards.size||(u.updateGuards=h.updateGuards))),c&&u&&(!h||!Xn(u,h)||!p)&&(u.enterCallbacks[d]||[]).forEach(y=>y(c))},{flush:"post"}),()=>{const c=s.value,u=e.name,d=a.value,p=d&&d.components[u];if(!p)return ml(n.default,{Component:p,route:c});const h=d.props[u],m=h?h===!0?c.params:typeof h=="function"?h(c):h:null,_=_t(p,Ae({},m,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(d.instances[u]=null)},ref:l}));return ml(n.default,{Component:_,route:c})||_}}});function ml(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const sw=rw;function ow(e){const t=H_(e.routes,e),n=e.parseQuery||Y_,r=e.stringifyQuery||cl,s=e.history,o=ur(),i=ur(),a=ur(),l=Gt(sn);let c=sn;jn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=_o.bind(null,P=>""+P),d=_o.bind(null,v_),p=_o.bind(null,Dr);function h(P,Z){let G,re;return od(P)?(G=t.getRecordMatcher(P),re=Z):re=P,t.addRoute(re,G)}function m(P){const Z=t.getRecordMatcher(P);Z&&t.removeRoute(Z)}function y(){return t.getRoutes().map(P=>P.record)}function _(P){return!!t.getRecordMatcher(P)}function C(P,Z){if(Z=Ae({},Z||l.value),typeof P=="string"){const S=wo(n,P,Z.path),M=t.resolve({path:S.path},Z),$=s.createHref(S.fullPath);return Ae(S,M,{params:p(M.params),hash:Dr(S.hash),redirectedFrom:void 0,href:$})}let G;if(P.path!=null)G=Ae({},P,{path:wo(n,P.path,Z.path).path});else{const S=Ae({},P.params);for(const M in S)S[M]==null&&delete S[M];G=Ae({},P,{params:d(S)}),Z.params=d(Z.params)}const re=t.resolve(G,Z),Se=P.hash||"";re.params=u(p(re.params));const g=__(r,Ae({},P,{hash:h_(Se),path:re.path})),v=s.createHref(g);return Ae({fullPath:g,hash:Se,query:r===cl?J_(P.query):P.query||{}},re,{redirectedFrom:void 0,href:v})}function O(P){return typeof P=="string"?wo(n,P,l.value.path):Ae({},P)}function b(P,Z){if(c!==P)return Qn(8,{from:Z,to:P})}function x(P){return F(P)}function R(P){return x(Ae(O(P),{replace:!0}))}function N(P){const Z=P.matched[P.matched.length-1];if(Z&&Z.redirect){const{redirect:G}=Z;let re=typeof G=="function"?G(P):G;return typeof re=="string"&&(re=re.includes("?")||re.includes("#")?re=O(re):{path:re},re.params={}),Ae({query:P.query,hash:P.hash,params:re.path!=null?{}:P.params},re)}}function F(P,Z){const G=c=C(P),re=l.value,Se=P.state,g=P.force,v=P.replace===!0,S=N(G);if(S)return F(Ae(O(S),{state:typeof S=="object"?Ae({},Se,S.state):Se,force:g,replace:v}),Z||G);const M=G;M.redirectedFrom=Z;let $;return!g&&w_(r,re,G)&&($=Qn(16,{to:M,from:re}),de(re,re,!0,!1)),($?Promise.resolve($):Y(M,re)).catch(D=>Ht(D)?Ht(D,2)?D:X(D):B(D,M,re)).then(D=>{if(D){if(Ht(D,2))return F(Ae({replace:v},O(D.to),{state:typeof D.to=="object"?Ae({},Se,D.to.state):Se,force:g}),Z||M)}else D=le(M,re,!0,v,Se);return se(M,re,D),D})}function k(P,Z){const G=b(P,Z);return G?Promise.reject(G):Promise.resolve()}function A(P){const Z=je.values().next().value;return Z&&typeof Z.runWithContext=="function"?Z.runWithContext(P):P()}function Y(P,Z){let G;const[re,Se,g]=iw(P,Z);G=xo(re.reverse(),"beforeRouteLeave",P,Z);for(const S of re)S.leaveGuards.forEach(M=>{G.push(cn(M,P,Z))});const v=k.bind(null,P,Z);return G.push(v),Qe(G).then(()=>{G=[];for(const S of o.list())G.push(cn(S,P,Z));return G.push(v),Qe(G)}).then(()=>{G=xo(Se,"beforeRouteUpdate",P,Z);for(const S of Se)S.updateGuards.forEach(M=>{G.push(cn(M,P,Z))});return G.push(v),Qe(G)}).then(()=>{G=[];for(const S of g)if(S.beforeEnter)if(Et(S.beforeEnter))for(const M of S.beforeEnter)G.push(cn(M,P,Z));else G.push(cn(S.beforeEnter,P,Z));return G.push(v),Qe(G)}).then(()=>(P.matched.forEach(S=>S.enterCallbacks={}),G=xo(g,"beforeRouteEnter",P,Z,A),G.push(v),Qe(G))).then(()=>{G=[];for(const S of i.list())G.push(cn(S,P,Z));return G.push(v),Qe(G)}).catch(S=>Ht(S,8)?S:Promise.reject(S))}function se(P,Z,G){a.list().forEach(re=>A(()=>re(P,Z,G)))}function le(P,Z,G,re,Se){const g=b(P,Z);if(g)return g;const v=Z===sn,S=jn?history.state:{};G&&(re||v?s.replace(P.fullPath,Ae({scroll:v&&S&&S.scroll},Se)):s.push(P.fullPath,Se)),l.value=P,de(P,Z,G,v),X()}let ge;function ve(){ge||(ge=s.listen((P,Z,G)=>{if(!Ft.listening)return;const re=C(P),Se=N(re);if(Se){F(Ae(Se,{replace:!0,force:!0}),re).catch(wr);return}c=re;const g=l.value;jn&&P_(tl(g.fullPath,G.delta),Gs()),Y(re,g).catch(v=>Ht(v,12)?v:Ht(v,2)?(F(Ae(O(v.to),{force:!0}),re).then(S=>{Ht(S,20)&&!G.delta&&G.type===Ir.pop&&s.go(-1,!1)}).catch(wr),Promise.reject()):(G.delta&&s.go(-G.delta,!1),B(v,re,g))).then(v=>{v=v||le(re,g,!1),v&&(G.delta&&!Ht(v,8)?s.go(-G.delta,!1):G.type===Ir.pop&&Ht(v,20)&&s.go(-1,!1)),se(re,g,v)}).catch(wr)}))}let xe=ur(),V=ur(),U;function B(P,Z,G){X(P);const re=V.list();return re.length?re.forEach(Se=>Se(P,Z,G)):console.error(P),Promise.reject(P)}function Ne(){return U&&l.value!==sn?Promise.resolve():new Promise((P,Z)=>{xe.add([P,Z])})}function X(P){return U||(U=!P,ve(),xe.list().forEach(([Z,G])=>P?G(P):Z()),xe.reset()),P}function de(P,Z,G,re){const{scrollBehavior:Se}=e;if(!jn||!Se)return Promise.resolve();const g=!G&&R_(tl(P.fullPath,0))||(re||!G)&&history.state&&history.state.scroll||null;return Ke().then(()=>Se(P,Z,g)).then(v=>v&&k_(v)).catch(v=>B(v,P,Z))}const ne=P=>s.go(P);let ye;const je=new Set,Ft={currentRoute:l,listening:!0,addRoute:h,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:y,resolve:C,options:e,push:x,replace:R,go:ne,back:()=>ne(-1),forward:()=>ne(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:V.add,isReady:Ne,install(P){const Z=this;P.component("RouterLink",ew),P.component("RouterView",sw),P.config.globalProperties.$router=Z,Object.defineProperty(P.config.globalProperties,"$route",{enumerable:!0,get:()=>f(l)}),jn&&!ye&&l.value===sn&&(ye=!0,x(s.location).catch(Se=>{}));const G={};for(const Se in sn)Object.defineProperty(G,Se,{get:()=>l.value[Se],enumerable:!0});P.provide(Ys,Z),P.provide(Bi,Wl(G)),P.provide(Zo,l);const re=P.unmount;je.add(P),P.unmount=function(){je.delete(P),je.size<1&&(c=sn,ge&&ge(),ge=null,l.value=sn,ye=!1,U=!1),re()}}};function Qe(P){return P.reduce((Z,G)=>Z.then(()=>A(G)),Promise.resolve())}return Ft}function iw(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(c=>Xn(c,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(c=>Xn(c,l))||s.push(l))}return[n,r,s]}function aw(){return nt(Ys)}function lw(e){return nt(Bi)}const uw=gi("app",()=>{const e=L(!1),t=L("system"),n=L("zh-CN"),r=L("Asia/Shanghai"),s=L(!1),o=L(""),i=L([]),a=L(navigator.onLine),l=H(()=>i.value.filter(V=>!V.read).length),c=H(()=>l.value>0),u=H(()=>t.value==="system"?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":t.value),d=H(()=>({sidebarCollapsed:e.value,theme:t.value,language:n.value,timezone:r.value})),p=()=>{e.value=!e.value,le()},h=V=>{e.value=V,le()},m=V=>{t.value=V,_(),le()},y=()=>{const V=["light","dark","system"],B=(V.indexOf(t.value)+1)%V.length;m(V[B])},_=()=>{const V=document.documentElement;u.value==="dark"?V.classList.add("dark"):V.classList.remove("dark")},C=V=>{n.value=V,le()},O=V=>{r.value=V,le()},b=(V="加载中...")=>{s.value=!0,o.value=V},x=()=>{s.value=!1,o.value=""},R=V=>{const U=`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,B={...V,id:U,createdAt:new Date().toISOString()};return i.value.unshift(B),i.value.length>100&&(i.value=i.value.slice(0,100)),U},N=V=>{const U=i.value.findIndex(B=>B.id===V);U>-1&&i.value.splice(U,1)},F=V=>{const U=i.value.find(B=>B.id===V);U&&(U.read=!0)},k=()=>{i.value.forEach(V=>V.read=!0)},A=()=>{i.value=[]},Y=()=>{i.value=i.value.filter(V=>!V.read)},se=V=>{a.value=V},le=()=>{if(typeof window>"u")return;const V={sidebarCollapsed:e.value,theme:t.value,language:n.value,timezone:r.value,timestamp:Date.now()};localStorage.setItem("app_config",JSON.stringify(V))},ge=()=>{if(typeof window>"u")return!1;try{const V=localStorage.getItem("app_config");if(!V)return!1;const U=JSON.parse(V);return e.value=U.sidebarCollapsed??!1,t.value=U.theme??"system",n.value=U.language??"zh-CN",r.value=U.timezone??"Asia/Shanghai",_(),!0}catch(V){return console.error("恢复应用配置失败:",V),!1}};return{sidebarCollapsed:e,theme:t,language:n,timezone:r,isLoading:s,loadingMessage:o,notifications:i,isOnline:a,unreadNotifications:l,hasUnreadNotifications:c,currentTheme:u,appConfig:d,toggleSidebar:p,setSidebarCollapsed:h,setTheme:m,toggleTheme:y,setLanguage:C,setTimezone:O,showLoading:b,hideLoading:x,addNotification:R,removeNotification:N,markNotificationAsRead:F,markAllNotificationsAsRead:k,clearAllNotifications:A,clearReadNotifications:Y,setOnlineStatus:se,restoreAppConfig:ge,resetAppConfig:()=>{e.value=!1,t.value="system",n.value="zh-CN",r.value="Asia/Shanghai",_(),le()},initializeApp:()=>{ge(),window.addEventListener("online",()=>se(!0)),window.addEventListener("offline",()=>se(!1)),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{t.value==="system"&&_()})}}}),cw=I({__name:"Breadcrumb",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",class:Ee(t.class)},[q(n.$slots,"default")],2))}}),gl=I({__name:"BreadcrumbItem",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("li",{"data-slot":"breadcrumb-item",class:Ee(f(he)("inline-flex items-center gap-1.5",t.class))},[q(n.$slots,"default")],2))}}),dw=I({__name:"BreadcrumbLink",props:{asChild:{type:Boolean},as:{default:"a"},class:{}},setup(e){const t=e;return(n,r)=>(T(),z(f(Ie),{"data-slot":"breadcrumb-link",as:n.as,"as-child":n.asChild,class:Ee(f(he)("hover:text-foreground transition-colors",t.class))},{default:w(()=>[q(n.$slots,"default")]),_:3},8,["as","as-child","class"]))}}),fw=I({__name:"BreadcrumbList",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("ol",{"data-slot":"breadcrumb-list",class:Ee(f(he)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t.class))},[q(n.$slots,"default")],2))}}),pw=I({__name:"BreadcrumbPage",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",class:Ee(f(he)("text-foreground font-normal",t.class))},[q(n.$slots,"default")],2))}}),hw=I({__name:"BreadcrumbSeparator",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",class:Ee(f(he)("[&>svg]:size-3.5",t.class))},[q(n.$slots,"default",{},()=>[E(f(eb))])],2))}}),mw=I({__name:"Badge",props:{asChild:{type:Boolean},as:{},variant:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(Ie),ue({"data-slot":"badge",class:f(he)(f(gw)({variant:r.variant}),t.class)},f(n)),{default:w(()=>[q(r.$slots,"default")]),_:3},16,["class"]))}}),gw=vi("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}}),vl=I({__name:"DropdownMenu",props:{defaultOpen:{type:Boolean},open:{type:Boolean},dir:{},modal:{type:Boolean}},emits:["update:open"],setup(e,{emit:t}){const s=Ot(e,t);return(o,i)=>(T(),z(f(Xv),ue({"data-slot":"dropdown-menu"},f(s)),{default:w(()=>[q(o.$slots,"default")]),_:3},16))}}),yl=I({__name:"DropdownMenuContent",props:{forceMount:{type:Boolean},loop:{type:Boolean},side:{},sideOffset:{default:4},sideFlip:{type:Boolean},align:{},alignOffset:{},alignFlip:{type:Boolean},avoidCollisions:{type:Boolean},collisionBoundary:{},collisionPadding:{},arrowPadding:{},sticky:{},hideWhenDetached:{type:Boolean},positionStrategy:{},updatePositionStrategy:{},disableUpdateOnLayoutShift:{type:Boolean},prioritizePosition:{type:Boolean},reference:{},asChild:{type:Boolean},as:{},class:{}},emits:["escapeKeyDown","pointerDownOutside","focusOutside","interactOutside","closeAutoFocus"],setup(e,{emit:t}){const n=e,r=t,s=vt(n,"class"),o=Ot(s,r);return(i,a)=>(T(),z(f(oy),null,{default:w(()=>[E(f(Zv),ue({"data-slot":"dropdown-menu-content"},f(o),{class:f(he)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--reka-dropdown-menu-content-available-height) min-w-[8rem] origin-(--reka-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",n.class)}),{default:w(()=>[q(i.$slots,"default")]),_:3},16,["class"])]),_:3}))}}),ss=I({__name:"DropdownMenuItem",props:{disabled:{type:Boolean},textValue:{},asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean},variant:{default:"default"}},setup(e){const t=e,n=vt(t,"inset","variant","class"),r=zr(n);return(s,o)=>(T(),z(f(ty),ue({"data-slot":"dropdown-menu-item","data-inset":s.inset?"":void 0,"data-variant":s.variant},f(r),{class:f(he)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t.class)}),{default:w(()=>[q(s.$slots,"default")]),_:3},16,["data-inset","data-variant","class"]))}}),bl=I({__name:"DropdownMenuLabel",props:{asChild:{type:Boolean},as:{},class:{},inset:{type:Boolean}},setup(e){const t=e,n=vt(t,"class","inset"),r=zr(n);return(s,o)=>(T(),z(f(ry),ue({"data-slot":"dropdown-menu-label","data-inset":s.inset?"":void 0},f(r),{class:f(he)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t.class)}),{default:w(()=>[q(s.$slots,"default")]),_:3},16,["data-inset","class"]))}}),os=I({__name:"DropdownMenuSeparator",props:{asChild:{type:Boolean},as:{},class:{}},setup(e){const t=e,n=vt(t,"class");return(r,s)=>(T(),z(f(ay),ue({"data-slot":"dropdown-menu-separator"},f(n),{class:f(he)("bg-border -mx-1 my-1 h-px",t.class)}),null,16,["class"]))}}),_l=I({__name:"DropdownMenuTrigger",props:{disabled:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const n=zr(e);return(r,s)=>(T(),z(f(uy),ue({"data-slot":"dropdown-menu-trigger"},f(n)),{default:w(()=>[q(r.$slots,"default")]),_:3},16))}}),vw={class:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12"},yw={class:"flex items-center gap-2 px-4"},bw={class:"ml-auto flex items-center gap-2 px-4"},_w={class:"relative hidden md:block"},ww={class:"max-h-80 overflow-y-auto"},xw={key:0,class:"p-4 text-center text-sm text-muted-foreground"},Cw={key:1},Sw=["onClick"],Ew={class:"flex-shrink-0"},Aw={class:"flex-1 space-y-1"},Ow={class:"text-sm font-medium"},kw={class:"text-xs text-muted-foreground"},Pw={class:"text-xs text-muted-foreground"},Rw={class:"flex flex-col space-y-1"},Tw={class:"text-sm font-medium leading-none"},Mw={class:"text-xs leading-none text-muted-foreground"},Dw=I({__name:"AppHeader",setup(e){const t=lw(),n=aw(),r=Kc(),s=uw(),o=L(""),i={"/":"首页","/dashboard":"仪表盘","/crm":"客户关系","/inventory":"库存管理","/mes":"生产执行","/procurement":"采购管理","/quality":"质量管理","/admin/users":"用户管理","/admin/metadata":"元数据管理","/admin/settings":"系统设置"},a=H(()=>i[t.path]||"未知页面"),l=H(()=>s.unreadNotifications),c=b=>{switch(b){case"warning":return Wc;case"success":return zc;case"error":return nb;default:return ob}},u=b=>{switch(b){case"warning":return"text-yellow-500";case"success":return"text-green-500";case"error":return"text-red-500";default:return"text-blue-500"}},d=b=>{const x=new Date(b),N=new Date().getTime()-x.getTime(),F=Math.floor(N/(1e3*60)),k=Math.floor(N/(1e3*60*60)),A=Math.floor(N/(1e3*60*60*24));return F<1?"刚刚":F<60?`${F}分钟前`:k<24?`${k}小时前`:A<7?`${A}天前`:x.toLocaleDateString()},p=()=>{o.value.trim()&&n.push(`/search?q=${encodeURIComponent(o.value)}`)},h=b=>{s.markNotificationAsRead(b)},m=()=>{s.markAllNotificationsAsRead()},y=()=>{s.toggleTheme()},_=async()=>{r.restoreAuthState()||await r.login({username:"admin",password:"admin"})},C=async()=>{await r.logout()},O=()=>{s.addNotification({type:"warning",title:"库存预警",message:"6mm透明浮法玻璃库存不足，当前库存：15片",read:!1}),s.addNotification({type:"info",title:"新订单",message:'客户"建筑公司A"提交了新的玻璃加工订单',read:!1}),s.addNotification({type:"success",title:"生产完成",message:"订单#2024001的钢化玻璃生产已完成",read:!0}),s.addNotification({type:"error",title:"设备故障",message:"钢化炉#2出现温度异常，请及时检修",read:!1})};return ht(()=>{s.initializeApp(),_(),O()}),(b,x)=>{const R=ci("router-link");return T(),be("header",vw,[oe("div",yw,[E(f(Hb),{class:"-ml-1"}),E(f(zb),{orientation:"vertical",class:"mr-2 h-4"}),E(f(cw),null,{default:w(()=>[E(f(fw),null,{default:w(()=>[E(f(gl),{class:"hidden md:block"},{default:w(()=>[E(f(dw),{href:"/"},{default:w(()=>x[1]||(x[1]=[$e(" 首页 ",-1)])),_:1,__:[1]})]),_:1}),E(f(hw),{class:"hidden md:block"}),E(f(gl),null,{default:w(()=>[E(f(pw),null,{default:w(()=>[$e(Ve(a.value),1)]),_:1})]),_:1})]),_:1})]),_:1})]),oe("div",bw,[oe("div",_w,[E(f(db),{class:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),E(f(Bb),{type:"search",placeholder:"搜索功能...",class:"w-[200px] pl-8 lg:w-[300px]",modelValue:o.value,"onUpdate:modelValue":x[0]||(x[0]=N=>o.value=N),onKeyup:Lu(p,["enter"])},null,8,["modelValue"])]),E(f(vl),null,{default:w(()=>[E(f(_l),{"as-child":""},{default:w(()=>[E(f(dr),{variant:"outline",size:"icon",class:"relative"},{default:w(()=>[E(f(Qy),{class:"h-4 w-4"}),l.value>0?(T(),z(f(mw),{key:0,class:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs",variant:"destructive"},{default:w(()=>[$e(Ve(l.value>9?"9+":l.value),1)]),_:1})):qr("",!0)]),_:1})]),_:1}),E(f(yl),{align:"end",class:"w-80"},{default:w(()=>[E(f(bl),{class:"flex items-center justify-between"},{default:w(()=>[x[3]||(x[3]=$e(" 通知中心 ",-1)),E(f(dr),{variant:"ghost",size:"sm",onClick:m},{default:w(()=>x[2]||(x[2]=[$e(" 全部已读 ",-1)])),_:1,__:[2]})]),_:1,__:[3]}),E(f(os)),oe("div",ww,[f(s).notifications.length===0?(T(),be("div",xw," 暂无通知 ")):(T(),be("div",Cw,[(T(!0),be(Ue,null,Po(f(s).notifications.slice(0,5),N=>(T(),be("div",{key:N.id,class:Ee(["flex items-start gap-3 p-3 hover:bg-accent cursor-pointer",{"bg-accent/50":!N.read}]),onClick:F=>h(N.id)},[oe("div",Ew,[(T(),z(Ar(c(N.type)),{class:Ee(["h-4 w-4",u(N.type)])},null,8,["class"]))]),oe("div",Aw,[oe("p",Ow,Ve(N.title),1),oe("p",kw,Ve(N.message),1),oe("p",Pw,Ve(d(N.createdAt)),1)])],10,Sw))),128))]))]),E(f(os)),E(f(ss),{"as-child":""},{default:w(()=>[E(R,{to:"/notifications",class:"w-full text-center"},{default:w(()=>x[4]||(x[4]=[$e(" 查看全部通知 ",-1)])),_:1,__:[4]})]),_:1})]),_:1})]),_:1}),E(f(vl),null,{default:w(()=>[E(f(_l),{"as-child":""},{default:w(()=>[E(f(dr),{variant:"outline",size:"icon"},{default:w(()=>[E(f(Gc),{class:"h-8 w-8"},{default:w(()=>[E(f(Jc),{src:f(r).currentUser?.avatar||"",alt:f(r).currentUser?.name||""},null,8,["src","alt"]),E(f(Yc),null,{default:w(()=>[$e(Ve(f(r).currentUser?.name?.charAt(0)||"U"),1)]),_:1})]),_:1})]),_:1})]),_:1}),E(f(yl),{align:"end",class:"w-56"},{default:w(()=>[E(f(bl),{class:"font-normal"},{default:w(()=>[oe("div",Rw,[oe("p",Tw,Ve(f(r).currentUser?.name||"未登录"),1),oe("p",Mw,Ve(f(r).currentUser?.email||""),1)])]),_:1}),E(f(os)),E(f(ss),{"as-child":""},{default:w(()=>[E(R,{to:"/profile",class:"flex items-center"},{default:w(()=>[E(f(hb),{class:"mr-2 h-4 w-4"}),x[5]||(x[5]=$e(" 个人资料 ",-1))]),_:1,__:[5]})]),_:1}),E(f(ss),{"as-child":""},{default:w(()=>[E(R,{to:"/settings",class:"flex items-center"},{default:w(()=>[E(f(Hc),{class:"mr-2 h-4 w-4"}),x[6]||(x[6]=$e(" 设置 ",-1))]),_:1,__:[6]})]),_:1}),E(f(os)),E(f(ss),{onClick:C,class:"text-red-600"},{default:w(()=>[E(f(ab),{class:"mr-2 h-4 w-4"}),x[7]||(x[7]=$e(" 退出登录 ",-1))]),_:1,__:[7]})]),_:1})]),_:1}),E(f(dr),{variant:"outline",size:"icon",onClick:y},{default:w(()=>[E(f(fb),{class:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),E(f(lb),{class:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"})]),_:1})])])}}}),Iw={class:"min-h-screen bg-background"},$w={class:"flex-1 space-y-4 p-4 md:p-6 lg:p-8"},Bw=I({__name:"AppLayout",setup(e){const t=L(window.innerWidth),n=H(()=>t.value<768),r=()=>{t.value=window.innerWidth};return ht(()=>{window.addEventListener("resize",r)}),On(()=>{window.removeEventListener("resize",r)}),(s,o)=>{const i=ci("router-view");return T(),be("div",Iw,[E(f(jb),{"default-open":!n.value},{default:w(()=>[E(r_),E(f(qb),null,{default:w(()=>[E(Dw),oe("main",$w,[E(i)])]),_:1})]),_:1},8,["default-open"])])}}}),qw=I({__name:"App",setup(e){return(t,n)=>(T(),z(Bw))}}),Lw="modulepreload",Nw=function(e){return"/"+e},wl={},wn=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let l=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=i?.nonce||i?.getAttribute("nonce");s=l(n.map(c=>{if(c=Nw(c),c in wl)return;wl[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const p=document.createElement("link");if(p.rel=u?"stylesheet":Lw,u||(p.as="script"),p.crossOrigin="",p.href=c,a&&p.setAttribute("nonce",a),document.head.appendChild(p),u)return new Promise((h,m)=>{p.addEventListener("load",h),p.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return s.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})},Fw=gi("business",()=>{const e=L(null),t=L(null),n=L([]),r=L([]),s=L([]),o=L([]),i=L(!1),a=L(!1),l=L(!1),c=L(!1),u=L(null),d=L(null),p=L(null),h=L(null),m=H(()=>r.value.filter(X=>X.quantity<=X.minStock)),y=H(()=>m.value.length),_=H(()=>s.value.filter(X=>X.isActive)),C=H(()=>n.value.filter(X=>X.status==="draft"||X.status==="confirmed")),O=H(()=>n.value.filter(X=>X.status==="in_production")),b=H(()=>n.value.filter(X=>X.status==="completed")),x=H(()=>{const X={draft:0,confirmed:0,in_production:0,completed:0,cancelled:0};return n.value.forEach(de=>{X[de.status]++}),X}),R=X=>{e.value=X},N=X=>{t.value=X},F=async(X=50)=>{try{i.value=!0,u.value=null;const de=await Qa.getOrders({pageSize:X});if(de.success&&de.data){const ne=de.data.map(ye=>({id:ye.id,customerId:ye.customerId,customerName:ye.customerName,status:ye.status,totalAmount:ye.totalAmount,createdAt:ye.createdAt,updatedAt:ye.updatedAt,items:ye.items||[]}));return n.value=ne,!0}else throw new Error(de.message||"加载订单数据失败")}catch(de){return u.value=de instanceof Error?de.message:"加载订单失败",!1}finally{i.value=!1}},k=async()=>{try{a.value=!0,d.value=null;const X=await Qb.getStock();if(X.success&&X.data){const de=X.data.map(ne=>({id:ne.id,materialId:ne.materialId,materialName:ne.materialName,quantity:ne.quantity,unit:ne.unit,location:ne.location,minStock:ne.minStock||10,maxStock:ne.maxStock||100,lastUpdated:ne.lastUpdated||new Date().toISOString()}));return r.value=de,!0}else throw new Error(X.message||"加载库存数据失败")}catch(X){return d.value=X instanceof Error?X.message:"加载库存失败",!1}finally{a.value=!1}},A=async()=>{try{l.value=!0,p.value=null;const X=await Qa.getCustomers();if(X.success&&X.data){const de=X.data.map(ne=>({id:ne.id,name:ne.name,type:ne.type,industry:ne.industry,contactPerson:ne.contactPerson,phone:ne.phone,email:ne.email,address:ne.address,isActive:ne.isActive!==!1}));return s.value=de,!0}else throw new Error(X.message||"加载客户数据失败")}catch(X){return p.value=X instanceof Error?X.message:"加载客户失败",!1}finally{l.value=!1}},Y=async()=>{try{c.value=!0,h.value=null;const X=await Xb.getMaterials();if(X.success&&X.data){const de=X.data.map(ne=>({id:ne.id,templateId:ne.templateId||ne.id,name:ne.name,category:ne.category,baseAttributes:ne.baseAttributes||{},variantAttributes:ne.variantAttributes||{},specifications:ne.specifications||{},isActive:ne.isActive!==!1,createdAt:ne.createdAt||new Date().toISOString(),updatedAt:ne.updatedAt}));return o.value=de,!0}else throw new Error(X.message||"加载物料数据失败")}catch(X){return h.value=X instanceof Error?X.message:"加载物料失败",!1}finally{c.value=!1}},se=(X,de)=>{const ne=n.value.find(ye=>ye.id===X);return ne?(ne.status=de,ne.updatedAt=new Date().toISOString(),!0):!1},le=X=>{const de=`order_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,ne={...X,id:de,createdAt:new Date().toISOString()};return n.value.unshift(ne),de},ge=(X,de)=>{const ne=r.value.find(ye=>ye.id===X);return ne?(ne.quantity=de,ne.lastUpdated=new Date().toISOString(),!0):!1},ve=()=>{u.value=null,d.value=null,p.value=null,h.value=null},xe=()=>{if(typeof window>"u")return;const X={selectedMaterialVariant:e.value,currentOrder:t.value,recentOrders:n.value,stockItems:r.value,customers:s.value,materialVariants:o.value,timestamp:Date.now()};try{localStorage.setItem("business_data",JSON.stringify(X))}catch(de){console.error("持久化业务数据失败:",de)}},V=()=>{if(typeof window>"u")return!1;try{const X=localStorage.getItem("business_data");if(!X)return!1;const de=JSON.parse(X);return Date.now()-de.timestamp>7200*1e3?(U(),!1):(e.value=de.selectedMaterialVariant,t.value=de.currentOrder,n.value=de.recentOrders||[],r.value=de.stockItems||[],s.value=de.customers||[],o.value=de.materialVariants||[],!0)}catch(X){return console.error("恢复业务数据失败:",X),U(),!1}},U=()=>{typeof window>"u"||localStorage.removeItem("business_data")};return{selectedMaterialVariant:e,currentOrder:t,recentOrders:n,stockItems:r,customers:s,materialVariants:o,isLoadingOrders:i,isLoadingStock:a,isLoadingCustomers:l,isLoadingMaterials:c,ordersError:u,stockError:d,customersError:p,materialsError:h,lowStockItems:m,lowStockCount:y,activeCustomers:_,pendingOrders:C,inProductionOrders:O,completedOrders:b,ordersByStatus:x,setSelectedMaterialVariant:R,setCurrentOrder:N,loadRecentOrders:F,loadStockItems:k,loadCustomers:A,loadMaterials:Y,updateOrderStatus:se,addOrder:le,updateStockQuantity:ge,clearErrors:ve,resetAllData:()=>{e.value=null,t.value=null,n.value=[],r.value=[],s.value=[],o.value=[],ve(),U()},persistBusinessData:xe,restoreBusinessData:V,initializeBusinessData:async()=>{V()||(await Promise.all([F(20),k(),A(),Y()]),xe())}}}),In=I({__name:"Card",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"card",class:Ee(f(he)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t.class))},[q(n.$slots,"default")],2))}}),$n=I({__name:"CardContent",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"card-content",class:Ee(f(he)("px-6",t.class))},[q(n.$slots,"default")],2))}}),Bn=I({__name:"CardHeader",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("div",{"data-slot":"card-header",class:Ee(f(he)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t.class))},[q(n.$slots,"default")],2))}}),qn=I({__name:"CardTitle",props:{class:{}},setup(e){const t=e;return(n,r)=>(T(),be("h3",{"data-slot":"card-title",class:Ee(f(he)("leading-none font-semibold",t.class))},[q(n.$slots,"default")],2))}}),jw={class:"space-y-6"},zw={class:"grid gap-4 md:grid-cols-2 lg:grid-cols-4"},Vw={class:"text-2xl font-bold"},Hw={class:"text-2xl font-bold"},Uw={class:"text-2xl font-bold"},Ww={class:"grid gap-4 md:grid-cols-2 lg:grid-cols-7"},Kw=I({__name:"DashboardView",setup(e){const t=Fw();return ht(async()=>{await t.initializeBusinessData()}),(n,r)=>(T(),be("div",jw,[r[12]||(r[12]=oe("div",{class:"flex items-center justify-between"},[oe("h1",{class:"text-3xl font-bold tracking-tight"},"仪表盘")],-1)),oe("div",zw,[E(f(In),null,{default:w(()=>[E(f(Bn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:w(()=>[E(f(qn),{class:"text-sm font-medium"},{default:w(()=>r[0]||(r[0]=[$e("今日订单",-1)])),_:1,__:[0]}),E(f(Uc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f($n),null,{default:w(()=>[oe("div",Vw,Ve(f(t).recentOrders.length),1),r[1]||(r[1]=oe("p",{class:"text-xs text-muted-foreground"}," 总订单数量 ",-1))]),_:1,__:[1]})]),_:1}),E(f(In),null,{default:w(()=>[E(f(Bn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:w(()=>[E(f(qn),{class:"text-sm font-medium"},{default:w(()=>r[2]||(r[2]=[$e("生产进度",-1)])),_:1,__:[2]}),E(f(Vc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f($n),null,{default:w(()=>[oe("div",Hw,Ve(f(t).inProductionOrders.length),1),r[3]||(r[3]=oe("p",{class:"text-xs text-muted-foreground"}," 生产中订单 ",-1))]),_:1,__:[3]})]),_:1}),E(f(In),null,{default:w(()=>[E(f(Bn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:w(()=>[E(f(qn),{class:"text-sm font-medium"},{default:w(()=>r[4]||(r[4]=[$e("库存预警",-1)])),_:1,__:[4]}),E(f(Wc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f($n),null,{default:w(()=>[oe("div",Uw,Ve(f(t).lowStockCount),1),r[5]||(r[5]=oe("p",{class:"text-xs text-muted-foreground"}," 需要补货的物料 ",-1))]),_:1,__:[5]})]),_:1}),E(f(In),null,{default:w(()=>[E(f(Bn),{class:"flex flex-row items-center justify-between space-y-0 pb-2"},{default:w(()=>[E(f(qn),{class:"text-sm font-medium"},{default:w(()=>r[6]||(r[6]=[$e("质量合格率",-1)])),_:1,__:[6]}),E(f(zc),{class:"h-4 w-4 text-muted-foreground"})]),_:1}),E(f($n),null,{default:w(()=>r[7]||(r[7]=[oe("div",{class:"text-2xl font-bold"},"98.5%",-1),oe("p",{class:"text-xs text-muted-foreground"}," +0.2% 较昨日 ",-1)])),_:1,__:[7]})]),_:1})]),oe("div",Ww,[E(f(In),{class:"col-span-4"},{default:w(()=>[E(f(Bn),null,{default:w(()=>[E(f(qn),null,{default:w(()=>r[8]||(r[8]=[$e("生产概览",-1)])),_:1,__:[8]})]),_:1}),E(f($n),{class:"pl-2"},{default:w(()=>r[9]||(r[9]=[oe("div",{class:"h-[200px] flex items-center justify-center text-muted-foreground"}," 生产数据图表区域 ",-1)])),_:1,__:[9]})]),_:1}),E(f(In),{class:"col-span-3"},{default:w(()=>[E(f(Bn),null,{default:w(()=>[E(f(qn),null,{default:w(()=>r[10]||(r[10]=[$e("最近活动",-1)])),_:1,__:[10]})]),_:1}),E(f($n),null,{default:w(()=>r[11]||(r[11]=[oe("div",{class:"space-y-4"},[oe("div",{class:"flex items-center"},[oe("div",{class:"ml-4 space-y-1"},[oe("p",{class:"text-sm font-medium leading-none"}," 订单 #2024001 已完成生产 "),oe("p",{class:"text-sm text-muted-foreground"}," 2小时前 ")])]),oe("div",{class:"flex items-center"},[oe("div",{class:"ml-4 space-y-1"},[oe("p",{class:"text-sm font-medium leading-none"}," 新客户注册：建筑公司B "),oe("p",{class:"text-sm text-muted-foreground"}," 4小时前 ")])]),oe("div",{class:"flex items-center"},[oe("div",{class:"ml-4 space-y-1"},[oe("p",{class:"text-sm font-medium leading-none"}," 库存补货：6mm透明玻璃 "),oe("p",{class:"text-sm text-muted-foreground"}," 6小时前 ")])])],-1)])),_:1,__:[11]})]),_:1})])]))}}),Gw=ow({history:I_("/"),routes:[{path:"/",redirect:"/dashboard"},{path:"/dashboard",name:"dashboard",component:Kw},{path:"/crm",name:"crm",component:()=>wn(()=>import("./CrmView-DZbhcTa2.js"),__vite__mapDeps([0,1]))},{path:"/inventory",name:"inventory",component:()=>wn(()=>import("./InventoryView---4uNayw.js"),__vite__mapDeps([2,1]))},{path:"/mes",name:"mes",component:()=>wn(()=>import("./MesView-2a1sUtv7.js"),__vite__mapDeps([3,1]))},{path:"/procurement",name:"procurement",component:()=>wn(()=>import("./ProcurementView-crMoVyzN.js"),__vite__mapDeps([4,1]))},{path:"/quality",name:"quality",component:()=>wn(()=>import("./QualityView-BP4ZT7mq.js"),__vite__mapDeps([5,1]))},{path:"/user-test",name:"user-test",component:()=>wn(()=>import("./UserTestView-Bg5Bivgq.js"),[])},{path:"/store-test",name:"store-test",component:()=>wn(()=>import("./StoreTestView-BBxTT930.js"),[])}]}),qi=kp(qw);qi.use(Tp());qi.use(Gw);qi.mount("#app");export{Ue as F,dr as _,oe as a,E as b,be as c,I as d,In as e,$e as f,Bn as g,qn as h,$n as i,Be as j,he as k,Kc as l,Po as m,Ee as n,T as o,qr as p,z as q,q as r,mw as s,Ve as t,f as u,uw as v,w,Fw as x,H as y};
