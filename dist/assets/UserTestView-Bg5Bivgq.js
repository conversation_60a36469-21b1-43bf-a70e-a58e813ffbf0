import{d as v,l as y,c as u,a as s,b as n,w as a,u as t,e as m,g as f,h as _,f as d,i as g,t as i,F as k,m as w,p as C,_ as o,o as r,q as L,s as U}from"./index-NdzTyMCT.js";const $={class:"space-y-6"},V={class:"grid grid-cols-2 gap-4"},B={class:"text-lg"},N={class:"text-lg"},S={class:"text-lg"},j={class:"text-lg"},A={class:"text-lg"},E={class:"text-lg"},F={class:"flex flex-wrap gap-2 mt-2"},P={class:"grid grid-cols-2 gap-4"},R={class:"text-lg"},T={class:"text-lg"},q={class:"text-lg"},z={class:"text-lg"},D={class:"flex gap-4"},I={key:0,class:"p-4 bg-red-50 border border-red-200 rounded-md"},G={class:"text-red-800"},M=v({__name:"UserTestView",setup(H){const l=y(),p=async()=>{await l.login({username:"admin",password:"admin"})},x=async()=>{await l.logout()},b=async()=>{await l.refreshUserInfo()};return(J,e)=>(r(),u("div",$,[e[16]||(e[16]=s("div",{class:"flex items-center justify-between"},[s("h1",{class:"text-3xl font-bold tracking-tight"},"用户状态管理测试")],-1)),n(t(m),null,{default:a(()=>[n(t(f),null,{default:a(()=>[n(t(_),null,{default:a(()=>e[0]||(e[0]=[d("当前用户状态",-1)])),_:1,__:[0]})]),_:1}),n(t(g),{class:"space-y-4"},{default:a(()=>[s("div",V,[s("div",null,[e[1]||(e[1]=s("label",{class:"text-sm font-medium"},"认证状态:",-1)),s("p",B,i(t(l).isAuthenticated?"已登录":"未登录"),1)]),s("div",null,[e[2]||(e[2]=s("label",{class:"text-sm font-medium"},"用户名:",-1)),s("p",N,i(t(l).currentUser?.name||"无"),1)]),s("div",null,[e[3]||(e[3]=s("label",{class:"text-sm font-medium"},"邮箱:",-1)),s("p",S,i(t(l).currentUser?.email||"无"),1)]),s("div",null,[e[4]||(e[4]=s("label",{class:"text-sm font-medium"},"部门:",-1)),s("p",j,i(t(l).currentUser?.department||"无"),1)]),s("div",null,[e[5]||(e[5]=s("label",{class:"text-sm font-medium"},"角色:",-1)),s("p",A,i(t(l).currentUser?.roles.join(", ")||"无"),1)]),s("div",null,[e[6]||(e[6]=s("label",{class:"text-sm font-medium"},"是否管理员:",-1)),s("p",E,i(t(l).isAdmin?"是":"否"),1)])]),s("div",null,[e[7]||(e[7]=s("label",{class:"text-sm font-medium"},"权限列表:",-1)),s("div",F,[(r(!0),u(k,null,w(t(l).permissions,c=>(r(),L(t(U),{key:c,variant:"secondary"},{default:a(()=>[d(i(c),1)]),_:2},1024))),128))])])]),_:1})]),_:1}),n(t(m),null,{default:a(()=>[n(t(f),null,{default:a(()=>[n(t(_),null,{default:a(()=>e[8]||(e[8]=[d("权限测试",-1)])),_:1,__:[8]})]),_:1}),n(t(g),{class:"space-y-4"},{default:a(()=>[s("div",P,[s("div",null,[e[9]||(e[9]=s("label",{class:"text-sm font-medium"},"订单创建权限:",-1)),s("p",R,i(t(l).hasPermission("order.create")?"有权限":"无权限"),1)]),s("div",null,[e[10]||(e[10]=s("label",{class:"text-sm font-medium"},"生产管理权限:",-1)),s("p",T,i(t(l).hasPermission("production.manage")?"有权限":"无权限"),1)]),s("div",null,[e[11]||(e[11]=s("label",{class:"text-sm font-medium"},"管理员角色:",-1)),s("p",q,i(t(l).hasRole("admin")?"是":"否"),1)]),s("div",null,[e[12]||(e[12]=s("label",{class:"text-sm font-medium"},"销售工程师角色:",-1)),s("p",z,i(t(l).hasRole("sales_engineer")?"是":"否"),1)])])]),_:1})]),_:1}),n(t(m),null,{default:a(()=>[n(t(f),null,{default:a(()=>[n(t(_),null,{default:a(()=>e[13]||(e[13]=[d("操作测试",-1)])),_:1,__:[13]})]),_:1}),n(t(g),{class:"space-y-4"},{default:a(()=>[s("div",D,[n(t(o),{onClick:p,disabled:t(l).isLoading},{default:a(()=>[d(i(t(l).isLoading?"登录中...":"测试登录 (admin)"),1)]),_:1},8,["disabled"]),n(t(o),{onClick:x,variant:"outline",disabled:t(l).isLoading},{default:a(()=>[d(i(t(l).isLoading?"登出中...":"测试登出"),1)]),_:1},8,["disabled"]),n(t(o),{onClick:b,variant:"secondary",disabled:t(l).isLoading},{default:a(()=>e[14]||(e[14]=[d(" 刷新用户信息 ",-1)])),_:1,__:[14]},8,["disabled"])]),t(l).error?(r(),u("div",I,[s("p",G,"错误: "+i(t(l).error),1),n(t(o),{onClick:t(l).clearError,size:"sm",variant:"outline",class:"mt-2"},{default:a(()=>e[15]||(e[15]=[d(" 清除错误 ",-1)])),_:1,__:[15]},8,["onClick"])])):C("",!0)]),_:1})]),_:1})]))}});export{M as default};
