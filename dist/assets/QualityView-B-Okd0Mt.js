import{d as _,c as f,a as l,b as t,w as a,u as e,_ as i,e as o,f as n,g as u,h as r,i as d,o as m}from"./index-xR3mVGqH.js";import{P as c,_ as p}from"./CardDescription.vue_vue_type_script_setup_true_lang-BxK2WQlc.js";const x={class:"space-y-6"},$={class:"flex items-center justify-between"},b=_({__name:"QualityView",setup(w){return(g,s)=>(m(),f("div",x,[l("div",$,[s[1]||(s[1]=l("h1",{class:"text-3xl font-bold tracking-tight"},"质量管理",-1)),t(e(i),null,{default:a(()=>[t(e(c),{class:"mr-2 h-4 w-4"}),s[0]||(s[0]=n(" 新建质检记录 ",-1))]),_:1,__:[0]})]),t(e(o),null,{default:a(()=>[t(e(u),null,{default:a(()=>[t(e(r),null,{default:a(()=>s[2]||(s[2]=[n("质量检测",-1)])),_:1,__:[2]}),t(e(p),null,{default:a(()=>s[3]||(s[3]=[n(" 管理产品质量检测和质量控制流程 ",-1)])),_:1,__:[3]})]),_:1}),t(e(d),null,{default:a(()=>s[4]||(s[4]=[l("div",{class:"h-[400px] flex items-center justify-center text-muted-foreground"}," 质量数据表格区域 ",-1)])),_:1,__:[4]})]),_:1})]))}});export{b as default};
