import{d as _,c as f,a as l,b as e,w as a,u as t,_ as i,e as o,f as n,g as u,h as r,i as d,o as m}from"./index-xR3mVGqH.js";import{P as c,_ as p}from"./CardDescription.vue_vue_type_script_setup_true_lang-BxK2WQlc.js";const x={class:"space-y-6"},$={class:"flex items-center justify-between"},k=_({__name:"MesView",setup(w){return(g,s)=>(m(),f("div",x,[l("div",$,[s[1]||(s[1]=l("h1",{class:"text-3xl font-bold tracking-tight"},"生产执行系统",-1)),e(t(i),null,{default:a(()=>[e(t(c),{class:"mr-2 h-4 w-4"}),s[0]||(s[0]=n(" 新建生产任务 ",-1))]),_:1,__:[0]})]),e(t(o),null,{default:a(()=>[e(t(u),null,{default:a(()=>[e(t(r),null,{default:a(()=>s[2]||(s[2]=[n("生产监控",-1)])),_:1,__:[2]}),e(t(p),null,{default:a(()=>s[3]||(s[3]=[n(" 实时监控玻璃深加工生产线状态 ",-1)])),_:1,__:[3]})]),_:1}),e(t(d),null,{default:a(()=>s[4]||(s[4]=[l("div",{class:"h-[400px] flex items-center justify-center text-muted-foreground"}," 生产监控界面区域 ",-1)])),_:1,__:[4]})]),_:1})]))}});export{k as default};
