import{d as _,c as f,a as n,b as e,w as a,u as t,_ as o,e as i,f as l,g as r,h as u,i as d,o as m}from"./index-NdzTyMCT.js";import{P as c,_ as p}from"./CardDescription.vue_vue_type_script_setup_true_lang-Cwfa4Smo.js";const x={class:"space-y-6"},$={class:"flex items-center justify-between"},V=_({__name:"InventoryView",setup(w){return(g,s)=>(m(),f("div",x,[n("div",$,[s[1]||(s[1]=n("h1",{class:"text-3xl font-bold tracking-tight"},"库存管理",-1)),e(t(o),null,{default:a(()=>[e(t(c),{class:"mr-2 h-4 w-4"}),s[0]||(s[0]=l(" 新增物料 ",-1))]),_:1,__:[0]})]),e(t(i),null,{default:a(()=>[e(t(r),null,{default:a(()=>[e(t(u),null,{default:a(()=>s[2]||(s[2]=[l("物料库存",-1)])),_:1,__:[2]}),e(t(p),null,{default:a(()=>s[3]||(s[3]=[l(" 管理玻璃原片、型材等物料的库存信息 ",-1)])),_:1,__:[3]})]),_:1}),e(t(d),null,{default:a(()=>s[4]||(s[4]=[n("div",{class:"h-[400px] flex items-center justify-center text-muted-foreground"}," 库存数据表格区域 ",-1)])),_:1,__:[4]})]),_:1})]))}});export{V as default};
