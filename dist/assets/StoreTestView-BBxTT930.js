import{d as E,l as L,v as B,x as z,y as $,c as f,a as s,b as o,q as v,p as b,w as l,u as t,e as c,g as x,h as g,f as r,i as p,t as n,_ as u,F as N,m as T,o as m}from"./index-NdzTyMCT.js";const V={class:"space-y-6"},I={class:"grid grid-cols-3 gap-4"},O={class:"text-lg"},U={class:"text-lg"},A={class:"text-lg"},j={class:"flex gap-2"},q={class:"grid grid-cols-3 gap-4"},D={class:"text-lg"},F={class:"text-lg"},R={class:"text-lg"},G={class:"flex gap-2"},H={class:"grid grid-cols-4 gap-4"},J={class:"text-lg"},K={class:"text-lg"},M={class:"text-lg text-red-600"},P={class:"text-lg"},Q={class:"flex gap-2"},W={class:"grid grid-cols-5 gap-4"},X={class:"text-center"},Y={class:"text-2xl font-bold"},Z={class:"text-center"},h={class:"text-2xl font-bold"},tt={class:"text-center"},st={class:"text-2xl font-bold"},et={class:"text-center"},lt={class:"text-2xl font-bold"},ot={class:"text-center"},dt={class:"text-2xl font-bold"},nt={class:"space-y-2"},rt={class:"font-medium"},it={class:"text-sm text-muted-foreground"},at={class:"text-right"},ut={class:"text-lg font-bold text-red-600"},mt={class:"text-sm text-muted-foreground"},_t={key:0,class:"p-3 bg-red-50 border border-red-200 rounded-md"},ft={class:"text-red-800"},ct={key:1,class:"p-3 bg-red-50 border border-red-200 rounded-md"},xt={class:"text-red-800"},gt={key:2,class:"p-3 bg-red-50 border border-red-200 rounded-md"},pt={class:"text-red-800"},yt=E({__name:"StoreTestView",setup(bt){const i=L(),_=B(),d=z(),k=$(()=>!!(i.error||d.ordersError||d.stockError||d.customersError)),y=async()=>{await i.login({username:"admin",password:"admin"})},S=async()=>{await i.logout()},C=()=>{_.addNotification({type:"info",title:"测试通知",message:`这是一个测试通知，时间：${new Date().toLocaleTimeString()}`,read:!1})},w=()=>{i.clearError(),d.clearErrors()};return(vt,e)=>(m(),f("div",V,[e[30]||(e[30]=s("div",{class:"flex items-center justify-between"},[s("h1",{class:"text-3xl font-bold tracking-tight"},"状态管理系统测试")],-1)),o(t(c),null,{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),null,{default:l(()=>e[3]||(e[3]=[r("用户状态管理",-1)])),_:1,__:[3]})]),_:1}),o(t(p),{class:"space-y-4"},{default:l(()=>[s("div",I,[s("div",null,[e[4]||(e[4]=s("label",{class:"text-sm font-medium"},"认证状态:",-1)),s("p",O,n(t(i).isAuthenticated?"已登录":"未登录"),1)]),s("div",null,[e[5]||(e[5]=s("label",{class:"text-sm font-medium"},"用户名:",-1)),s("p",U,n(t(i).currentUser?.name||"无"),1)]),s("div",null,[e[6]||(e[6]=s("label",{class:"text-sm font-medium"},"权限数量:",-1)),s("p",A,n(t(i).permissions.length),1)])]),s("div",j,[o(t(u),{onClick:y,size:"sm",disabled:t(i).isLoading},{default:l(()=>e[7]||(e[7]=[r(" 测试登录 ",-1)])),_:1,__:[7]},8,["disabled"]),o(t(u),{onClick:S,size:"sm",variant:"outline",disabled:t(i).isLoading},{default:l(()=>e[8]||(e[8]=[r(" 测试登出 ",-1)])),_:1,__:[8]},8,["disabled"])])]),_:1})]),_:1}),o(t(c),null,{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),null,{default:l(()=>e[9]||(e[9]=[r("应用全局状态",-1)])),_:1,__:[9]})]),_:1}),o(t(p),{class:"space-y-4"},{default:l(()=>[s("div",q,[s("div",null,[e[10]||(e[10]=s("label",{class:"text-sm font-medium"},"侧边栏状态:",-1)),s("p",D,n(t(_).sidebarCollapsed?"收起":"展开"),1)]),s("div",null,[e[11]||(e[11]=s("label",{class:"text-sm font-medium"},"当前主题:",-1)),s("p",F,n(t(_).currentTheme),1)]),s("div",null,[e[12]||(e[12]=s("label",{class:"text-sm font-medium"},"未读通知:",-1)),s("p",R,n(t(_).unreadNotifications),1)])]),s("div",G,[o(t(u),{onClick:t(_).toggleSidebar,size:"sm"},{default:l(()=>e[13]||(e[13]=[r(" 切换侧边栏 ",-1)])),_:1,__:[13]},8,["onClick"]),o(t(u),{onClick:t(_).toggleTheme,size:"sm",variant:"outline"},{default:l(()=>e[14]||(e[14]=[r(" 切换主题 ",-1)])),_:1,__:[14]},8,["onClick"]),o(t(u),{onClick:C,size:"sm",variant:"secondary"},{default:l(()=>e[15]||(e[15]=[r(" 添加通知 ",-1)])),_:1,__:[15]})])]),_:1})]),_:1}),o(t(c),null,{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),null,{default:l(()=>e[16]||(e[16]=[r("业务数据状态",-1)])),_:1,__:[16]})]),_:1}),o(t(p),{class:"space-y-4"},{default:l(()=>[s("div",H,[s("div",null,[e[17]||(e[17]=s("label",{class:"text-sm font-medium"},"订单数量:",-1)),s("p",J,n(t(d).recentOrders.length),1)]),s("div",null,[e[18]||(e[18]=s("label",{class:"text-sm font-medium"},"库存项目:",-1)),s("p",K,n(t(d).stockItems.length),1)]),s("div",null,[e[19]||(e[19]=s("label",{class:"text-sm font-medium"},"低库存预警:",-1)),s("p",M,n(t(d).lowStockCount),1)]),s("div",null,[e[20]||(e[20]=s("label",{class:"text-sm font-medium"},"客户数量:",-1)),s("p",P,n(t(d).customers.length),1)])]),s("div",Q,[o(t(u),{onClick:e[0]||(e[0]=a=>t(d).loadRecentOrders()),size:"sm",disabled:t(d).isLoadingOrders},{default:l(()=>[r(n(t(d).isLoadingOrders?"加载中...":"加载订单"),1)]),_:1},8,["disabled"]),o(t(u),{onClick:e[1]||(e[1]=a=>t(d).loadStockItems()),size:"sm",variant:"outline",disabled:t(d).isLoadingStock},{default:l(()=>[r(n(t(d).isLoadingStock?"加载中...":"加载库存"),1)]),_:1},8,["disabled"]),o(t(u),{onClick:e[2]||(e[2]=a=>t(d).loadCustomers()),size:"sm",variant:"secondary",disabled:t(d).isLoadingCustomers},{default:l(()=>[r(n(t(d).isLoadingCustomers?"加载中...":"加载客户"),1)]),_:1},8,["disabled"])])]),_:1})]),_:1}),o(t(c),null,{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),null,{default:l(()=>e[21]||(e[21]=[r("订单状态统计",-1)])),_:1,__:[21]})]),_:1}),o(t(p),null,{default:l(()=>[s("div",W,[s("div",X,[s("p",Y,n(t(d).ordersByStatus.draft),1),e[22]||(e[22]=s("p",{class:"text-sm text-muted-foreground"},"草稿",-1))]),s("div",Z,[s("p",h,n(t(d).ordersByStatus.confirmed),1),e[23]||(e[23]=s("p",{class:"text-sm text-muted-foreground"},"已确认",-1))]),s("div",tt,[s("p",st,n(t(d).ordersByStatus.in_production),1),e[24]||(e[24]=s("p",{class:"text-sm text-muted-foreground"},"生产中",-1))]),s("div",et,[s("p",lt,n(t(d).ordersByStatus.completed),1),e[25]||(e[25]=s("p",{class:"text-sm text-muted-foreground"},"已完成",-1))]),s("div",ot,[s("p",dt,n(t(d).ordersByStatus.cancelled),1),e[26]||(e[26]=s("p",{class:"text-sm text-muted-foreground"},"已取消",-1))])])]),_:1})]),_:1}),t(d).lowStockItems.length>0?(m(),v(t(c),{key:0},{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),{class:"text-red-600"},{default:l(()=>e[27]||(e[27]=[r("低库存预警",-1)])),_:1,__:[27]})]),_:1}),o(t(p),null,{default:l(()=>[s("div",nt,[(m(!0),f(N,null,T(t(d).lowStockItems,a=>(m(),f("div",{key:a.id,class:"flex justify-between items-center p-3 bg-red-50 border border-red-200 rounded-md"},[s("div",null,[s("p",rt,n(a.materialName),1),s("p",it,"位置: "+n(a.location),1)]),s("div",at,[s("p",ut,n(a.quantity)+" "+n(a.unit),1),s("p",mt,"最低库存: "+n(a.minStock),1)])]))),128))])]),_:1})]),_:1})):b("",!0),k.value?(m(),v(t(c),{key:1},{default:l(()=>[o(t(x),null,{default:l(()=>[o(t(g),{class:"text-red-600"},{default:l(()=>e[28]||(e[28]=[r("错误信息",-1)])),_:1,__:[28]})]),_:1}),o(t(p),{class:"space-y-2"},{default:l(()=>[t(i).error?(m(),f("div",_t,[s("p",ft,"用户状态错误: "+n(t(i).error),1)])):b("",!0),t(d).ordersError?(m(),f("div",ct,[s("p",xt,"订单加载错误: "+n(t(d).ordersError),1)])):b("",!0),t(d).stockError?(m(),f("div",gt,[s("p",pt,"库存加载错误: "+n(t(d).stockError),1)])):b("",!0),o(t(u),{onClick:w,size:"sm",variant:"outline",class:"mt-2"},{default:l(()=>e[29]||(e[29]=[r(" 清除所有错误 ",-1)])),_:1,__:[29]})]),_:1})]),_:1})):b("",!0)]))}});export{yt as default};
