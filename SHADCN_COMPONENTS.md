# Shadcn Vue Components Setup

This document describes the Shadcn Vue component system configuration for the Glass ERP prototype.

## Configuration

### Components.json
The project is configured with:
- **Style**: New York
- **TypeScript**: Enabled
- **Base Color**: Neutral
- **CSS Variables**: Enabled
- **Icon Library**: Lucide

### Installed Components

The following UI components have been installed and are ready to use:

#### Core Components
- **Button** - Various button variants (default, secondary, destructive, outline, ghost, link)
- **Card** - Card container with header, content, and footer sections
- **Input** - Text input field
- **Label** - Form labels
- **Select** - Dropdown selection component
- **Textarea** - Multi-line text input
- **Table** - Data table with header, body, and footer

#### Form Components
- **Form** - Form wrapper with validation support (vee-validate + zod)
- **FormField** - Individual form field wrapper
- **FormItem** - Form item container
- **FormLabel** - Form field label
- **FormControl** - Form control wrapper
- **FormDescription** - Form field description
- **FormMessage** - Form validation message

## Usage Examples

### Simple Components (Without Validation)

```vue
<template>
  <div>
    <!-- Button -->
    <Button variant="default">Click me</Button>
    
    <!-- Card -->
    <Card>
      <CardHeader>
        <CardTitle>Title</CardTitle>
        <CardDescription>Description</CardDescription>
      </CardHeader>
      <CardContent>
        <p>Content</p>
      </CardContent>
    </Card>
    
    <!-- Simple Form -->
    <div class="space-y-4">
      <div>
        <Label for="name">Name</Label>
        <Input id="name" v-model="name" placeholder="Enter name" />
      </div>
      
      <div>
        <Label for="status">Status</Label>
        <Select v-model="status">
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'

const name = ref('')
const status = ref('')
</script>
```

### Form with Validation

```vue
<template>
  <Form :validation-schema="formSchema" @submit="onSubmit">
    <FormField v-slot="{ componentField }" name="email">
      <FormItem>
        <FormLabel>Email</FormLabel>
        <FormControl>
          <Input v-bind="componentField" type="email" placeholder="Enter email" />
        </FormControl>
        <FormDescription>We'll never share your email.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>
    
    <Button type="submit">Submit</Button>
  </Form>
</template>

<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

const formSchema = toTypedSchema(z.object({
  email: z.string().email('Invalid email address'),
}))

function onSubmit(values: any) {
  console.log('Form submitted:', values)
}
</script>
```

## Dependencies

The following dependencies were automatically installed:
- `reka-ui` - Underlying UI library
- `@tanstack/vue-table` - Table functionality
- `@vee-validate/zod` - Form validation with Zod
- `vee-validate` - Vue form validation
- `@vueuse/core` - Vue composition utilities
- `zod` - Schema validation

## Test Components

Two test components have been created to demonstrate usage:
- `ComponentsTest.vue` - Full form validation example
- `SimpleComponentsTest.vue` - Simple components without validation

## Theming

The components support both light and dark themes through CSS variables defined in `src/assets/main.css`. The theme can be toggled by adding/removing the `dark` class on the root element.

## Next Steps

The component system is now ready for use in the Glass ERP prototype. You can:
1. Import and use any of the installed components
2. Create custom components that extend the base components
3. Add additional Shadcn Vue components as needed using `pnpm dlx shadcn-vue@latest add <component-name>`