# Implementation Plan

- [x] 1. 项目基础架构搭建
  - 使用 pnpm + Vite 初始化 Vue 3 + TypeScript 项目
  - 配置 vite.config.ts 支持路径别名和 Tailwind CSS
  - 配置 tsconfig.json 支持路径别名
  - 安装核心依赖：vue-router、pinia、tailwindcss、shadcn-vue、lucide-vue-next
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 2. Shadcn Vue 组件系统配置
  - 初始化 shadcn-vue CLI 配置
  - 配置 components.json 文件
  - 配置 Tailwind CSS 全局样式文件
  - 安装基础 UI 组件（button、card、table、form 等）
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 3. 项目目录结构创建
  - 创建完整的项目目录结构（assets、components、router、store、types、utils、views）
  - 按功能模块划分 views 目录（crm、inventory、mes、procurement、quality）
  - 创建 components/layout 和 components/ui 目录结构
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 4. Mock 数据系统搭建
  - 创建 /public/mock/ 目录结构
  - 按业务模块创建 JSON 数据文件目录（common、metadata、crm、inventory、mes、procurement、quality）
  - 实现 utils/api.ts 中的 fetch 封装函数
  - 创建统一的数据加载接口和错误处理机制
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 7.1, 7.2, 7.3, 7.4_

- [x] 5. 玻璃行业物料变体数据模型设计
  - 创建 types/material-variant.ts 定义物料变体相关类型
  - 实现 MaterialTemplate、MaterialVariant、GlassSheetVariant、ProfileVariant 接口
  - 创建物料变体的基础属性和变体属性类型定义
  - 实现物料变体库存管理相关类型定义
  - _Requirements: 4.2, 4.3, 4.4_

- [x] 6. 玻璃行业示例数据创建
  - 创建 users.json 包含不同角色用户数据（销售工程师、生产调度员、质量工程师等）
  - 创建 materialCategory.json 包含玻璃行业物料分类数据
  - 创建 material.json 包含玻璃原片和型材的物料变体数据
  - 确保数据符合玻璃深加工行业特性和 MTO 模式需求
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 7. 核心布局组件开发
  - 实现 AppLayout.vue 主布局组件，集成 Shadcn Vue Sidebar
  - 实现 AppSidebar.vue 侧边栏组件，支持角色驱动的菜单显示
  - 实现 AppHeader.vue 顶部导航组件，包含用户信息和通知功能
  - 确保布局组件支持响应式设计和移动端适配
  - _Requirements: 8.1, 8.2, 8.3, 8.4_

- [ ] 8. 路由系统配置
  - 配置 router/index.ts 基础路由
  - 实现"仪表盘"、"元数据管理"、"客户关系"页面路由
  - 支持按功能模块扩展路由配置
  - 确保路由能够正确导航到对应页面组件
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 9. Pinia 状态管理配置
  - 配置 store/index.ts 基础状态管理
  - 实现用户状态管理（当前登录用户、角色权限）
  - 实现全局状态管理（当前选中的物料变体、订单状态等）
  - 支持状态持久化和跨组件数据共享
  - _Requirements: 8.1, 8.2, 8.3_

- [ ] 10. 物料变体管理组件开发
- [ ] 10.1 物料模板管理组件
  - 实现 MaterialTemplateManager.vue 组件
  - 支持配置物料的基础属性（厚度、颜色、等级）和变体属性（宽度、高度、长度）
  - 实现物料模板的创建、编辑、删除功能
  - _Requirements: 4.2, 4.3_

- [ ] 10.2 物料变体生成器组件
  - 实现 MaterialVariantGenerator.vue 组件
  - 支持基于物料模板批量生成物料变体
  - 实现变体的启用/禁用管理功能
  - _Requirements: 4.2, 4.3_

- [ ] 10.3 物料变体选择器组件
  - 实现 MaterialVariantSelector.vue 组件
  - 支持按属性筛选物料变体（厚度、颜色、尺寸等）
  - 显示物料变体的库存状态和价格信息
  - _Requirements: 4.2, 4.3_

- [ ] 11. MTO 核心业务组件开发
- [ ] 11.1 订单配置器组件
  - 实现 OrderConfigurator.vue 组件
  - 支持客户需求到产品规格的转换配置
  - 集成物料变体选择功能
  - 实现订单项的动态添加和编辑
  - _Requirements: 8.1, 8.4_

- [ ] 11.2 玻璃切割优化组件
  - 实现 GlassSheetOptimizer.vue 组件
  - 支持多物料变体组合的切割优化
  - 可视化展示切割方案和材料利用率
  - 实现不同优化算法的结果对比
  - _Requirements: 4.4_

- [ ] 11.3 型材开料计划组件
  - 实现 ProfileCuttingPlanner.vue 组件
  - 支持型材物料变体的开料优化
  - 展示开料方案和余料管理
  - _Requirements: 4.4_

- [ ] 12. 数据可视化组件开发
- [ ] 12.1 仪表盘卡片组件
  - 实现 DashboardCard.vue 组件
  - 支持 KPI 指标展示和数据可视化
  - 实现不同角色的定制化仪表盘视图
  - _Requirements: 8.1, 8.3_

- [ ] 12.2 图表组件
  - 实现 ChartWidget.vue 组件
  - 支持生产进度、库存状态、质量趋势等图表展示
  - 集成图表库实现数据可视化
  - _Requirements: 8.3_

- [ ] 13. 页面级组件开发
- [ ] 13.1 仪表盘页面
  - 实现 Dashboard.vue 页面组件
  - 集成仪表盘卡片和图表组件
  - 实现角色驱动的仪表盘内容展示
  - _Requirements: 6.2, 8.1, 8.3_

- [ ] 13.2 元数据管理页面
  - 实现元数据管理相关页面组件
  - 集成物料变体管理组件
  - 支持物料模板和变体的管理操作
  - _Requirements: 6.2, 4.2, 4.3_

- [ ] 13.3 客户关系管理页面
  - 实现 CRM 相关页面组件
  - 集成订单配置器和客户管理功能
  - 支持订单的创建、编辑、跟踪
  - _Requirements: 6.2, 8.4_

- [ ] 14. 原型演示功能完善
- [ ] 14.1 演示数据完善
  - 完善所有业务模块的演示数据
  - 确保数据间的逻辑一致性和业务关联性
  - 创建典型的业务场景演示数据
  - _Requirements: 4.1, 4.4_

- [ ] 14.2 用户角色切换功能
  - 实现用户角色切换演示功能
  - 展示不同角色的界面差异和权限控制
  - 实现角色驱动的菜单和功能展示
  - _Requirements: 8.1, 8.2_

- [ ] 15. 原型测试和优化
- [ ] 15.1 功能验证测试
  - 验证核心业务组件的基本功能
  - 测试物料变体管理的完整流程
  - 验证 MTO 业务流程的演示效果
  - _Requirements: 9.3_

- [ ] 15.2 用户体验优化
  - 优化组件的交互体验和视觉效果
  - 确保原型的演示流畅性和专业性
  - 完善错误处理和用户提示
  - _Requirements: 8.4, 9.1, 9.2_

- [ ] 16. 项目文档完善
  - 完善项目的技术文档和使用说明
  - 创建原型演示指南和业务场景说明
  - 整理项目的技术架构和实现细节文档
  - _Requirements: 9.6_