# Design Document

## Overview

本设计文档基于需求文档，为玻璃深加工企业ERP+MES系统的高保真原型提供详细的技术架构和实现方案。该原型专门针对MTO（Make-to-Order）模式的玻璃深加工企业，重点展示从客户订单到产品交付的完整业务流程。原型采用现代前端技术栈，通过数据驱动和可视化的方式展示核心业务场景，为开发工程师提供清晰的实现蓝图和业务理解。

### 原型核心目标
- **业务流程验证**：展示MTO模式下的订单驱动生产流程
- **行业特性展示**：体现玻璃深加工的工艺特点和管理需求
- **技术可行性**：验证现代前端技术栈在复杂业务场景中的应用
- **用户体验**：为不同角色用户提供直观的操作界面演示

## Architecture

### 技术架构

```mermaid
graph TB
    A[Browser] --> B[Vue 3 Application]
    B --> C[Vue Router]
    B --> D[Pinia Store]
    B --> E[Shadcn Vue Components]
    B --> F[API Layer]
    F --> G[Mock Data JSON Files]
    
    subgraph "Frontend Stack"
        B
        C
        D
        E
    end
    
    subgraph "Data Layer"
        F
        G
    end
    
    subgraph "UI Framework"
        E --> H[Tailwind CSS]
        E --> I[Reka UI]
        E --> J[Lucide Icons]
    end
```

### 项目结构设计

基于需求文档中的目录结构规划，采用模块化的组织方式：

```
src/
├── assets/                 # 静态资源
│   ├── images/             # 图片资源
│   ├── icons/              # 图标资源
│   └── styles/             # 全局样式
├── components/             # 全局共享组件
│   ├── layout/             # 布局组件
│   │   ├── AppHeader.vue   # 顶部导航
│   │   ├── AppSidebar.vue  # 侧边栏
│   │   └── AppLayout.vue   # 主布局
│   └── ui/                 # Shadcn Vue 组件
├── composables/            # Vue 组合式函数
├── router/                 # 路由配置
├── store/                  # Pinia 状态管理
├── types/                  # TypeScript 类型定义
├── utils/                  # 工具函数
├── views/                  # 页面级组件
│   ├── Dashboard.vue       # 仪表盘
│   ├── crm/               # 客户关系管理
│   ├── inventory/         # 库存管理
│   ├── mes/               # 生产执行系统
│   ├── procurement/       # 采购管理
│   └── quality/           # 质量管理
├── App.vue
└── main.ts
```

## Components and Interfaces

### 核心组件设计

#### 1. 布局组件系统

**AppLayout.vue** - 主布局组件
- 集成 Shadcn Vue 的 Sidebar 组件
- 响应式设计，支持移动端适配
- 角色驱动的导航菜单显示

**AppSidebar.vue** - 侧边栏组件
- 基于用户角色动态生成菜单
- 支持多级菜单结构
- 集成搜索功能

**AppHeader.vue** - 顶部导航组件
- 用户信息显示
- 通知中心
- 主题切换功能

#### 2. 业务组件系统

**MTO 核心业务组件**
- `OrderConfigurator.vue` - 订单配置器，支持客户需求到产品规格的转换
- `ProcessRouteDesigner.vue` - 工艺路线设计器，动态生成加工流程
- `GlassCuttingOptimizer.vue` - 玻璃切割优化可视化组件
- `ProductionScheduler.vue` - 生产排程甘特图组件

**物料变体管理组件**
- `MaterialTemplateManager.vue` - 物料模板管理，配置基础属性和变体属性
- `MaterialVariantGenerator.vue` - 批量生成物料变体的工具
- `MaterialVariantSelector.vue` - 智能物料变体选择器，支持按属性筛选和库存状态显示
- `MaterialVariantInventoryView.vue` - 按物料变体展示库存状态和预警信息

**玻璃行业特色组件**
- `GlassSheetOptimizer.vue` - 玻璃原片切割优化器，支持多变体组合优化
- `ProfileCuttingPlanner.vue` - 型材开料计划器，优化型材长度利用率
- `MaterialVariantCalculator.vue` - 基于变体的原材料用量精确计算器
- `WasteVariantTracker.vue` - 余料变体管理，支持余料的重新利用匹配
- `QualityInspectorByVariant.vue` - 按变体记录质量检测数据

**通用业务组件**
- `DashboardCard.vue` - 仪表盘卡片组件
- `ChartWidget.vue` - 图表组件（生产进度、库存状态等）
- `DataTable.vue` - 基于 TanStack Vue Table 的数据表格
- `FormBuilder.vue` - 动态表单构建器

### 接口设计

#### API 接口层

```typescript
// utils/api.ts
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

interface ApiClient {
  get<T>(url: string): Promise<ApiResponse<T>>;
  post<T>(url: string, data: any): Promise<ApiResponse<T>>;
  put<T>(url: string, data: any): Promise<ApiResponse<T>>;
  delete<T>(url: string): Promise<ApiResponse<T>>;
}

// Mock API 实现
class MockApiClient implements ApiClient {
  async get<T>(url: string): Promise<ApiResponse<T>> {
    const response = await fetch(`/mock${url}.json`);
    const data = await response.json();
    return { data, success: true };
  }
  // ... 其他方法实现
}
```

#### 组件接口规范

```typescript
// types/components.ts
interface BaseComponent {
  id: string;
  className?: string;
  disabled?: boolean;
}

interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: any) => VNode;
}

interface FormField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'date' | 'number';
  required?: boolean;
  validation?: ValidationRule[];
  options?: SelectOption[];
}
```

## Data Models

### 基于 ODOO 的数据模型设计

参考 ODOO 系统的数据结构，结合玻璃深加工行业特性：

#### 1. 用户和权限模型

```typescript
// types/user.ts
interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  avatar?: string;
  roles: Role[];
  department: Department;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Role {
  id: string;
  name: string;
  code: string;
  permissions: Permission[];
  description?: string;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
}
```

#### 2. 物料变体管理模型

基于玻璃深加工企业的特性，采用物料模板+变体的管理模式：

```typescript
// types/material-variant.ts
interface MaterialTemplate {
  id: string;
  name: string; // "浮法玻璃"
  code: string; // "GLASS_FLOAT"
  category: MaterialCategory;
  materialType: 'raw_glass' | 'profile' | 'hardware' | 'sealant' | 'chemical';
  baseAttributes: BaseAttribute[]; // 基础规格：厚度、颜色、等级
  variantAttributes: VariantAttribute[]; // 变体规格：宽度、高度、长度
  variants: ProductVariant[]; // 具体的变体列表
  isActive: boolean;
  createdAt: string;
}

interface BaseAttribute {
  id: string;
  name: string; // "厚度"、"颜色"、"等级"
  type: 'number' | 'text' | 'select';
  unit?: string; // "mm"
  options?: string[]; // ["透明", "茶色", "蓝色"]
  isRequired: boolean;
}

interface VariantAttribute {
  id: string;
  name: string; // "宽度"、"高度"、"长度"
  type: 'number';
  unit: string; // "mm"
  minValue?: number;
  maxValue?: number;
  isRequired: boolean;
}

interface MaterialVariant {
  id: string;
  templateId: string;
  sku: string; // "GLASS_FLOAT_6MM_CLEAR_3300x2140"
  displayName: string; // "6mm透明浮法玻璃 3300x2140mm"
  baseAttributeValues: AttributeValue[]; // 厚度:6mm, 颜色:透明, 等级:优等品
  variantAttributeValues: AttributeValue[]; // 宽度:3300mm, 高度:2140mm
  cost: number;
  weight: number;
  area: number; // 对于玻璃，面积是重要属性
  supplier: Supplier;
  leadTime: number;
  isActive: boolean;
  stockQuantity: number;
  reservedQuantity: number;
  availableQuantity: number;
}

interface AttributeValue {
  attributeId: string;
  attributeName: string;
  value: string | number;
  unit?: string;
}

// 玻璃原片物料的具体实现
interface GlassSheetVariant extends MaterialVariant {
  thickness: number; // mm
  color: string; // "透明"、"茶色"、"蓝色"
  grade: string; // "优等品"、"一等品"
  width: number; // mm
  height: number; // mm
  glassType: 'float' | 'ultra_clear' | 'tinted' | 'reflective';
  surfaceQuality: 'standard' | 'premium';
}

// 型材物料的具体实现
interface ProfileVariant extends MaterialVariant {
  crossSection: string; // "50x30mm"
  material: string; // "铝合金"、"塑钢"
  color: string; // "银白色"、"香槟色"
  length: number; // mm
  wallThickness: number; // mm
  surfaceTreatment: 'anodized' | 'powder_coated' | 'electrophoresis';
}
```

#### 3. 基于变体的库存管理模型

```typescript
// types/variant-inventory.ts
interface MaterialVariantStock {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  locationId: string;
  location: StockLocation;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  unitCost: number;
  totalValue: number;
  lastMovementDate: string;
  reorderPoint: number; // 物料变体级别的安全库存
  maxStock: number; // 物料变体级别的最大库存
  lotNumbers: string[]; // 批次号
  expiryDate?: string; // 过期日期（如适用）
}

interface MaterialVariantStockMove {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  quantity: number;
  sourceLocationId: string;
  destinationLocationId: string;
  moveType: 'receipt' | 'delivery' | 'internal' | 'adjustment' | 'scrap';
  reference: string;
  state: 'draft' | 'confirmed' | 'done' | 'cancelled';
  unitCost: number;
  totalCost: number;
  scheduledDate: string;
  effectiveDate?: string;
  relatedOrderId?: string;
  cuttingPlanId?: string; // 关联的切割计划
}

interface WasteMaterialVariantStock {
  id: string;
  originalMaterialVariantId: string;
  originalMaterialVariant: MaterialVariant;
  currentDimensions: Dimensions;
  remainingArea: number; // 对于玻璃
  remainingLength: number; // 对于型材
  quality: 'good' | 'damaged' | 'unusable';
  locationId: string;
  createdDate: string;
  lastUsedDate?: string;
  potentialUses: PotentialUse[]; // 可能的用途匹配
}

interface PotentialUse {
  orderItemId: string;
  requiredDimensions: Dimensions;
  matchScore: number; // 匹配度评分
  wasteAfterUse: number;
}

interface MaterialVariantReorderRule {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  minQuantity: number;
  maxQuantity: number;
  reorderQuantity: number;
  leadTime: number;
  supplierId: string;
  isActive: boolean;
  seasonalAdjustment?: SeasonalAdjustment[];
}

interface SeasonalAdjustment {
  month: number;
  adjustmentFactor: number; // 季节性调整系数
}

// 变体库存预警
interface MaterialVariantStockAlert {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  alertType: 'low_stock' | 'overstock' | 'no_stock' | 'expiring' | 'slow_moving';
  currentQuantity: number;
  thresholdQuantity: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  createdDate: string;
  isResolved: boolean;
  resolvedDate?: string;
}
```

#### 4. MTO生产管理模型

```typescript
// types/mto-manufacturing.ts
interface CustomerOrder {
  id: string;
  customerName: string;
  orderDate: string;
  deliveryDate: string;
  items: OrderItem[];
  status: 'draft' | 'confirmed' | 'in_production' | 'completed' | 'delivered';
  totalValue: number;
  specialRequirements?: string;
}

interface OrderItem {
  id: string;
  productType: 'window' | 'door' | 'curtain_wall' | 'partition' | 'custom';
  specifications: GlassSpecification;
  quantity: number;
  unitPrice: number;
  processRoute: ProcessRoute;
  materialRequirements: MaterialRequirement[];
}

interface GlassSpecification {
  width: number;
  height: number;
  thickness: number;
  glassType: 'float' | 'tempered' | 'laminated' | 'insulated' | 'coated';
  edgeWork: 'polished' | 'ground' | 'beveled';
  holes?: HoleSpecification[];
  coating?: CoatingSpecification;
  specialProcessing?: string[];
}

interface ProcessRoute {
  id: string;
  name: string;
  steps: ProcessStep[];
  estimatedDuration: number;
  qualityCheckpoints: QualityCheckpoint[];
}

interface ProcessStep {
  id: string;
  name: string;
  workCenter: string;
  operation: 'cutting' | 'tempering' | 'laminating' | 'coating' | 'drilling' | 'polishing';
  duration: number;
  setupTime: number;
  parameters: Record<string, any>;
  nextSteps: string[];
}

interface MaterialVariantOptimizationInput {
  requiredPieces: RequiredPiece[]; // 客户订单需要的玻璃片
  availableMaterialVariants: GlassSheetVariant[]; // 可用的玻璃原片物料变体
  optimizationGoal: 'minimize_waste' | 'minimize_cost' | 'minimize_variants' | 'balanced';
  constraints: OptimizationConstraints;
}

interface RequiredPiece {
  id: string;
  width: number;
  height: number;
  thickness: number;
  color: string;
  grade: string;
  quantity: number;
  orderItemId: string;
  allowRotation: boolean;
}

interface OptimizationConstraints {
  maxVariantsToUse: number; // 最多使用的变体数量
  minUtilizationRate: number; // 最低利用率要求
  preferredVariants: string[]; // 优先使用的变体ID
  excludedVariants: string[]; // 排除的变体ID
}

interface MaterialVariantOptimizationResult {
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  overallUtilizationRate: number;
  variantCuttingPlans: VariantCuttingPlan[];
  alternativeOptions: AlternativeOption[];
}

interface SelectedMaterialVariant {
  materialVariantId: string;
  materialVariant: GlassSheetVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteArea: number;
}

interface MaterialVariantCuttingPlan {
  materialVariantId: string;
  sheetIndex: number; // 第几张原片
  pieces: CuttingPiece[];
  wasteAreas: WasteArea[];
}

interface CuttingPiece {
  pieceId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  orderItemId: string;
  requiredPieceId: string;
}

interface WasteArea {
  x: number;
  y: number;
  width: number;
  height: number;
  area: number;
  isReusable: boolean; // 是否可作为余料重复利用
}

// 型材切割优化
interface ProfileMaterialOptimizationInput {
  requiredLengths: RequiredLength[];
  availableProfileMaterialVariants: ProfileVariant[];
  optimizationGoal: 'minimize_waste' | 'minimize_cost';
}

interface RequiredLength {
  id: string;
  length: number;
  quantity: number;
  orderItemId: string;
  profileTemplateId: string;
}

interface ProfileMaterialOptimizationResult {
  selectedProfileMaterialVariants: SelectedProfileMaterialVariant[];
  cuttingPlans: ProfileCuttingPlan[];
  totalWasteLength: number;
  utilizationRate: number;
}

interface ProfileMaterialCuttingPlan {
  materialVariantId: string;
  profileIndex: number;
  cuts: ProfileCut[];
  wasteLength: number;
}

interface ProfileCut {
  cutId: string;
  startPosition: number;
  length: number;
  orderItemId: string;
  requiredLengthId: string;
}
```

## Error Handling

### 错误处理策略

#### 1. API 错误处理

```typescript
// utils/errorHandler.ts
interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

class ErrorHandler {
  static handleApiError(error: ApiError): void {
    switch (error.code) {
      case 'NETWORK_ERROR':
        this.showNetworkError();
        break;
      case 'VALIDATION_ERROR':
        this.showValidationError(error.details);
        break;
      case 'PERMISSION_DENIED':
        this.showPermissionError();
        break;
      default:
        this.showGenericError(error.message);
    }
  }

  static showNetworkError(): void {
    // 显示网络错误提示
  }

  static showValidationError(details: any): void {
    // 显示表单验证错误
  }
}
```

#### 2. 组件错误边界

```typescript
// components/ErrorBoundary.vue
interface ErrorInfo {
  componentName: string;
  errorMessage: string;
  stackTrace: string;
  timestamp: string;
}

// 使用 Vue 3 的 errorHandler 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('Global error:', error);
  // 发送错误报告到监控系统
  ErrorReporter.report({
    error,
    component: instance?.$options.name,
    info
  });
};
```

#### 3. 用户友好的错误提示

```typescript
// composables/useNotification.ts
export function useNotification() {
  const showError = (message: string, details?: string) => {
    // 使用 Shadcn Vue 的 Toast 组件显示错误
  };

  const showSuccess = (message: string) => {
    // 显示成功提示
  };

  const showWarning = (message: string) => {
    // 显示警告提示
  };

  return {
    showError,
    showSuccess,
    showWarning
  };
}
```

## Testing Strategy

### 原型验证策略

作为高保真原型项目，测试重点关注功能演示和业务流程验证，而非完整的质量保证。

#### 1. 功能验证测试

重点验证核心业务组件的基本功能：

```typescript
// tests/prototype/order-configurator.test.ts
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import OrderConfigurator from '@/components/OrderConfigurator.vue';

describe('OrderConfigurator Prototype', () => {
  it('displays glass specification form', () => {
    const wrapper = mount(OrderConfigurator);
    expect(wrapper.find('[data-testid="glass-spec-form"]').exists()).toBe(true);
  });

  it('calculates material requirements', async () => {
    const wrapper = mount(OrderConfigurator);
    await wrapper.find('[data-testid="calculate-btn"]').trigger('click');
    expect(wrapper.find('[data-testid="material-list"]').exists()).toBe(true);
  });
});
```

#### 2. 业务流程验证

验证关键业务场景的完整流程：

```typescript
// tests/prototype/mto-workflow.test.ts
import { test, expect } from '@playwright/test';

test('MTO workflow demonstration', async ({ page }) => {
  await page.goto('/');
  
  // 1. 订单配置
  await page.click('[data-testid="new-order"]');
  await page.fill('[data-testid="glass-width"]', '1200');
  await page.fill('[data-testid="glass-height"]', '800');
  await page.selectOption('[data-testid="glass-type"]', 'tempered');
  
  // 2. 工艺路线生成
  await page.click('[data-testid="generate-route"]');
  await expect(page.locator('[data-testid="process-route"]')).toBeVisible();
  
  // 3. 切割优化
  await page.click('[data-testid="optimize-cutting"]');
  await expect(page.locator('[data-testid="cutting-plan"]')).toBeVisible();
  
  // 4. 生产排程
  await page.click('[data-testid="schedule-production"]');
  await expect(page.locator('[data-testid="gantt-chart"]')).toBeVisible();
});
```

#### 3. 演示数据验证

确保演示数据的完整性和一致性：

```typescript
// tests/prototype/demo-data.test.ts
import { describe, it, expect } from 'vitest';
import { loadMockData } from '@/utils/api';

describe('Demo Data Validation', () => {
  it('loads complete glass industry data', async () => {
    const materials = await loadMockData('/materials');
    expect(materials.data).toHaveLength(expect.any(Number));
    expect(materials.data[0]).toHaveProperty('glassType');
    expect(materials.data[0]).toHaveProperty('thickness');
  });

  it('provides realistic order scenarios', async () => {
    const orders = await loadMockData('/orders');
    expect(orders.data).toContainEqual(
      expect.objectContaining({
        productType: expect.stringMatching(/window|door|curtain_wall/),
        specifications: expect.objectContaining({
          width: expect.any(Number),
          height: expect.any(Number)
        })
      })
    );
  });
});
```

### 原型演示场景

#### 核心演示流程

1. **角色切换演示**：展示不同用户角色的界面差异
2. **订单配置演示**：从客户需求到产品规格的转换过程
3. **工艺优化演示**：切割方案和工艺路线的智能生成
4. **生产监控演示**：实时生产进度和质量状态展示
5. **异常处理演示**：订单变更和生产异常的处理流程

#### 演示数据设计原则

- **真实性**：基于实际玻璃深加工企业的业务场景
- **完整性**：覆盖从订单到交付的完整业务链条
- **多样性**：包含不同类型的产品和工艺要求
- **关联性**：数据间保持逻辑一致性和业务关联性

## 原型特色设计

### MTO业务流程可视化

#### 订单驱动的产品配置流程
```mermaid
graph LR
    A[客户需求] --> B[规格配置]
    B --> C[工艺选择]
    C --> D[材料计算]
    D --> E[成本估算]
    E --> F[交期确认]
    F --> G[订单确认]
    G --> H[生产排程]
```

#### 玻璃深加工工艺流程
```mermaid
graph TB
    A[原片玻璃] --> B[切割优化]
    B --> C[边部处理]
    C --> D{工艺分支}
    D -->|钢化| E[钢化炉]
    D -->|夹胶| F[夹胶线]
    D -->|中空| G[中空线]
    E --> H[质量检测]
    F --> H
    G --> H
    H --> I[包装入库]
```

### 关键演示场景设计

#### 场景1：多变体玻璃原片切割优化
- **背景**：某商业大厦幕墙项目，需要1000+块不同规格玻璃，包含6mm、8mm、12mm等不同厚度
- **演示重点**：
  - 智能选择最优的原片变体组合（3300x2140、3660x2440等规格）
  - 多变体并行切割优化算法
  - 实时对比不同变体组合的成本和浪费率
- **技术亮点**：变体组合算法可视化、材料利用率实时计算

#### 场景2：型材变体开料优化
- **背景**：定制化门窗项目，需要多种长度的铝合金型材
- **演示重点**：
  - 从6000mm、4000mm等不同长度的型材变体中选择最优组合
  - 开料方案的智能生成和优化
  - 余料长度的最小化处理
- **技术亮点**：一维切割优化算法、余料重复利用匹配

#### 场景3：变体库存智能补货
- **背景**：基于历史订单分析，预测不同变体的需求量
- **演示重点**：
  - 按变体分析库存周转率和安全库存
  - 智能推荐补货的变体规格和数量
  - 季节性需求的变体库存调整
- **技术亮点**：变体需求预测算法、库存优化建议

#### 场景4：余料变体再利用匹配
- **背景**：利用历史切割产生的余料变体满足新订单需求
- **演示重点**：
  - 余料变体的智能匹配和推荐
  - 余料使用后的二次余料管理
  - 成本效益分析和环保效益展示
- **技术亮点**：余料匹配算法、循环利用追踪

### 用户角色体验设计

#### 销售工程师视角
- **核心功能**：订单配置器、报价计算器、交期查询
- **界面特点**：简洁直观、快速响应、移动端适配
- **数据展示**：客户历史、产品目录、价格体系

#### 生产调度员视角
- **核心功能**：生产排程、资源分配、进度监控
- **界面特点**：甘特图、看板视图、实时更新
- **数据展示**：设备状态、人员安排、物料准备

#### 质量工程师视角
- **核心功能**：检测标准、质量记录、异常追踪
- **界面特点**：检测流程、数据录入、报告生成
- **数据展示**：质量趋势、缺陷分析、改进建议

### 技术创新展示

#### 变体管理智能算法可视化
- **多变体切割优化**：实时展示不同变体组合的切割方案对比，包括成本、浪费率、利用率等关键指标
- **变体选择算法**：动态展示系统如何从众多变体中选择最优组合的决策过程
- **余料变体匹配**：可视化展示余料变体与新需求的智能匹配过程和匹配度评分
- **变体库存优化**：展示基于历史数据和需求预测的变体库存优化建议

#### 实时数据流
- **生产监控**：模拟实时设备数据和生产状态
- **库存变化**：动态展示物料消耗和库存变化
- **订单状态**：实时更新订单进度和交付状态

## 物料变体管理技术实现

### 物料变体数据结构设计

#### 玻璃原片物料变体示例
```json
{
  "template": {
    "id": "TPL_GLASS_FLOAT",
    "name": "浮法玻璃",
    "materialType": "raw_glass",
    "baseAttributes": [
      {"name": "厚度", "type": "select", "options": ["4mm", "5mm", "6mm", "8mm", "10mm", "12mm"]},
      {"name": "颜色", "type": "select", "options": ["透明", "茶色", "蓝色", "绿色", "灰色"]},
      {"name": "等级", "type": "select", "options": ["优等品", "一等品", "合格品"]}
    ],
    "variantAttributes": [
      {"name": "宽度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 3660},
      {"name": "高度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 2440}
    ]
  },
  "variants": [
    {
      "id": "VAR_GLASS_6MM_CLEAR_3300x2140",
      "sku": "GLASS_FLOAT_6MM_CLEAR_3300x2140",
      "displayName": "6mm透明浮法玻璃 3300x2140mm",
      "baseValues": [
        {"attribute": "厚度", "value": "6mm"},
        {"attribute": "颜色", "value": "透明"},
        {"attribute": "等级", "value": "优等品"}
      ],
      "variantValues": [
        {"attribute": "宽度", "value": 3300, "unit": "mm"},
        {"attribute": "高度", "value": 2140, "unit": "mm"}
      ],
      "cost": 85.50,
      "weight": 33.8,
      "area": 7.062,
      "stockQuantity": 120,
      "reorderPoint": 20
    }
  ]
}
```

#### 型材物料变体示例
```json
{
  "template": {
    "id": "TPL_PROFILE_ALU",
    "name": "铝合金型材",
    "materialType": "profile",
    "baseAttributes": [
      {"name": "截面", "type": "select", "options": ["50x30mm", "60x40mm", "80x50mm"]},
      {"name": "颜色", "type": "select", "options": ["银白色", "香槟色", "古铜色"]},
      {"name": "壁厚", "type": "select", "options": ["1.2mm", "1.4mm", "1.6mm", "2.0mm"]}
    ],
    "variantAttributes": [
      {"name": "长度", "type": "number", "unit": "mm", "minValue": 1000, "maxValue": 6000}
    ]
  },
  "variants": [
    {
      "id": "VAR_PROFILE_50x30_SILVER_6000",
      "sku": "PROFILE_ALU_50x30_SILVER_6000",
      "displayName": "50x30mm银白色铝型材 6000mm长",
      "baseValues": [
        {"attribute": "截面", "value": "50x30mm"},
        {"attribute": "颜色", "value": "银白色"},
        {"attribute": "壁厚", "value": "1.4mm"}
      ],
      "variantValues": [
        {"attribute": "长度", "value": 6000, "unit": "mm"}
      ],
      "cost": 28.50,
      "weight": 2.85,
      "stockQuantity": 500,
      "reorderPoint": 50
    }
  ]
}
```

### 物料变体优化算法核心逻辑

#### 玻璃切割物料变体选择算法
```typescript
class GlassMaterialVariantOptimizer {
  optimize(input: MaterialVariantOptimizationInput): MaterialVariantOptimizationResult {
    // 1. 按需求分组（相同厚度、颜色、等级）
    const groupedRequirements = this.groupRequirementsBySpecs(input.requiredPieces);
    
    // 2. 为每组需求筛选合适的物料变体
    const suitableMaterialVariants = this.filterSuitableMaterialVariants(
      groupedRequirements, 
      input.availableMaterialVariants
    );
    
    // 3. 生成物料变体组合方案
    const combinations = this.generateMaterialVariantCombinations(
      groupedRequirements, 
      suitableMaterialVariants
    );
    
    // 4. 评估每种组合方案
    const evaluatedCombinations = combinations.map(combo => 
      this.evaluateCombination(combo, input.optimizationGoal)
    );
    
    // 5. 选择最优方案
    return this.selectBestCombination(evaluatedCombinations);
  }

  private evaluateCombination(combination: MaterialVariantCombination, goal: string): EvaluationResult {
    const cuttingPlans = combination.materialVariants.map(materialVariant => 
      this.generateCuttingPlan(materialVariant, combination.requirements)
    );
    
    return {
      totalCost: this.calculateTotalCost(cuttingPlans),
      totalWaste: this.calculateTotalWaste(cuttingPlans),
      utilizationRate: this.calculateUtilizationRate(cuttingPlans),
      materialVariantCount: combination.materialVariants.length,
      score: this.calculateScore(cuttingPlans, goal)
    };
  }
}
```

### 物料变体库存管理策略

#### 智能补货算法
```typescript
class MaterialVariantReplenishmentManager {
  generateReplenishmentPlan(materialVariants: MaterialVariant[]): ReplenishmentPlan[] {
    return materialVariants.map(materialVariant => {
      const demandForecast = this.forecastDemand(materialVariant);
      const currentStock = materialVariant.stockQuantity;
      const safetyStock = this.calculateSafetyStock(materialVariant);
      const optimalOrderQuantity = this.calculateEOQ(materialVariant);
      
      if (currentStock <= materialVariant.reorderPoint) {
        return {
          materialVariantId: materialVariant.id,
          recommendedQuantity: optimalOrderQuantity,
          urgency: this.calculateUrgency(currentStock, demandForecast),
          expectedDelivery: this.calculateDeliveryDate(materialVariant.leadTime),
          costImpact: optimalOrderQuantity * materialVariant.cost
        };
      }
      
      return null;
    }).filter(plan => plan !== null);
  }
}
```

这个优化后的设计文档完整体现了玻璃深加工企业基于**物料变体**的管理特性，包括：

1. **完整的物料变体数据模型**：支持基础属性和变体属性的分离管理，区分物料和产品的概念
2. **智能的物料变体选择算法**：针对玻璃原片和型材物料的切割优化
3. **精细的物料变体库存管理**：支持物料变体级别的库存控制和补货策略
4. **实用的演示场景**：展示物料变体管理在MTO生产模式中的应用价值

这样的设计能够为开发工程师提供清晰的技术实现指导，准确体现了玻璃深加工企业中**物料**（原材料）采用变体管理、**产品**（成品）按订单定制的核心业务特性。